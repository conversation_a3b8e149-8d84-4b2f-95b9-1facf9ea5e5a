import { BadRequestException } from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';

/**
 * File upload interface for validation
 */
interface FileUpload {
  originalname: string;
  size: number;
  mimetype: string;
}

/**
 * Validation utility functions for common validation tasks
 */
export class ValidationUtil {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    // More strict email validation
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    // Additional checks for common invalid patterns
    if (email.includes('..') || email.startsWith('.') || email.endsWith('.')) {
      return false;
    }

    return emailRegex.test(email);
  }

  /**
   * Validate phone number format (international)
   */
  static isValidPhoneNumber(phone: string): boolean {
    // Remove spaces, dashes, and parentheses for validation
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

    // Must start with + and have 7-15 digits total (including country code)
    const phoneRegex = /^\+[1-9]\d{6,14}$/;

    // Additional check for minimum length
    if (cleanPhone.length < 8) {
      return false;
    }

    return phoneRegex.test(cleanPhone);
  }

  /**
   * Validate password strength
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    errors: string[];
    score: number;
  } {
    const errors: string[] = [];
    let score = 0;

    // Minimum length
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    } else {
      score += 1;
    }

    // Maximum length
    if (password.length > 128) {
      errors.push('Password must not exceed 128 characters');
    }

    // Contains lowercase
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      score += 1;
    }

    // Contains uppercase
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      score += 1;
    }

    // Contains number
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else {
      score += 1;
    }

    // Contains special character
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else {
      score += 1;
    }

    // Check for common patterns
    const commonPatterns = [
      /(.)\1{2,}/, // Repeated characters
      /123456|654321|abcdef|qwerty|password/i, // Common sequences
    ];

    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        errors.push('Password contains common patterns and is not secure');
        score = Math.max(0, score - 1);
        break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      score: Math.min(5, score),
    };
  }

  /**
   * Validate UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Validate tenant ID format
   */
  static isValidTenantId(tenantId: string): boolean {
    // Tenant ID should be alphanumeric with hyphens and underscores, 3-50 characters
    const tenantRegex = /^[a-zA-Z0-9_-]{3,50}$/;
    return tenantRegex.test(tenantId);
  }

  /**
   * Validate employee ID format
   */
  static isValidEmployeeId(employeeId: string): boolean {
    // Employee ID should be alphanumeric, 3-20 characters
    const employeeRegex = /^[a-zA-Z0-9]{3,20}$/;
    return employeeRegex.test(employeeId);
  }

  /**
   * Validate date string format (ISO 8601)
   */
  static isValidDateString(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && dateString === date.toISOString().split('T')[0];
  }

  /**
   * Validate date range
   */
  static isValidDateRange(startDate: string, endDate: string): boolean {
    if (!this.isValidDateString(startDate) || !this.isValidDateString(endDate)) {
      return false;
    }
    return new Date(startDate) <= new Date(endDate);
  }

  /**
   * Validate currency code (ISO 4217)
   */
  static isValidCurrencyCode(currency: string): boolean {
    const validCurrencies = [
      'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
      'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'ZAR', 'BRL', 'INR', 'KRW', 'PLN',
    ];
    return validCurrencies.includes(currency.toUpperCase());
  }

  /**
   * Validate salary amount
   */
  static isValidSalaryAmount(amount: number, currency = 'USD'): boolean {
    if (amount < 0) return false;
    
    // Set reasonable limits based on currency
    const limits = {
      USD: { min: 0, max: 10000000 }, // $10M
      EUR: { min: 0, max: 10000000 },
      GBP: { min: 0, max: 8000000 },
      JPY: { min: 0, max: 1000000000 }, // 1B Yen
      INR: { min: 0, max: 750000000 }, // 75 Crore
    };

    const limit = limits[currency] || limits.USD;
    return amount >= limit.min && amount <= limit.max;
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate and transform DTO using class-validator
   */
  static async validateAndTransform<T>(
    dtoClass: new () => T,
    data: any,
  ): Promise<T> {
    const dto = plainToClass(dtoClass, data);
    const errors = await validate(dto as any);

    if (errors.length > 0) {
      const errorMessages = this.formatValidationErrors(errors);
      throw new BadRequestException({
        message: 'Validation failed',
        errors: errorMessages,
      });
    }

    return dto;
  }

  /**
   * Format validation errors for consistent error responses
   */
  static formatValidationErrors(errors: ValidationError[]): string[] {
    const messages: string[] = [];

    for (const error of errors) {
      if (error.constraints) {
        messages.push(...Object.values(error.constraints));
      }

      // Handle nested validation errors
      if (error.children && error.children.length > 0) {
        const childMessages = this.formatValidationErrors(error.children);
        messages.push(...childMessages.map(msg => `${error.property}.${msg}`));
      }
    }

    return messages;
  }

  /**
   * Validate file upload
   */
  static validateFileUpload(
    file: FileUpload,
    options: {
      maxSize?: number; // in bytes
      allowedMimeTypes?: string[];
      allowedExtensions?: string[];
    } = {},
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
      allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf'],
    } = options;

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`);
    }

    // Check MIME type
    if (!allowedMimeTypes.includes(file.mimetype)) {
      errors.push(`File type ${file.mimetype} is not allowed`);
    }

    // Check file extension
    const extension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    if (!allowedExtensions.includes(extension)) {
      errors.push(`File extension ${extension} is not allowed`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate JSON structure
   */
  static isValidJSON(jsonString: string): boolean {
    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate URL format
   */
  static isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate IP address (IPv4 and IPv6)
   */
  static isValidIPAddress(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Validate time format (HH:MM or HH:MM:SS)
   */
  static isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
    return timeRegex.test(time);
  }

  /**
   * Validate timezone
   */
  static isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }
}
