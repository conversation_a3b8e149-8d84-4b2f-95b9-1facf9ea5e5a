/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ([
/* 0 */,
/* 1 */
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),
/* 2 */
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),
/* 3 */
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),
/* 4 */
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),
/* 5 */
/***/ ((module) => {

module.exports = require("helmet");

/***/ }),
/* 6 */
/***/ ((module) => {

module.exports = require("compression");

/***/ }),
/* 7 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(8);
const cache_manager_1 = __webpack_require__(9);
const throttler_1 = __webpack_require__(10);
const graphql_1 = __webpack_require__(11);
const apollo_1 = __webpack_require__(12);
const nest_winston_1 = __webpack_require__(13);
const redisStore = __webpack_require__(14);
const database_config_1 = __webpack_require__(15);
const redis_config_1 = __webpack_require__(16);
const jwt_config_1 = __webpack_require__(17);
const winston_config_1 = __webpack_require__(18);
const database_module_1 = __webpack_require__(20);
const common_module_1 = __webpack_require__(35);
const auth_module_1 = __webpack_require__(52);
const user_module_1 = __webpack_require__(68);
const rbac_module_1 = __webpack_require__(71);
const tenant_module_1 = __webpack_require__(83);
const health_module_1 = __webpack_require__(84);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [database_config_1.databaseConfig, redis_config_1.redisConfig, jwt_config_1.jwtConfig],
                envFilePath: ['.env.local', '.env'],
                validationSchema: null,
            }),
            nest_winston_1.WinstonModule.forRootAsync({
                useFactory: () => (0, winston_config_1.createWinstonConfig)(),
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    type: 'postgres',
                    host: configService.get('database.host'),
                    port: configService.get('database.port'),
                    username: configService.get('database.username'),
                    password: configService.get('database.password'),
                    database: configService.get('database.name'),
                    entities: [__dirname + '/**/*.entity{.ts,.js}'],
                    migrations: [__dirname + '/database/migrations/*{.ts,.js}'],
                    synchronize: configService.get('NODE_ENV') === 'development',
                    logging: configService.get('NODE_ENV') === 'development',
                    ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
                    extra: {
                        max: 20,
                        idleTimeoutMillis: 30000,
                        connectionTimeoutMillis: 2000,
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            cache_manager_1.CacheModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    store: redisStore,
                    host: configService.get('redis.host'),
                    port: configService.get('redis.port'),
                    password: configService.get('redis.password'),
                    db: configService.get('redis.db'),
                    ttl: 300,
                    max: 1000,
                }),
                inject: [config_1.ConfigService],
                isGlobal: true,
            }),
            throttler_1.ThrottlerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    ttl: 60,
                    limit: configService.get('NODE_ENV') === 'production' ? 100 : 1000,
                }),
                inject: [config_1.ConfigService],
            }),
            graphql_1.GraphQLModule.forRootAsync({
                driver: apollo_1.ApolloDriver,
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    autoSchemaFile: true,
                    sortSchema: true,
                    playground: configService.get('NODE_ENV') === 'development',
                    introspection: configService.get('NODE_ENV') === 'development',
                    context: ({ req, res }) => ({ req, res }),
                    formatError: (error) => {
                        console.error('GraphQL Error:', error);
                        return {
                            message: error.message,
                            code: error.extensions?.code,
                            timestamp: new Date().toISOString(),
                        };
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            database_module_1.DatabaseModule,
            common_module_1.CommonModule,
            auth_module_1.AuthModule,
            user_module_1.UserModule,
            rbac_module_1.RbacModule,
            tenant_module_1.TenantModule,
            health_module_1.HealthModule,
        ],
        controllers: [],
        providers: [],
    })
], AppModule);


/***/ }),
/* 8 */
/***/ ((module) => {

module.exports = require("@nestjs/typeorm");

/***/ }),
/* 9 */
/***/ ((module) => {

module.exports = require("@nestjs/cache-manager");

/***/ }),
/* 10 */
/***/ ((module) => {

module.exports = require("@nestjs/throttler");

/***/ }),
/* 11 */
/***/ ((module) => {

module.exports = require("@nestjs/graphql");

/***/ }),
/* 12 */
/***/ ((module) => {

module.exports = require("@nestjs/apollo");

/***/ }),
/* 13 */
/***/ ((module) => {

module.exports = require("nest-winston");

/***/ }),
/* 14 */
/***/ ((module) => {

module.exports = require("cache-manager-redis-store");

/***/ }),
/* 15 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.databaseConfig = void 0;
const config_1 = __webpack_require__(3);
exports.databaseConfig = (0, config_1.registerAs)('database', () => ({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    name: process.env.DB_NAME || 'peoplenest_hrms',
    poolSize: parseInt(process.env.DB_POOL_SIZE || '20', 10),
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '100', 10),
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000', 10),
    timeout: parseInt(process.env.DB_TIMEOUT || '60000', 10),
    ssl: process.env.NODE_ENV === 'production' ? {
        rejectUnauthorized: false,
        ca: process.env.DB_SSL_CA,
        cert: process.env.DB_SSL_CERT,
        key: process.env.DB_SSL_KEY,
    } : false,
    logging: process.env.NODE_ENV === 'development',
    logger: 'advanced-console',
    migrationsRun: process.env.NODE_ENV === 'production',
    synchronize: process.env.NODE_ENV === 'development',
    tenantMode: process.env.TENANT_MODE || 'schema',
    defaultTenant: process.env.DEFAULT_TENANT || 'default',
    cache: {
        duration: parseInt(process.env.DB_CACHE_DURATION || '30000', 10),
        type: 'redis',
    },
    backup: {
        enabled: process.env.DB_BACKUP_ENABLED === 'true',
        schedule: process.env.DB_BACKUP_SCHEDULE || '0 2 * * *',
        retention: parseInt(process.env.DB_BACKUP_RETENTION || '30', 10),
    },
}));


/***/ }),
/* 16 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.redisConfig = void 0;
const config_1 = __webpack_require__(3);
exports.redisConfig = (0, config_1.registerAs)('redis', () => ({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || '10000', 10),
    lazyConnect: true,
    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3', 10),
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100', 10),
    family: 4,
    keepAlive: true,
    ttl: parseInt(process.env.REDIS_TTL || '300', 10),
    max: parseInt(process.env.REDIS_MAX_ITEMS || '1000', 10),
    session: {
        ttl: parseInt(process.env.REDIS_SESSION_TTL || '86400', 10),
        prefix: process.env.REDIS_SESSION_PREFIX || 'sess:',
    },
    keys: {
        user: 'user:',
        tenant: 'tenant:',
        auth: 'auth:',
        session: 'session:',
        rate_limit: 'rate_limit:',
        temp: 'temp:',
    },
    cluster: {
        enabled: process.env.REDIS_CLUSTER_ENABLED === 'true',
        nodes: process.env.REDIS_CLUSTER_NODES?.split(',') || [],
        options: {
            redisOptions: {
                password: process.env.REDIS_PASSWORD,
            },
        },
    },
    sentinel: {
        enabled: process.env.REDIS_SENTINEL_ENABLED === 'true',
        sentinels: process.env.REDIS_SENTINELS?.split(',').map(sentinel => {
            const [host, port] = sentinel.split(':');
            return { host, port: parseInt(port, 10) };
        }) || [],
        name: process.env.REDIS_SENTINEL_NAME || 'mymaster',
    },
}));


/***/ }),
/* 17 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.jwtConfig = void 0;
const config_1 = __webpack_require__(3);
exports.jwtConfig = (0, config_1.registerAs)('jwt', () => ({
    accessToken: {
        secret: process.env.JWT_ACCESS_SECRET || 'your-super-secret-access-key-change-in-production',
        expiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
        algorithm: 'HS256',
        issuer: process.env.JWT_ISSUER || 'peoplenest-hrms',
        audience: process.env.JWT_AUDIENCE || 'peoplenest-users',
    },
    refreshToken: {
        secret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-in-production',
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        algorithm: 'HS256',
        issuer: process.env.JWT_ISSUER || 'peoplenest-hrms',
        audience: process.env.JWT_AUDIENCE || 'peoplenest-users',
    },
    passwordResetToken: {
        secret: process.env.JWT_PASSWORD_RESET_SECRET || 'your-super-secret-password-reset-key',
        expiresIn: process.env.JWT_PASSWORD_RESET_EXPIRES_IN || '1h',
        algorithm: 'HS256',
    },
    emailVerificationToken: {
        secret: process.env.JWT_EMAIL_VERIFICATION_SECRET || 'your-super-secret-email-verification-key',
        expiresIn: process.env.JWT_EMAIL_VERIFICATION_EXPIRES_IN || '24h',
        algorithm: 'HS256',
    },
    oauth: {
        keycloak: {
            enabled: process.env.OAUTH_KEYCLOAK_ENABLED === 'true',
            serverUrl: process.env.KEYCLOAK_SERVER_URL || 'http://localhost:8080',
            realm: process.env.KEYCLOAK_REALM || 'peoplenest',
            clientId: process.env.KEYCLOAK_CLIENT_ID || 'peoplenest-client',
            clientSecret: process.env.KEYCLOAK_CLIENT_SECRET || '',
            redirectUri: process.env.KEYCLOAK_REDIRECT_URI || 'http://localhost:3001/api/v1/auth/keycloak/callback',
        },
        oauth2: {
            enabled: process.env.OAUTH2_ENABLED === 'true',
            authorizationURL: process.env.OAUTH2_AUTHORIZATION_URL || '',
            tokenURL: process.env.OAUTH2_TOKEN_URL || '',
            clientID: process.env.OAUTH2_CLIENT_ID || '',
            clientSecret: process.env.OAUTH2_CLIENT_SECRET || '',
            callbackURL: process.env.OAUTH2_CALLBACK_URL || '',
            scope: process.env.OAUTH2_SCOPE?.split(',') || ['openid', 'profile', 'email'],
        },
    },
    security: {
        rotateRefreshTokens: process.env.JWT_ROTATE_REFRESH_TOKENS === 'true',
        revokeRefreshTokensOnPasswordChange: true,
        rateLimiting: {
            login: {
                windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW || '900000', 10),
                max: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5', 10),
            },
            register: {
                windowMs: parseInt(process.env.REGISTER_RATE_LIMIT_WINDOW || '3600000', 10),
                max: parseInt(process.env.REGISTER_RATE_LIMIT_MAX || '3', 10),
            },
        },
        password: {
            minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8', 10),
            requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE === 'true',
            requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE === 'true',
            requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS === 'true',
            requireSpecialChars: process.env.PASSWORD_REQUIRE_SPECIAL_CHARS === 'true',
            maxAge: parseInt(process.env.PASSWORD_MAX_AGE || '7776000', 10),
        },
    },
}));


/***/ }),
/* 18 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.createWinstonConfig = createWinstonConfig;
const winston = __webpack_require__(19);
const nest_winston_1 = __webpack_require__(13);
function createWinstonConfig() {
    const environment = process.env.NODE_ENV || 'development';
    const logLevel = process.env.LOG_LEVEL || (environment === 'production' ? 'info' : 'debug');
    const customFormat = winston.format.combine(winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, context, correlationId, tenantId, userId, ...meta }) => {
        const logEntry = {
            timestamp,
            level,
            message,
            context,
            correlationId,
            tenantId,
            userId,
            ...meta,
        };
        Object.keys(logEntry).forEach(key => {
            if (logEntry[key] === undefined) {
                delete logEntry[key];
            }
        });
        return JSON.stringify(logEntry);
    }));
    const transports = [];
    if (environment === 'development') {
        transports.push(new winston.transports.Console({
            format: winston.format.combine(winston.format.timestamp({ format: 'HH:mm:ss' }), winston.format.ms(), nest_winston_1.utilities.format.nestLike('PeopleNest', {
                colors: true,
                prettyPrint: true,
            })),
        }));
    }
    if (environment === 'production') {
        transports.push(new winston.transports.File({
            filename: 'logs/combined.log',
            format: customFormat,
            maxsize: 10485760,
            maxFiles: 10,
            tailable: true,
        }));
        transports.push(new winston.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            format: customFormat,
            maxsize: 10485760,
            maxFiles: 10,
            tailable: true,
        }));
        transports.push(new winston.transports.File({
            filename: 'logs/audit.log',
            format: customFormat,
            maxsize: 10485760,
            maxFiles: 50,
            tailable: true,
        }));
        transports.push(new winston.transports.Console({
            format: customFormat,
        }));
    }
    return {
        level: logLevel,
        format: customFormat,
        defaultMeta: {
            service: 'peoplenest-hrms',
            version: process.env.APP_VERSION || '1.0.0',
            environment,
        },
        transports,
        exceptionHandlers: [
            new winston.transports.File({
                filename: 'logs/exceptions.log',
                format: customFormat,
            }),
        ],
        rejectionHandlers: [
            new winston.transports.File({
                filename: 'logs/rejections.log',
                format: customFormat,
            }),
        ],
        exitOnError: false,
    };
}


/***/ }),
/* 19 */
/***/ ((module) => {

module.exports = require("winston");

/***/ }),
/* 20 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DatabaseModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(8);
const config_1 = __webpack_require__(3);
const database_service_1 = __webpack_require__(21);
const tenant_database_service_1 = __webpack_require__(26);
const migration_service_1 = __webpack_require__(27);
const user_entity_1 = __webpack_require__(28);
const refresh_token_entity_1 = __webpack_require__(29);
const password_reset_entity_1 = __webpack_require__(30);
const login_attempt_entity_1 = __webpack_require__(31);
const role_entity_1 = __webpack_require__(32);
const permission_entity_1 = __webpack_require__(33);
const user_role_entity_1 = __webpack_require__(34);
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    type: 'postgres',
                    host: configService.get('database.host'),
                    port: configService.get('database.port'),
                    username: configService.get('database.username'),
                    password: configService.get('database.password'),
                    database: configService.get('database.name'),
                    schema: 'public',
                    entities: [
                        user_entity_1.User,
                        refresh_token_entity_1.RefreshToken,
                        password_reset_entity_1.PasswordReset,
                        login_attempt_entity_1.LoginAttempt,
                        role_entity_1.Role,
                        permission_entity_1.Permission,
                        user_role_entity_1.UserRole,
                    ],
                    synchronize: configService.get('NODE_ENV') === 'development',
                    logging: configService.get('NODE_ENV') === 'development' ? ['query', 'error'] : ['error'],
                    migrations: ['dist/database/migrations/*.js'],
                    migrationsTableName: 'migrations',
                    migrationsRun: false,
                    ssl: configService.get('NODE_ENV') === 'production' ? {
                        rejectUnauthorized: false,
                    } : false,
                    extra: {
                        max: configService.get('database.maxConnections', 20),
                        idleTimeoutMillis: 30000,
                        connectionTimeoutMillis: 2000,
                    },
                    poolSize: configService.get('database.poolSize', 20),
                    acquireTimeout: configService.get('database.acquireTimeout', 60000),
                    timeout: configService.get('database.timeout', 60000),
                    cache: configService.get('database.cache.type') === 'redis' ? {
                        type: 'redis',
                        options: {
                            host: configService.get('redis.host', 'localhost'),
                            port: configService.get('redis.port', 6379),
                            password: configService.get('redis.password'),
                            db: configService.get('redis.cacheDb', 1),
                        },
                        duration: configService.get('database.cache.duration', 30000),
                    } : false,
                }),
                inject: [config_1.ConfigService],
            }),
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                refresh_token_entity_1.RefreshToken,
                password_reset_entity_1.PasswordReset,
                login_attempt_entity_1.LoginAttempt,
                role_entity_1.Role,
                permission_entity_1.Permission,
                user_role_entity_1.UserRole,
            ]),
        ],
        providers: [
            database_service_1.DatabaseService,
            tenant_database_service_1.TenantDatabaseService,
            migration_service_1.MigrationService,
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            database_service_1.DatabaseService,
            tenant_database_service_1.TenantDatabaseService,
            migration_service_1.MigrationService,
        ],
    })
], DatabaseModule);


/***/ }),
/* 21 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DatabaseService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(22);
const typeorm_2 = __webpack_require__(8);
const config_1 = __webpack_require__(3);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const correlation_service_1 = __webpack_require__(23);
let DatabaseService = DatabaseService_1 = class DatabaseService {
    dataSource;
    configService;
    winstonLogger;
    correlationService;
    logger = new common_1.Logger(DatabaseService_1.name);
    startTime = Date.now();
    constructor(dataSource, configService, winstonLogger, correlationService) {
        this.dataSource = dataSource;
        this.configService = configService;
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
    }
    async onModuleInit() {
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Initializing database service', {
            context: DatabaseService_1.name,
            correlationId,
        });
        try {
            await this.healthCheck();
            this.logger.log('Database connection established successfully');
        }
        catch (error) {
            this.winstonLogger.error('Failed to establish database connection', {
                context: DatabaseService_1.name,
                correlationId,
                error: error.message,
            });
            throw error;
        }
    }
    async healthCheck() {
        const startTime = Date.now();
        const correlationId = this.correlationService.getCorrelationId();
        try {
            await this.dataSource.query('SELECT 1');
            const responseTime = Date.now() - startTime;
            const stats = await this.getConnectionStats();
            const healthCheck = {
                isHealthy: true,
                responseTime,
                activeConnections: stats.activeConnections,
                maxConnections: stats.maxConnections,
            };
            this.winstonLogger.debug('Database health check successful', {
                context: DatabaseService_1.name,
                correlationId,
                healthCheck,
            });
            return healthCheck;
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            const healthCheck = {
                isHealthy: false,
                responseTime,
                activeConnections: 0,
                maxConnections: 0,
                error: error.message,
            };
            this.winstonLogger.error('Database health check failed', {
                context: DatabaseService_1.name,
                correlationId,
                healthCheck,
                error: error.message,
            });
            return healthCheck;
        }
    }
    async getConnectionStats() {
        try {
            const driver = this.dataSource.driver;
            const pool = driver.master || driver.pool;
            const stats = {
                totalConnections: pool?.totalCount || 0,
                activeConnections: pool?.acquiredCount || 0,
                idleConnections: pool?.freeCount || 0,
                waitingConnections: pool?.pendingCount || 0,
                maxConnections: pool?.max || 0,
                uptime: Date.now() - this.startTime,
            };
            return stats;
        }
        catch (error) {
            this.logger.warn('Failed to get connection stats', error.message);
            return {
                totalConnections: 0,
                activeConnections: 0,
                idleConnections: 0,
                waitingConnections: 0,
                maxConnections: 0,
                uptime: Date.now() - this.startTime,
            };
        }
    }
    createQueryRunner() {
        return this.dataSource.createQueryRunner();
    }
    async executeTransaction(operation) {
        const queryRunner = this.createQueryRunner();
        const correlationId = this.correlationService.getCorrelationId();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.winstonLogger.debug('Starting database transaction', {
                context: DatabaseService_1.name,
                correlationId,
            });
            const result = await operation(queryRunner);
            await queryRunner.commitTransaction();
            this.winstonLogger.debug('Database transaction committed successfully', {
                context: DatabaseService_1.name,
                correlationId,
            });
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.winstonLogger.error('Database transaction rolled back', {
                context: DatabaseService_1.name,
                correlationId,
                error: error.message,
            });
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async schemaExists(schemaName) {
        try {
            const result = await this.dataSource.query('SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1', [schemaName]);
            return result.length > 0;
        }
        catch (error) {
            this.logger.error(`Failed to check if schema ${schemaName} exists`, error.message);
            return false;
        }
    }
    async createSchema(schemaName) {
        const correlationId = this.correlationService.getCorrelationId();
        try {
            await this.dataSource.query(`CREATE SCHEMA IF NOT EXISTS "${schemaName}"`);
            this.winstonLogger.info('Schema created successfully', {
                context: DatabaseService_1.name,
                correlationId,
                schemaName,
            });
        }
        catch (error) {
            this.winstonLogger.error('Failed to create schema', {
                context: DatabaseService_1.name,
                correlationId,
                schemaName,
                error: error.message,
            });
            throw error;
        }
    }
    async dropSchema(schemaName, cascade = false) {
        const correlationId = this.correlationService.getCorrelationId();
        try {
            const cascadeClause = cascade ? 'CASCADE' : 'RESTRICT';
            await this.dataSource.query(`DROP SCHEMA IF EXISTS "${schemaName}" ${cascadeClause}`);
            this.winstonLogger.info('Schema dropped successfully', {
                context: DatabaseService_1.name,
                correlationId,
                schemaName,
                cascade,
            });
        }
        catch (error) {
            this.winstonLogger.error('Failed to drop schema', {
                context: DatabaseService_1.name,
                correlationId,
                schemaName,
                error: error.message,
            });
            throw error;
        }
    }
    async getAllSchemas() {
        try {
            const result = await this.dataSource.query(`SELECT schema_name FROM information_schema.schemata 
         WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
         ORDER BY schema_name`);
            return result.map(row => row.schema_name);
        }
        catch (error) {
            this.logger.error('Failed to get all schemas', error.message);
            return [];
        }
    }
    async getDatabaseInfo() {
        try {
            const versionResult = await this.dataSource.query('SELECT version()');
            const sizeResult = await this.dataSource.query(`SELECT pg_size_pretty(pg_database_size(current_database())) as size`);
            const options = this.dataSource.options;
            return {
                version: versionResult[0]?.version,
                size: sizeResult[0]?.size,
                name: options.database,
                host: options.host,
                port: options.port,
            };
        }
        catch (error) {
            this.logger.error('Failed to get database info', error.message);
            return null;
        }
    }
    getDataSource() {
        return this.dataSource;
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __param(2, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_1.DataSource !== "undefined" && typeorm_1.DataSource) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _c : Object, typeof (_d = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _d : Object])
], DatabaseService);


/***/ }),
/* 22 */
/***/ ((module) => {

module.exports = require("typeorm");

/***/ }),
/* 23 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CorrelationService_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CorrelationService = void 0;
const common_1 = __webpack_require__(2);
const async_hooks_1 = __webpack_require__(24);
const uuid_1 = __webpack_require__(25);
let CorrelationService = class CorrelationService {
    static { CorrelationService_1 = this; }
    static asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();
    context;
    constructor() {
        this.context = {
            correlationId: (0, uuid_1.v4)(),
        };
    }
    generateCorrelationId() {
        const correlationId = (0, uuid_1.v4)();
        this.context.correlationId = correlationId;
        return correlationId;
    }
    getCorrelationId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.correlationId || this.context.correlationId;
    }
    setCorrelationId(correlationId) {
        this.context.correlationId = correlationId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.correlationId = correlationId;
        }
    }
    getTenantId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.tenantId || this.context.tenantId;
    }
    setTenantId(tenantId) {
        this.context.tenantId = tenantId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.tenantId = tenantId;
        }
    }
    getUserId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.userId || this.context.userId;
    }
    setUserId(userId) {
        this.context.userId = userId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.userId = userId;
        }
    }
    getRequestId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.requestId || this.context.requestId;
    }
    setRequestId(requestId) {
        this.context.requestId = requestId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.requestId = requestId;
        }
    }
    getSessionId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.sessionId || this.context.sessionId;
    }
    setSessionId(sessionId) {
        this.context.sessionId = sessionId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.sessionId = sessionId;
        }
    }
    getTraceId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.traceId || this.context.traceId;
    }
    setTraceId(traceId) {
        this.context.traceId = traceId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.traceId = traceId;
        }
    }
    getSpanId() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context?.spanId || this.context.spanId;
    }
    setSpanId(spanId) {
        this.context.spanId = spanId;
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            currentContext.spanId = spanId;
        }
    }
    getContext() {
        const context = CorrelationService_1.asyncLocalStorage.getStore();
        return context || this.context;
    }
    setContext(context) {
        this.context = { ...this.context, ...context };
        const currentContext = CorrelationService_1.asyncLocalStorage.getStore();
        if (currentContext) {
            Object.assign(currentContext, context);
        }
    }
    static runWithContext(context, fn) {
        return CorrelationService_1.asyncLocalStorage.run(context, fn);
    }
    createChildContext() {
        const parentContext = this.getContext();
        return {
            ...parentContext,
            correlationId: (0, uuid_1.v4)(),
        };
    }
};
exports.CorrelationService = CorrelationService;
exports.CorrelationService = CorrelationService = CorrelationService_1 = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST }),
    __metadata("design:paramtypes", [])
], CorrelationService);


/***/ }),
/* 24 */
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),
/* 25 */
/***/ ((module) => {

module.exports = require("uuid");

/***/ }),
/* 26 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TenantDatabaseService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TenantDatabaseService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(22);
const typeorm_2 = __webpack_require__(8);
const config_1 = __webpack_require__(3);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const correlation_service_1 = __webpack_require__(23);
const database_service_1 = __webpack_require__(21);
let TenantDatabaseService = TenantDatabaseService_1 = class TenantDatabaseService {
    dataSource;
    configService;
    winstonLogger;
    correlationService;
    databaseService;
    logger = new common_1.Logger(TenantDatabaseService_1.name);
    tenantConnections = new Map();
    schemaCache = new Map();
    constructor(dataSource, configService, winstonLogger, correlationService, databaseService) {
        this.dataSource = dataSource;
        this.configService = configService;
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
        this.databaseService = databaseService;
    }
    getTenantSchemaName(tenantId) {
        if (!tenantId || typeof tenantId !== 'string') {
            throw new common_1.BadRequestException('Invalid tenant ID');
        }
        const sanitized = tenantId
            .toLowerCase()
            .replace(/[^a-z0-9_]/g, '_')
            .replace(/^[^a-z_]/, '_')
            .substring(0, 63);
        return `tenant_${sanitized}`;
    }
    async createTenantSchema(config) {
        const { tenantId, createIfNotExists = true } = config;
        const schemaName = config.schemaName || this.getTenantSchemaName(tenantId);
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Creating tenant schema', {
            context: TenantDatabaseService_1.name,
            correlationId,
            tenantId,
            schemaName,
        });
        try {
            const exists = await this.databaseService.schemaExists(schemaName);
            if (exists && !createIfNotExists) {
                throw new common_1.BadRequestException(`Tenant schema ${schemaName} already exists`);
            }
            if (!exists) {
                await this.databaseService.createSchema(schemaName);
                await this.createTenantTables(schemaName);
                this.winstonLogger.info('Tenant schema created successfully', {
                    context: TenantDatabaseService_1.name,
                    correlationId,
                    tenantId,
                    schemaName,
                });
            }
            const tenantSchema = {
                tenantId,
                schemaName,
                isActive: true,
                createdAt: new Date(),
            };
            this.schemaCache.set(tenantId, tenantSchema);
            return tenantSchema;
        }
        catch (error) {
            this.winstonLogger.error('Failed to create tenant schema', {
                context: TenantDatabaseService_1.name,
                correlationId,
                tenantId,
                schemaName,
                error: error.message,
            });
            throw new common_1.InternalServerErrorException(`Failed to create tenant schema: ${error.message}`);
        }
    }
    async createTenantTables(schemaName) {
        const correlationId = this.correlationService.getCorrelationId();
        try {
            await this.dataSource.query(`SET search_path TO "${schemaName}"`);
            const tenantTables = [
                `CREATE TABLE IF NOT EXISTS employees (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          employee_id VARCHAR(50) UNIQUE NOT NULL,
          user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
          first_name VARCHAR(100) NOT NULL,
          last_name VARCHAR(100) NOT NULL,
          email VARCHAR(255) UNIQUE NOT NULL,
          phone_number VARCHAR(20),
          date_of_birth DATE,
          hire_date DATE NOT NULL,
          termination_date DATE,
          job_title VARCHAR(100),
          department_id UUID REFERENCES departments(id),
          manager_id UUID REFERENCES employees(id),
          employment_status VARCHAR(20) DEFAULT 'ACTIVE',
          employment_type VARCHAR(20) DEFAULT 'FULL_TIME',
          salary DECIMAL(12,2),
          currency VARCHAR(3) DEFAULT 'USD',
          address JSONB,
          emergency_contacts JSONB,
          documents JSONB,
          metadata JSONB,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          deleted_at TIMESTAMP WITH TIME ZONE
        )`,
                `CREATE TABLE IF NOT EXISTS departments (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(100) NOT NULL,
          description TEXT,
          parent_department_id UUID REFERENCES departments(id),
          manager_id UUID REFERENCES employees(id),
          cost_center VARCHAR(50),
          budget DECIMAL(12,2),
          location VARCHAR(100),
          metadata JSONB,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          deleted_at TIMESTAMP WITH TIME ZONE
        )`,
                `CREATE TABLE IF NOT EXISTS attendance_records (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
          date DATE NOT NULL,
          clock_in TIMESTAMP WITH TIME ZONE,
          clock_out TIMESTAMP WITH TIME ZONE,
          break_start TIMESTAMP WITH TIME ZONE,
          break_end TIMESTAMP WITH TIME ZONE,
          total_hours DECIMAL(4,2),
          overtime_hours DECIMAL(4,2),
          status VARCHAR(20) DEFAULT 'PRESENT',
          location JSONB,
          notes TEXT,
          approved_by UUID REFERENCES employees(id),
          approved_at TIMESTAMP WITH TIME ZONE,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(employee_id, date)
        )`,
                `CREATE TABLE IF NOT EXISTS leave_requests (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
          leave_type VARCHAR(50) NOT NULL,
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          total_days DECIMAL(3,1) NOT NULL,
          reason TEXT,
          status VARCHAR(20) DEFAULT 'PENDING',
          approved_by UUID REFERENCES employees(id),
          approved_at TIMESTAMP WITH TIME ZONE,
          rejection_reason TEXT,
          documents JSONB,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )`,
                `CREATE TABLE IF NOT EXISTS payroll_records (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
          pay_period_start DATE NOT NULL,
          pay_period_end DATE NOT NULL,
          gross_salary DECIMAL(12,2) NOT NULL,
          deductions JSONB,
          bonuses JSONB,
          overtime_pay DECIMAL(12,2) DEFAULT 0,
          net_salary DECIMAL(12,2) NOT NULL,
          tax_deductions DECIMAL(12,2) DEFAULT 0,
          currency VARCHAR(3) DEFAULT 'USD',
          status VARCHAR(20) DEFAULT 'DRAFT',
          processed_by UUID REFERENCES employees(id),
          processed_at TIMESTAMP WITH TIME ZONE,
          paid_at TIMESTAMP WITH TIME ZONE,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )`,
            ];
            for (const tableQuery of tenantTables) {
                await this.dataSource.query(tableQuery);
            }
            const indexes = [
                'CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department_id)',
                'CREATE INDEX IF NOT EXISTS idx_employees_manager ON employees(manager_id)',
                'CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(employment_status)',
                'CREATE INDEX IF NOT EXISTS idx_attendance_employee_date ON attendance_records(employee_id, date)',
                'CREATE INDEX IF NOT EXISTS idx_leave_requests_employee ON leave_requests(employee_id)',
                'CREATE INDEX IF NOT EXISTS idx_leave_requests_status ON leave_requests(status)',
                'CREATE INDEX IF NOT EXISTS idx_payroll_employee_period ON payroll_records(employee_id, pay_period_start, pay_period_end)',
            ];
            for (const indexQuery of indexes) {
                await this.dataSource.query(indexQuery);
            }
            this.winstonLogger.info('Tenant tables created successfully', {
                context: TenantDatabaseService_1.name,
                correlationId,
                schemaName,
                tablesCount: tenantTables.length,
                indexesCount: indexes.length,
            });
        }
        catch (error) {
            this.winstonLogger.error('Failed to create tenant tables', {
                context: TenantDatabaseService_1.name,
                correlationId,
                schemaName,
                error: error.message,
            });
            throw error;
        }
        finally {
            await this.dataSource.query('SET search_path TO public');
        }
    }
    async getTenantSchema(tenantId) {
        if (this.schemaCache.has(tenantId)) {
            return this.schemaCache.get(tenantId);
        }
        const schemaName = this.getTenantSchemaName(tenantId);
        const exists = await this.databaseService.schemaExists(schemaName);
        if (!exists) {
            return null;
        }
        const tenantSchema = {
            tenantId,
            schemaName,
            isActive: true,
            createdAt: new Date(),
        };
        this.schemaCache.set(tenantId, tenantSchema);
        return tenantSchema;
    }
    async executeInTenantContext(tenantId, operation) {
        const tenantSchema = await this.getTenantSchema(tenantId);
        if (!tenantSchema) {
            throw new common_1.BadRequestException(`Tenant schema not found for tenant: ${tenantId}`);
        }
        const correlationId = this.correlationService.getCorrelationId();
        return this.databaseService.executeTransaction(async (queryRunner) => {
            await queryRunner.query(`SET search_path TO "${tenantSchema.schemaName}", public`);
            this.winstonLogger.debug('Executing operation in tenant context', {
                context: TenantDatabaseService_1.name,
                correlationId,
                tenantId,
                schemaName: tenantSchema.schemaName,
            });
            try {
                return await operation(queryRunner.manager);
            }
            finally {
                await queryRunner.query('SET search_path TO public');
            }
        });
    }
    async deleteTenantSchema(tenantId, force = false) {
        const tenantSchema = await this.getTenantSchema(tenantId);
        if (!tenantSchema) {
            throw new common_1.BadRequestException(`Tenant schema not found for tenant: ${tenantId}`);
        }
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.warn('Deleting tenant schema', {
            context: TenantDatabaseService_1.name,
            correlationId,
            tenantId,
            schemaName: tenantSchema.schemaName,
            force,
        });
        try {
            await this.databaseService.dropSchema(tenantSchema.schemaName, true);
            this.schemaCache.delete(tenantId);
            this.winstonLogger.info('Tenant schema deleted successfully', {
                context: TenantDatabaseService_1.name,
                correlationId,
                tenantId,
                schemaName: tenantSchema.schemaName,
            });
        }
        catch (error) {
            this.winstonLogger.error('Failed to delete tenant schema', {
                context: TenantDatabaseService_1.name,
                correlationId,
                tenantId,
                schemaName: tenantSchema.schemaName,
                error: error.message,
            });
            throw new common_1.InternalServerErrorException(`Failed to delete tenant schema: ${error.message}`);
        }
    }
    async getAllTenantSchemas() {
        try {
            const allSchemas = await this.databaseService.getAllSchemas();
            const tenantSchemas = allSchemas
                .filter(schema => schema.startsWith('tenant_'))
                .map(schema => {
                const tenantId = schema.replace('tenant_', '');
                return {
                    tenantId,
                    schemaName: schema,
                    isActive: true,
                    createdAt: new Date(),
                };
            });
            return tenantSchemas;
        }
        catch (error) {
            this.logger.error('Failed to get all tenant schemas', error.message);
            return [];
        }
    }
};
exports.TenantDatabaseService = TenantDatabaseService;
exports.TenantDatabaseService = TenantDatabaseService = TenantDatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __param(2, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_1.DataSource !== "undefined" && typeorm_1.DataSource) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _c : Object, typeof (_d = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _d : Object, typeof (_e = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _e : Object])
], TenantDatabaseService);


/***/ }),
/* 27 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MigrationService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MigrationService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(22);
const typeorm_2 = __webpack_require__(8);
const config_1 = __webpack_require__(3);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const correlation_service_1 = __webpack_require__(23);
const database_service_1 = __webpack_require__(21);
let MigrationService = MigrationService_1 = class MigrationService {
    dataSource;
    configService;
    winstonLogger;
    correlationService;
    databaseService;
    logger = new common_1.Logger(MigrationService_1.name);
    constructor(dataSource, configService, winstonLogger, correlationService, databaseService) {
        this.dataSource = dataSource;
        this.configService = configService;
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
        this.databaseService = databaseService;
    }
    async runMigrations() {
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Starting database migrations', {
            context: MigrationService_1.name,
            correlationId,
        });
        try {
            const pendingMigrations = await this.dataSource.showMigrations();
            if (!pendingMigrations || (Array.isArray(pendingMigrations) && pendingMigrations.length === 0)) {
                this.winstonLogger.info('No pending migrations found', {
                    context: MigrationService_1.name,
                    correlationId,
                });
                return {
                    success: true,
                    migrationsRun: 0,
                    migrations: await this.getMigrationHistory(),
                };
            }
            const migrations = await this.dataSource.runMigrations({
                transaction: 'each',
            });
            const migrationsCount = Array.isArray(migrations) ? migrations.length : 0;
            const migrationNames = Array.isArray(migrations) ? migrations.map(m => m.name) : [];
            this.winstonLogger.info('Database migrations completed successfully', {
                context: MigrationService_1.name,
                correlationId,
                migrationsRun: migrationsCount,
                migrations: migrationNames,
            });
            return {
                success: true,
                migrationsRun: migrationsCount,
                migrations: await this.getMigrationHistory(),
            };
        }
        catch (error) {
            this.winstonLogger.error('Database migrations failed', {
                context: MigrationService_1.name,
                correlationId,
                error: error.message,
            });
            return {
                success: false,
                migrationsRun: 0,
                error: error.message,
                migrations: await this.getMigrationHistory(),
            };
        }
    }
    async revertLastMigration() {
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Reverting last migration', {
            context: MigrationService_1.name,
            correlationId,
        });
        try {
            await this.dataSource.undoLastMigration({
                transaction: 'each',
            });
            this.winstonLogger.info('Last migration reverted successfully', {
                context: MigrationService_1.name,
                correlationId,
            });
            return {
                success: true,
                migrationsRun: -1,
                migrations: await this.getMigrationHistory(),
            };
        }
        catch (error) {
            this.winstonLogger.error('Failed to revert last migration', {
                context: MigrationService_1.name,
                correlationId,
                error: error.message,
            });
            return {
                success: false,
                migrationsRun: 0,
                error: error.message,
                migrations: await this.getMigrationHistory(),
            };
        }
    }
    async getMigrationHistory() {
        try {
            const executedMigrations = await this.dataSource.query('SELECT * FROM migrations ORDER BY timestamp ASC');
            return executedMigrations.map(migration => ({
                id: migration.id,
                timestamp: migration.timestamp,
                name: migration.name,
                executed: true,
                executedAt: migration.timestamp ? new Date(migration.timestamp) : undefined,
            }));
        }
        catch (error) {
            this.logger.warn('Failed to get migration history', error.message);
            return [];
        }
    }
    async hasPendingMigrations() {
        try {
            const pendingMigrations = await this.dataSource.showMigrations();
            return Array.isArray(pendingMigrations) && pendingMigrations.length > 0;
        }
        catch (error) {
            this.logger.error('Failed to check pending migrations', error.message);
            return false;
        }
    }
    async generateMigration(name) {
        const correlationId = this.correlationService.getCorrelationId();
        try {
            this.winstonLogger.info('Generating new migration', {
                context: MigrationService_1.name,
                correlationId,
                name,
            });
            const timestamp = Date.now();
            const className = name.replace(/[^a-zA-Z0-9]/g, '');
            const migrationContent = `import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${className}${timestamp} implements MigrationInterface {
  name = '${className}${timestamp}';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add your migration logic here
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add your rollback logic here
  }
}
`;
            const fileName = `${timestamp}-${name}.ts`;
            this.winstonLogger.info('Migration template generated', {
                context: MigrationService_1.name,
                correlationId,
                fileName,
            });
            return migrationContent;
        }
        catch (error) {
            this.winstonLogger.error('Failed to generate migration', {
                context: MigrationService_1.name,
                correlationId,
                name,
                error: error.message,
            });
            throw error;
        }
    }
    async initializeSchema() {
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Initializing database schema', {
            context: MigrationService_1.name,
            correlationId,
        });
        try {
            await this.createExtensions();
            if (this.configService.get('NODE_ENV') === 'development') {
                await this.dataSource.synchronize();
                this.logger.log('Database schema synchronized');
            }
            const result = await this.runMigrations();
            if (!result.success) {
                throw new Error(`Migration failed: ${result.error}`);
            }
            this.winstonLogger.info('Database schema initialized successfully', {
                context: MigrationService_1.name,
                correlationId,
                migrationsRun: result.migrationsRun,
            });
        }
        catch (error) {
            this.winstonLogger.error('Failed to initialize database schema', {
                context: MigrationService_1.name,
                correlationId,
                error: error.message,
            });
            throw error;
        }
    }
    async createExtensions() {
        const extensions = [
            'uuid-ossp',
            'pgcrypto',
            'pg_trgm',
            'btree_gin',
        ];
        for (const extension of extensions) {
            try {
                await this.dataSource.query(`CREATE EXTENSION IF NOT EXISTS "${extension}"`);
                this.logger.debug(`Extension ${extension} created/verified`);
            }
            catch (error) {
                this.logger.warn(`Failed to create extension ${extension}:`, error.message);
            }
        }
    }
    async backupSchema() {
        const correlationId = this.correlationService.getCorrelationId();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `schema_backup_${timestamp}`;
        this.winstonLogger.info('Creating schema backup', {
            context: MigrationService_1.name,
            correlationId,
            backupName,
        });
        try {
            const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      `);
            const backupData = {
                timestamp: new Date(),
                tables: tables.map(t => t.table_name),
                migrations: await this.getMigrationHistory(),
            };
            this.winstonLogger.info('Schema backup created', {
                context: MigrationService_1.name,
                correlationId,
                backupName,
                tablesCount: tables.length,
            });
            return JSON.stringify(backupData, null, 2);
        }
        catch (error) {
            this.winstonLogger.error('Failed to create schema backup', {
                context: MigrationService_1.name,
                correlationId,
                backupName,
                error: error.message,
            });
            throw error;
        }
    }
    async getSchemaInfo() {
        try {
            const tables = await this.dataSource.query(`
        SELECT 
          table_name,
          table_type,
          table_schema
        FROM information_schema.tables 
        WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
        ORDER BY table_schema, table_name
      `);
            const indexes = await this.dataSource.query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
        ORDER BY schemaname, tablename, indexname
      `);
            return {
                tables: tables.length,
                indexes: indexes.length,
                tableDetails: tables,
                indexDetails: indexes,
                migrations: await this.getMigrationHistory(),
            };
        }
        catch (error) {
            this.logger.error('Failed to get schema info', error.message);
            return null;
        }
    }
};
exports.MigrationService = MigrationService;
exports.MigrationService = MigrationService = MigrationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __param(2, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_1.DataSource !== "undefined" && typeorm_1.DataSource) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _c : Object, typeof (_d = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _d : Object, typeof (_e = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _e : Object])
], MigrationService);


/***/ }),
/* 28 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.User = exports.UserStatus = void 0;
const typeorm_1 = __webpack_require__(22);
const refresh_token_entity_1 = __webpack_require__(29);
const password_reset_entity_1 = __webpack_require__(30);
const login_attempt_entity_1 = __webpack_require__(31);
const role_entity_1 = __webpack_require__(32);
const permission_entity_1 = __webpack_require__(33);
const user_role_entity_1 = __webpack_require__(34);
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
    UserStatus["PENDING"] = "pending";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
let User = class User {
    id;
    email;
    passwordHash;
    firstName;
    lastName;
    phoneNumber;
    jobTitle;
    department;
    tenantId;
    status;
    isActive;
    isEmailVerified;
    isLocked;
    lockedAt;
    lockReason;
    emailVerifiedAt;
    lastLoginAt;
    lastLoginIp;
    passwordChangedAt;
    failedLoginAttempts;
    lastFailedLoginAt;
    profilePicture;
    preferredLanguage;
    timezone;
    metadata;
    preferences;
    createdAt;
    updatedAt;
    deletedAt;
    refreshTokens;
    passwordResets;
    loginAttempts;
    roles;
    permissions;
    userRoles;
    tenant;
    get fullName() {
        return `${this.firstName} ${this.lastName}`.trim();
    }
    get initials() {
        return `${this.firstName.charAt(0)}${this.lastName.charAt(0)}`.toUpperCase();
    }
    isPasswordExpired() {
        if (!this.passwordChangedAt) {
            return false;
        }
        const maxAge = 90 * 24 * 60 * 60 * 1000;
        const now = new Date();
        const passwordAge = now.getTime() - this.passwordChangedAt.getTime();
        return passwordAge > maxAge;
    }
    lock(reason) {
        this.isLocked = true;
        this.lockedAt = new Date();
        this.lockReason = reason;
    }
    unlock() {
        this.isLocked = false;
        this.lockedAt = null;
        this.lockReason = null;
        this.failedLoginAttempts = 0;
        this.lastFailedLoginAt = null;
    }
    incrementFailedLoginAttempts() {
        this.failedLoginAttempts += 1;
        this.lastFailedLoginAt = new Date();
        if (this.failedLoginAttempts >= 5) {
            this.lock('Too many failed login attempts');
        }
    }
    resetFailedLoginAttempts() {
        this.failedLoginAttempts = 0;
        this.lastFailedLoginAt = null;
    }
    verifyEmail() {
        this.isEmailVerified = true;
        this.emailVerifiedAt = new Date();
    }
    activate() {
        this.isActive = true;
        this.status = UserStatus.ACTIVE;
    }
    deactivate() {
        this.isActive = false;
        this.status = UserStatus.INACTIVE;
    }
    suspend(reason) {
        this.isActive = false;
        this.status = UserStatus.SUSPENDED;
        if (reason && this.metadata) {
            this.metadata.suspensionReason = reason;
        }
    }
    hasRole(roleName) {
        return this.roles?.some(role => role.name === roleName && role.isActive) || false;
    }
    hasAnyRole(roleNames) {
        return roleNames.some(roleName => this.hasRole(roleName));
    }
    hasAllRoles(roleNames) {
        return roleNames.every(roleName => this.hasRole(roleName));
    }
    hasPermission(permissionName) {
        const hasDirectPermission = this.permissions?.some(permission => permission.name === permissionName && permission.isActive) || false;
        if (hasDirectPermission) {
            return true;
        }
        return this.roles?.some(role => role.isActive && role.hasPermission(permissionName)) || false;
    }
    hasAnyPermission(permissionNames) {
        return permissionNames.some(permissionName => this.hasPermission(permissionName));
    }
    hasAllPermissions(permissionNames) {
        return permissionNames.every(permissionName => this.hasPermission(permissionName));
    }
    getActiveRoles() {
        return this.roles?.filter(role => role.isActive) || [];
    }
    getActivePermissions() {
        const directPermissions = this.permissions?.filter(permission => permission.isActive) || [];
        const rolePermissions = this.getActiveRoles()
            .flatMap(role => role.permissions?.filter(permission => permission.isActive) || []);
        const allPermissions = [...directPermissions, ...rolePermissions];
        const uniquePermissions = allPermissions.filter((permission, index, self) => index === self.findIndex(p => p.id === permission.id));
        return uniquePermissions;
    }
    canAccess(resource, action) {
        const permissionName = `${resource}:${action}`;
        return this.hasPermission(permissionName);
    }
    isSuperAdmin() {
        return this.hasRole('super_admin') || this.hasRole('system_admin');
    }
    isTenantAdmin() {
        return this.hasRole('tenant_admin') || this.hasRole('admin');
    }
    isHRManager() {
        return this.hasRole('hr_manager') || this.hasRole('hr_admin');
    }
    softDelete() {
        this.deletedAt = new Date();
        this.isActive = false;
    }
    restore() {
        this.deletedAt = null;
        this.isActive = true;
        this.status = UserStatus.ACTIVE;
    }
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], User.prototype, "passwordHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "jobTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "department", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: UserStatus,
        default: UserStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], User.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isEmailVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isLocked", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], User.prototype, "lockedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "lockReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], User.prototype, "emailVerifiedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], User.prototype, "lastLoginAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "lastLoginIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], User.prototype, "passwordChangedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "failedLoginAttempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], User.prototype, "lastFailedLoginAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "profilePicture", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "preferredLanguage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "timezone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Record !== "undefined" && Record) === "function" ? _f : Object)
], User.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_g = typeof Record !== "undefined" && Record) === "function" ? _g : Object)
], User.prototype, "preferences", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_k = typeof Date !== "undefined" && Date) === "function" ? _k : Object)
], User.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => refresh_token_entity_1.RefreshToken, (refreshToken) => refreshToken.user),
    __metadata("design:type", Array)
], User.prototype, "refreshTokens", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => password_reset_entity_1.PasswordReset, (passwordReset) => passwordReset.user),
    __metadata("design:type", Array)
], User.prototype, "passwordResets", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => login_attempt_entity_1.LoginAttempt, (loginAttempt) => loginAttempt.user),
    __metadata("design:type", Array)
], User.prototype, "loginAttempts", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, (role) => role.users, {
        cascade: false,
    }),
    (0, typeorm_1.JoinTable)({
        name: 'user_roles',
        joinColumn: { name: 'userId', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'roleId', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], User.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => permission_entity_1.Permission, (permission) => permission.users, {
        cascade: false,
    }),
    (0, typeorm_1.JoinTable)({
        name: 'user_permissions',
        joinColumn: { name: 'userId', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'permissionId', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], User.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_role_entity_1.UserRole, (userRole) => userRole.user),
    __metadata("design:type", Array)
], User.prototype, "userRoles", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users'),
    (0, typeorm_1.Index)(['email', 'tenantId'], { unique: true }),
    (0, typeorm_1.Index)(['tenantId']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['createdAt'])
], User);


/***/ }),
/* 29 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RefreshToken = void 0;
const typeorm_1 = __webpack_require__(22);
const user_entity_1 = __webpack_require__(28);
let RefreshToken = class RefreshToken {
    id;
    token;
    userId;
    tenantId;
    expiresAt;
    isRevoked;
    revokedAt;
    revokedBy;
    revokedReason;
    ipAddress;
    userAgent;
    deviceId;
    deviceType;
    sessionId;
    metadata;
    createdAt;
    updatedAt;
    user;
    isExpired() {
        return new Date() > this.expiresAt;
    }
    isValid() {
        return !this.isRevoked && !this.isExpired();
    }
    revoke(revokedBy, reason) {
        this.isRevoked = true;
        this.revokedAt = new Date();
        this.revokedBy = revokedBy;
        this.revokedReason = reason;
    }
};
exports.RefreshToken = RefreshToken;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RefreshToken.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', unique: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], RefreshToken.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], RefreshToken.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], RefreshToken.prototype, "isRevoked", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], RefreshToken.prototype, "revokedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "revokedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "revokedReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "deviceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "deviceType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], RefreshToken.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], RefreshToken.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], RefreshToken.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], RefreshToken.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.refreshTokens, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", typeof (_f = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _f : Object)
], RefreshToken.prototype, "user", void 0);
exports.RefreshToken = RefreshToken = __decorate([
    (0, typeorm_1.Entity)('refresh_tokens'),
    (0, typeorm_1.Index)(['token'], { unique: true }),
    (0, typeorm_1.Index)(['userId', 'isRevoked']),
    (0, typeorm_1.Index)(['expiresAt'])
], RefreshToken);


/***/ }),
/* 30 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PasswordReset = void 0;
const typeorm_1 = __webpack_require__(22);
const user_entity_1 = __webpack_require__(28);
let PasswordReset = class PasswordReset {
    id;
    token;
    userId;
    tenantId;
    email;
    expiresAt;
    isUsed;
    usedAt;
    requestIpAddress;
    requestUserAgent;
    resetIpAddress;
    resetUserAgent;
    attemptCount;
    lastAttemptAt;
    metadata;
    createdAt;
    updatedAt;
    user;
    isExpired() {
        return new Date() > this.expiresAt;
    }
    isValid() {
        return !this.isUsed && !this.isExpired();
    }
    markAsUsed(ipAddress, userAgent) {
        this.isUsed = true;
        this.usedAt = new Date();
        this.resetIpAddress = ipAddress;
        this.resetUserAgent = userAgent;
    }
    incrementAttempt() {
        this.attemptCount += 1;
        this.lastAttemptAt = new Date();
    }
    hasExceededMaxAttempts(maxAttempts = 5) {
        return this.attemptCount >= maxAttempts;
    }
};
exports.PasswordReset = PasswordReset;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PasswordReset.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', unique: true }),
    __metadata("design:type", String)
], PasswordReset.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], PasswordReset.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], PasswordReset.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], PasswordReset.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], PasswordReset.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], PasswordReset.prototype, "isUsed", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], PasswordReset.prototype, "usedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true }),
    __metadata("design:type", String)
], PasswordReset.prototype, "requestIpAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PasswordReset.prototype, "requestUserAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true }),
    __metadata("design:type", String)
], PasswordReset.prototype, "resetIpAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PasswordReset.prototype, "resetUserAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], PasswordReset.prototype, "attemptCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], PasswordReset.prototype, "lastAttemptAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], PasswordReset.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], PasswordReset.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], PasswordReset.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.passwordResets, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", typeof (_g = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _g : Object)
], PasswordReset.prototype, "user", void 0);
exports.PasswordReset = PasswordReset = __decorate([
    (0, typeorm_1.Entity)('password_resets'),
    (0, typeorm_1.Index)(['token'], { unique: true }),
    (0, typeorm_1.Index)(['userId', 'isUsed']),
    (0, typeorm_1.Index)(['expiresAt'])
], PasswordReset);


/***/ }),
/* 31 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoginAttempt = exports.LoginAttemptStatus = void 0;
const typeorm_1 = __webpack_require__(22);
const user_entity_1 = __webpack_require__(28);
var LoginAttemptStatus;
(function (LoginAttemptStatus) {
    LoginAttemptStatus["SUCCESS"] = "success";
    LoginAttemptStatus["FAILED_INVALID_CREDENTIALS"] = "failed_invalid_credentials";
    LoginAttemptStatus["FAILED_ACCOUNT_LOCKED"] = "failed_account_locked";
    LoginAttemptStatus["FAILED_ACCOUNT_DISABLED"] = "failed_account_disabled";
    LoginAttemptStatus["FAILED_EMAIL_NOT_VERIFIED"] = "failed_email_not_verified";
    LoginAttemptStatus["FAILED_MFA_REQUIRED"] = "failed_mfa_required";
    LoginAttemptStatus["FAILED_MFA_INVALID"] = "failed_mfa_invalid";
    LoginAttemptStatus["FAILED_RATE_LIMITED"] = "failed_rate_limited";
    LoginAttemptStatus["FAILED_TENANT_INVALID"] = "failed_tenant_invalid";
    LoginAttemptStatus["FAILED_OTHER"] = "failed_other";
})(LoginAttemptStatus || (exports.LoginAttemptStatus = LoginAttemptStatus = {}));
let LoginAttempt = class LoginAttempt {
    id;
    email;
    userId;
    tenantId;
    status;
    ipAddress;
    userAgent;
    deviceId;
    deviceType;
    sessionId;
    correlationId;
    failureReason;
    location;
    countryCode;
    isSuspicious;
    riskScore;
    metadata;
    createdAt;
    user;
    isSuccessful() {
        return this.status === LoginAttemptStatus.SUCCESS;
    }
    isFailed() {
        return this.status !== LoginAttemptStatus.SUCCESS;
    }
    isHighRisk() {
        return this.riskScore >= 70;
    }
    markAsSuspicious(reason) {
        this.isSuspicious = true;
        if (reason && this.metadata) {
            this.metadata.suspiciousReason = reason;
        }
    }
};
exports.LoginAttempt = LoginAttempt;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], LoginAttempt.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: LoginAttemptStatus,
        default: LoginAttemptStatus.FAILED_OTHER,
    }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45 }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "deviceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "deviceType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "correlationId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "failureReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10, nullable: true }),
    __metadata("design:type", String)
], LoginAttempt.prototype, "countryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], LoginAttempt.prototype, "isSuspicious", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], LoginAttempt.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], LoginAttempt.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], LoginAttempt.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.loginAttempts, {
        onDelete: 'SET NULL',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", typeof (_c = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _c : Object)
], LoginAttempt.prototype, "user", void 0);
exports.LoginAttempt = LoginAttempt = __decorate([
    (0, typeorm_1.Entity)('login_attempts'),
    (0, typeorm_1.Index)(['email', 'status']),
    (0, typeorm_1.Index)(['ipAddress', 'status']),
    (0, typeorm_1.Index)(['userId', 'status']),
    (0, typeorm_1.Index)(['tenantId', 'status']),
    (0, typeorm_1.Index)(['createdAt'])
], LoginAttempt);


/***/ }),
/* 32 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Role = exports.RoleScope = exports.RoleType = void 0;
const typeorm_1 = __webpack_require__(22);
const user_entity_1 = __webpack_require__(28);
const permission_entity_1 = __webpack_require__(33);
const user_role_entity_1 = __webpack_require__(34);
var RoleType;
(function (RoleType) {
    RoleType["SYSTEM"] = "system";
    RoleType["TENANT"] = "tenant";
    RoleType["CUSTOM"] = "custom";
})(RoleType || (exports.RoleType = RoleType = {}));
var RoleScope;
(function (RoleScope) {
    RoleScope["GLOBAL"] = "global";
    RoleScope["TENANT"] = "tenant";
    RoleScope["DEPARTMENT"] = "department";
    RoleScope["TEAM"] = "team";
})(RoleScope || (exports.RoleScope = RoleScope = {}));
let Role = class Role {
    id;
    name;
    description;
    type;
    scope;
    tenantId;
    departmentId;
    teamId;
    isActive;
    isDefault;
    isSystemRole;
    priority;
    metadata;
    createdBy;
    updatedBy;
    createdAt;
    updatedAt;
    deletedAt;
    users;
    permissions;
    userRoles;
    get displayName() {
        return this.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    hasPermission(permissionName) {
        return this.permissions?.some(permission => permission.name === permissionName && permission.isActive) || false;
    }
    hasAnyPermission(permissionNames) {
        return permissionNames.some(permissionName => this.hasPermission(permissionName));
    }
    hasAllPermissions(permissionNames) {
        return permissionNames.every(permissionName => this.hasPermission(permissionName));
    }
    addPermission(permission) {
        if (!this.permissions) {
            this.permissions = [];
        }
        const exists = this.permissions.some(p => p.id === permission.id);
        if (!exists) {
            this.permissions.push(permission);
        }
    }
    removePermission(permissionId) {
        if (this.permissions) {
            this.permissions = this.permissions.filter(p => p.id !== permissionId);
        }
    }
    activate() {
        this.isActive = true;
    }
    deactivate() {
        this.isActive = false;
    }
    softDelete() {
        this.deletedAt = new Date();
        this.isActive = false;
    }
    restore() {
        this.deletedAt = null;
        this.isActive = true;
    }
};
exports.Role = Role;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Role.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Role.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RoleType,
        default: RoleType.CUSTOM,
    }),
    __metadata("design:type", String)
], Role.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: RoleScope,
        default: RoleScope.TENANT,
    }),
    __metadata("design:type", String)
], Role.prototype, "scope", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "departmentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "teamId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], Role.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Role.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Role.prototype, "isSystemRole", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Role.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], Role.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Role.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Role.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Role.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => user_entity_1.User, (user) => user.roles),
    __metadata("design:type", Array)
], Role.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => permission_entity_1.Permission, (permission) => permission.roles, {
        cascade: true,
    }),
    (0, typeorm_1.JoinTable)({
        name: 'role_permissions',
        joinColumn: { name: 'roleId', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'permissionId', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], Role.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_role_entity_1.UserRole, (userRole) => userRole.role),
    __metadata("design:type", Array)
], Role.prototype, "userRoles", void 0);
exports.Role = Role = __decorate([
    (0, typeorm_1.Entity)('roles'),
    (0, typeorm_1.Index)(['name', 'tenantId'], { unique: true }),
    (0, typeorm_1.Index)(['tenantId']),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['scope']),
    (0, typeorm_1.Index)(['isActive'])
], Role);


/***/ }),
/* 33 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Permission = exports.PermissionResource = exports.PermissionAction = exports.PermissionType = void 0;
const typeorm_1 = __webpack_require__(22);
const role_entity_1 = __webpack_require__(32);
const user_entity_1 = __webpack_require__(28);
var PermissionType;
(function (PermissionType) {
    PermissionType["SYSTEM"] = "system";
    PermissionType["TENANT"] = "tenant";
    PermissionType["RESOURCE"] = "resource";
    PermissionType["FEATURE"] = "feature";
})(PermissionType || (exports.PermissionType = PermissionType = {}));
var PermissionAction;
(function (PermissionAction) {
    PermissionAction["CREATE"] = "create";
    PermissionAction["READ"] = "read";
    PermissionAction["UPDATE"] = "update";
    PermissionAction["DELETE"] = "delete";
    PermissionAction["EXECUTE"] = "execute";
    PermissionAction["MANAGE"] = "manage";
    PermissionAction["VIEW"] = "view";
    PermissionAction["EDIT"] = "edit";
    PermissionAction["APPROVE"] = "approve";
    PermissionAction["REJECT"] = "reject";
})(PermissionAction || (exports.PermissionAction = PermissionAction = {}));
var PermissionResource;
(function (PermissionResource) {
    PermissionResource["USER"] = "user";
    PermissionResource["ROLE"] = "role";
    PermissionResource["PERMISSION"] = "permission";
    PermissionResource["TENANT"] = "tenant";
    PermissionResource["EMPLOYEE"] = "employee";
    PermissionResource["DEPARTMENT"] = "department";
    PermissionResource["TEAM"] = "team";
    PermissionResource["PAYROLL"] = "payroll";
    PermissionResource["ATTENDANCE"] = "attendance";
    PermissionResource["LEAVE"] = "leave";
    PermissionResource["PERFORMANCE"] = "performance";
    PermissionResource["RECRUITMENT"] = "recruitment";
    PermissionResource["TRAINING"] = "training";
    PermissionResource["DOCUMENT"] = "document";
    PermissionResource["REPORT"] = "report";
    PermissionResource["SETTING"] = "setting";
    PermissionResource["AUDIT"] = "audit";
    PermissionResource["SYSTEM"] = "system";
})(PermissionResource || (exports.PermissionResource = PermissionResource = {}));
let Permission = class Permission {
    id;
    name;
    description;
    type;
    action;
    resource;
    module;
    isActive;
    isSystemPermission;
    priority;
    conditions;
    metadata;
    createdBy;
    updatedBy;
    createdAt;
    updatedAt;
    deletedAt;
    roles;
    users;
    get displayName() {
        return this.name.replace(/[_:]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    get fullName() {
        return `${this.resource}:${this.action}`;
    }
    matches(resource, action) {
        return this.resource === resource && this.action === action;
    }
    isApplicableToTenant(tenantId) {
        if (this.type === PermissionType.SYSTEM) {
            return true;
        }
        if (this.type === PermissionType.TENANT && tenantId) {
            return true;
        }
        return false;
    }
    checkConditions(context) {
        if (!this.conditions) {
            return true;
        }
        for (const [key, value] of Object.entries(this.conditions)) {
            if (context[key] !== value) {
                return false;
            }
        }
        return true;
    }
    activate() {
        this.isActive = true;
    }
    deactivate() {
        this.isActive = false;
    }
    softDelete() {
        this.deletedAt = new Date();
        this.isActive = false;
    }
    restore() {
        this.deletedAt = null;
        this.isActive = true;
    }
};
exports.Permission = Permission;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Permission.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Permission.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PermissionType,
        default: PermissionType.RESOURCE,
    }),
    __metadata("design:type", String)
], Permission.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PermissionAction,
    }),
    __metadata("design:type", String)
], Permission.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PermissionResource,
    }),
    __metadata("design:type", String)
], Permission.prototype, "resource", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "module", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], Permission.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Permission.prototype, "isSystemPermission", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Permission.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], Permission.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], Permission.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Permission.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Permission.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Permission.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Permission.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, (role) => role.permissions),
    __metadata("design:type", Array)
], Permission.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => user_entity_1.User, (user) => user.permissions),
    __metadata("design:type", Array)
], Permission.prototype, "users", void 0);
exports.Permission = Permission = __decorate([
    (0, typeorm_1.Entity)('permissions'),
    (0, typeorm_1.Index)(['name'], { unique: true }),
    (0, typeorm_1.Index)(['resource']),
    (0, typeorm_1.Index)(['action']),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['isActive'])
], Permission);


/***/ }),
/* 34 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserRole = exports.UserRoleStatus = void 0;
const typeorm_1 = __webpack_require__(22);
const user_entity_1 = __webpack_require__(28);
const role_entity_1 = __webpack_require__(32);
var UserRoleStatus;
(function (UserRoleStatus) {
    UserRoleStatus["ACTIVE"] = "active";
    UserRoleStatus["INACTIVE"] = "inactive";
    UserRoleStatus["PENDING"] = "pending";
    UserRoleStatus["EXPIRED"] = "expired";
})(UserRoleStatus || (exports.UserRoleStatus = UserRoleStatus = {}));
let UserRole = class UserRole {
    id;
    userId;
    roleId;
    tenantId;
    status;
    expiresAt;
    activatedAt;
    assignedBy;
    assignmentReason;
    revokedBy;
    revokedAt;
    revocationReason;
    conditions;
    metadata;
    createdAt;
    updatedAt;
    user;
    role;
    isActive() {
        if (this.status !== UserRoleStatus.ACTIVE) {
            return false;
        }
        if (this.expiresAt && this.expiresAt < new Date()) {
            return false;
        }
        return true;
    }
    isExpired() {
        return this.expiresAt ? this.expiresAt < new Date() : false;
    }
    activate(activatedBy) {
        this.status = UserRoleStatus.ACTIVE;
        this.activatedAt = new Date();
        if (activatedBy) {
            this.assignedBy = activatedBy;
        }
    }
    deactivate() {
        this.status = UserRoleStatus.INACTIVE;
    }
    revoke(revokedBy, reason) {
        this.status = UserRoleStatus.INACTIVE;
        this.revokedAt = new Date();
        if (revokedBy) {
            this.revokedBy = revokedBy;
        }
        if (reason) {
            this.revocationReason = reason;
        }
    }
    extend(newExpiryDate) {
        this.expiresAt = newExpiryDate;
    }
    checkConditions(context) {
        if (!this.conditions) {
            return true;
        }
        for (const [key, value] of Object.entries(this.conditions)) {
            if (context[key] !== value) {
                return false;
            }
        }
        return true;
    }
};
exports.UserRole = UserRole;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserRole.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], UserRole.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], UserRole.prototype, "roleId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], UserRole.prototype, "tenantId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: UserRoleStatus,
        default: UserRoleStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], UserRole.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], UserRole.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], UserRole.prototype, "activatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], UserRole.prototype, "assignedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], UserRole.prototype, "assignmentReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], UserRole.prototype, "revokedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], UserRole.prototype, "revokedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], UserRole.prototype, "revocationReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], UserRole.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], UserRole.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], UserRole.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp with time zone' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], UserRole.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.userRoles, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", typeof (_h = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _h : Object)
], UserRole.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => role_entity_1.Role, (role) => role.userRoles, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'roleId' }),
    __metadata("design:type", typeof (_j = typeof role_entity_1.Role !== "undefined" && role_entity_1.Role) === "function" ? _j : Object)
], UserRole.prototype, "role", void 0);
exports.UserRole = UserRole = __decorate([
    (0, typeorm_1.Entity)('user_roles'),
    (0, typeorm_1.Index)(['userId', 'roleId'], { unique: true }),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['roleId']),
    (0, typeorm_1.Index)(['tenantId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['expiresAt'])
], UserRole);


/***/ }),
/* 35 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CommonModule = void 0;
const common_1 = __webpack_require__(2);
const core_1 = __webpack_require__(1);
const global_exception_filter_1 = __webpack_require__(36);
const logging_interceptor_1 = __webpack_require__(37);
const tenant_interceptor_1 = __webpack_require__(39);
const transform_interceptor_1 = __webpack_require__(41);
const correlation_service_1 = __webpack_require__(23);
const enhanced_logger_service_1 = __webpack_require__(43);
const utils_1 = __webpack_require__(44);
let CommonModule = class CommonModule {
};
exports.CommonModule = CommonModule;
exports.CommonModule = CommonModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [
            {
                provide: core_1.APP_FILTER,
                useClass: global_exception_filter_1.GlobalExceptionFilter,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logging_interceptor_1.LoggingInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: tenant_interceptor_1.TenantInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: transform_interceptor_1.TransformInterceptor,
            },
            correlation_service_1.CorrelationService,
            enhanced_logger_service_1.EnhancedLoggerService,
            utils_1.ValidationUtil,
            utils_1.ErrorHandler,
            utils_1.ResponseUtil,
            utils_1.DateUtil,
            utils_1.StringUtil,
        ],
        exports: [
            correlation_service_1.CorrelationService,
            enhanced_logger_service_1.EnhancedLoggerService,
            utils_1.ValidationUtil,
            utils_1.ErrorHandler,
            utils_1.ResponseUtil,
            utils_1.DateUtil,
            utils_1.StringUtil,
        ],
    })
], CommonModule);


/***/ }),
/* 36 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var GlobalExceptionFilter_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.GlobalExceptionFilter = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(22);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const correlation_service_1 = __webpack_require__(23);
let GlobalExceptionFilter = GlobalExceptionFilter_1 = class GlobalExceptionFilter {
    winstonLogger;
    correlationService;
    logger = new common_1.Logger(GlobalExceptionFilter_1.name);
    constructor(winstonLogger, correlationService) {
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const correlationId = this.correlationService.getCorrelationId();
        const tenantId = request.headers['x-tenant-id'];
        const userId = request.user?.id;
        let status;
        let message;
        let error;
        let details;
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
                error = exception.name;
            }
            else if (typeof exceptionResponse === 'object') {
                message = exceptionResponse.message || exception.message;
                error = exceptionResponse.error || exception.name;
                details = exceptionResponse.details;
            }
        }
        else if (exception instanceof typeorm_1.QueryFailedError) {
            status = common_1.HttpStatus.BAD_REQUEST;
            message = 'Database operation failed';
            error = 'DatabaseError';
            this.winstonLogger.error('Database error', {
                context: GlobalExceptionFilter_1.name,
                correlationId,
                tenantId,
                userId,
                error: exception.message,
                query: exception.query,
                parameters: exception.parameters,
            });
            if (process.env.NODE_ENV === 'development') {
                details = {
                    query: exception.query,
                    parameters: exception.parameters,
                };
            }
        }
        else if (exception instanceof Error) {
            status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
            message = process.env.NODE_ENV === 'production'
                ? 'Internal server error'
                : exception.message;
            error = exception.name;
            if (process.env.NODE_ENV === 'development') {
                details = {
                    stack: exception.stack,
                };
            }
        }
        else {
            status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
            message = 'Internal server error';
            error = 'UnknownError';
        }
        const errorResponse = {
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method,
            message,
            error,
            correlationId,
            tenantId,
        };
        if (details) {
            errorResponse.details = details;
        }
        const logLevel = status >= 500 ? 'error' : 'warn';
        const logMessage = `${request.method} ${request.url} - ${status} - ${message}`;
        this.winstonLogger.log(logLevel, logMessage, {
            context: GlobalExceptionFilter_1.name,
            correlationId,
            tenantId,
            userId,
            statusCode: status,
            path: request.url,
            method: request.method,
            userAgent: request.headers['user-agent'],
            ip: request.ip,
            exception: exception instanceof Error ? {
                name: exception.name,
                message: exception.message,
                stack: exception.stack,
            } : exception,
        });
        response.status(status).json(errorResponse);
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = GlobalExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _a : Object, typeof (_b = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _b : Object])
], GlobalExceptionFilter);


/***/ }),
/* 37 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LoggingInterceptor_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoggingInterceptor = void 0;
const common_1 = __webpack_require__(2);
const operators_1 = __webpack_require__(38);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const correlation_service_1 = __webpack_require__(23);
let LoggingInterceptor = LoggingInterceptor_1 = class LoggingInterceptor {
    winstonLogger;
    correlationService;
    logger = new common_1.Logger(LoggingInterceptor_1.name);
    constructor(winstonLogger, correlationService) {
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
    }
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        const { method, url, headers, body, query, params } = request;
        const userAgent = headers['user-agent'] || '';
        const ip = request.ip;
        const startTime = Date.now();
        const correlationId = this.correlationService.getCorrelationId() ||
            this.correlationService.generateCorrelationId();
        response.setHeader('X-Correlation-ID', correlationId);
        const tenantId = headers['x-tenant-id'];
        const userId = request.user?.id;
        this.winstonLogger.info(`Incoming ${method} ${url}`, {
            context: LoggingInterceptor_1.name,
            correlationId,
            tenantId,
            userId,
            method,
            url,
            userAgent,
            ip,
            query: Object.keys(query).length > 0 ? query : undefined,
            params: Object.keys(params).length > 0 ? params : undefined,
            body: this.shouldLogBody(url, method) ? this.sanitizeBody(body) : undefined,
        });
        return next.handle().pipe((0, operators_1.tap)({
            next: (data) => {
                const duration = Date.now() - startTime;
                const statusCode = response.statusCode;
                this.winstonLogger.info(`${method} ${url} - ${statusCode} - ${duration}ms`, {
                    context: LoggingInterceptor_1.name,
                    correlationId,
                    tenantId,
                    userId,
                    method,
                    url,
                    statusCode,
                    duration,
                    responseSize: JSON.stringify(data).length,
                });
            },
            error: (error) => {
                const duration = Date.now() - startTime;
                const statusCode = response.statusCode || 500;
                this.winstonLogger.error(`${method} ${url} - ${statusCode} - ${duration}ms - ERROR`, {
                    context: LoggingInterceptor_1.name,
                    correlationId,
                    tenantId,
                    userId,
                    method,
                    url,
                    statusCode,
                    duration,
                    error: {
                        name: error.name,
                        message: error.message,
                        stack: error.stack,
                    },
                });
            },
        }));
    }
    shouldLogBody(url, method) {
        const sensitiveEndpoints = [
            '/auth/login',
            '/auth/register',
            '/auth/reset-password',
            '/auth/change-password',
            '/users/password',
        ];
        if (method === 'GET') {
            return false;
        }
        if (sensitiveEndpoints.some(endpoint => url.includes(endpoint))) {
            return false;
        }
        return process.env.NODE_ENV === 'development';
    }
    sanitizeBody(body) {
        if (!body || typeof body !== 'object') {
            return body;
        }
        const sensitiveFields = [
            'password',
            'confirmPassword',
            'currentPassword',
            'newPassword',
            'token',
            'refreshToken',
            'accessToken',
            'secret',
            'apiKey',
            'privateKey',
            'ssn',
            'socialSecurityNumber',
            'creditCard',
            'bankAccount',
        ];
        const sanitized = { ...body };
        for (const field of sensitiveFields) {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        }
        return sanitized;
    }
};
exports.LoggingInterceptor = LoggingInterceptor;
exports.LoggingInterceptor = LoggingInterceptor = LoggingInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _a : Object, typeof (_b = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _b : Object])
], LoggingInterceptor);


/***/ }),
/* 38 */
/***/ ((module) => {

module.exports = require("rxjs/operators");

/***/ }),
/* 39 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TenantInterceptor = void 0;
const common_1 = __webpack_require__(2);
const core_1 = __webpack_require__(1);
const tenant_required_decorator_1 = __webpack_require__(40);
let TenantInterceptor = class TenantInterceptor {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const isTenantRequired = this.reflector.getAllAndOverride(tenant_required_decorator_1.TENANT_REQUIRED_KEY, [context.getHandler(), context.getClass()]);
        if (!isTenantRequired) {
            return next.handle();
        }
        const tenantId = this.extractTenantId(request);
        if (!tenantId) {
            throw new common_1.BadRequestException('Tenant ID is required');
        }
        if (!this.isValidTenantId(tenantId)) {
            throw new common_1.BadRequestException('Invalid tenant ID format');
        }
        const tenantContext = {
            tenantId,
            tenantSchema: this.getTenantSchema(tenantId),
            tenantDatabase: this.getTenantDatabase(tenantId),
        };
        request.tenantContext = tenantContext;
        request.tenantId = tenantId;
        return next.handle();
    }
    extractTenantId(request) {
        let tenantId = request.headers['x-tenant-id'];
        if (tenantId) {
            return tenantId;
        }
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            tenantId = this.extractTenantFromJWT(authHeader.substring(7));
            if (tenantId) {
                return tenantId;
            }
        }
        const host = request.headers.host;
        if (host) {
            tenantId = this.extractTenantFromSubdomain(host);
            if (tenantId) {
                return tenantId;
            }
        }
        tenantId = request.query.tenantId;
        if (tenantId) {
            return tenantId;
        }
        return null;
    }
    extractTenantFromJWT(token) {
        try {
            const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
            return payload.tenantId || payload.tenant_id || null;
        }
        catch (error) {
            return null;
        }
    }
    extractTenantFromSubdomain(host) {
        const parts = host.split('.');
        if (parts.length >= 3) {
            const subdomain = parts[0];
            const reservedSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp'];
            if (!reservedSubdomains.includes(subdomain.toLowerCase())) {
                return subdomain;
            }
        }
        return null;
    }
    isValidTenantId(tenantId) {
        const tenantIdRegex = /^[a-zA-Z0-9_-]{3,50}$/;
        return tenantIdRegex.test(tenantId);
    }
    getTenantSchema(tenantId) {
        const sanitizedTenantId = tenantId.toLowerCase().replace(/[^a-z0-9_]/g, '_');
        return `tenant_${sanitizedTenantId}`;
    }
    getTenantDatabase(tenantId) {
        const sanitizedTenantId = tenantId.toLowerCase().replace(/[^a-z0-9_]/g, '_');
        return `peoplenest_${sanitizedTenantId}`;
    }
};
exports.TenantInterceptor = TenantInterceptor;
exports.TenantInterceptor = TenantInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], TenantInterceptor);


/***/ }),
/* 40 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TenantOptional = exports.TenantRequired = exports.TENANT_REQUIRED_KEY = void 0;
const common_1 = __webpack_require__(2);
exports.TENANT_REQUIRED_KEY = 'tenantRequired';
const TenantRequired = () => (0, common_1.SetMetadata)(exports.TENANT_REQUIRED_KEY, true);
exports.TenantRequired = TenantRequired;
const TenantOptional = () => (0, common_1.SetMetadata)(exports.TENANT_REQUIRED_KEY, false);
exports.TenantOptional = TenantOptional;


/***/ }),
/* 41 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TransformInterceptor = void 0;
const common_1 = __webpack_require__(2);
const operators_1 = __webpack_require__(38);
const core_1 = __webpack_require__(1);
const skip_transform_decorator_1 = __webpack_require__(42);
let TransformInterceptor = class TransformInterceptor {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    intercept(context, next) {
        const response = context.switchToHttp().getResponse();
        const request = context.switchToHttp().getRequest();
        const skipTransform = this.reflector.getAllAndOverride(skip_transform_decorator_1.SKIP_TRANSFORM_KEY, [context.getHandler(), context.getClass()]);
        if (skipTransform) {
            return next.handle();
        }
        return next.handle().pipe((0, operators_1.map)((data) => {
            if (this.isAlreadyTransformed(data)) {
                return data;
            }
            const correlationId = response.getHeader('X-Correlation-ID');
            const apiResponse = {
                success: true,
                data,
                timestamp: new Date().toISOString(),
                correlationId,
            };
            if (this.isPaginatedData(data)) {
                apiResponse.data = data.items;
                apiResponse.pagination = {
                    page: data.page,
                    limit: data.limit,
                    total: data.total,
                    totalPages: Math.ceil(data.total / data.limit),
                    hasNext: data.page < Math.ceil(data.total / data.limit),
                    hasPrev: data.page > 1,
                };
            }
            if (this.hasMetadata(data)) {
                apiResponse.data = data.data;
                apiResponse.meta = data.meta;
                apiResponse.message = data.message;
            }
            if (typeof data === 'string') {
                apiResponse.message = data;
                apiResponse.data = null;
            }
            if (typeof data === 'boolean') {
                apiResponse.data = data;
                apiResponse.message = data ? 'Operation completed successfully' : 'Operation failed';
            }
            if (data === null || data === undefined) {
                apiResponse.data = null;
                apiResponse.message = 'No data found';
            }
            return apiResponse;
        }));
    }
    isAlreadyTransformed(data) {
        return (data &&
            typeof data === 'object' &&
            'success' in data &&
            'timestamp' in data);
    }
    isPaginatedData(data) {
        return (data &&
            typeof data === 'object' &&
            'items' in data &&
            'page' in data &&
            'limit' in data &&
            'total' in data &&
            Array.isArray(data.items));
    }
    hasMetadata(data) {
        return (data &&
            typeof data === 'object' &&
            'data' in data &&
            ('meta' in data || 'message' in data));
    }
};
exports.TransformInterceptor = TransformInterceptor;
exports.TransformInterceptor = TransformInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], TransformInterceptor);


/***/ }),
/* 42 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SkipTransform = exports.SKIP_TRANSFORM_KEY = void 0;
const common_1 = __webpack_require__(2);
exports.SKIP_TRANSFORM_KEY = 'skipTransform';
const SkipTransform = () => (0, common_1.SetMetadata)(exports.SKIP_TRANSFORM_KEY, true);
exports.SkipTransform = SkipTransform;


/***/ }),
/* 43 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EnhancedLoggerService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EnhancedLoggerService = exports.LogLevel = void 0;
const common_1 = __webpack_require__(2);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const correlation_service_1 = __webpack_require__(23);
var LogLevel;
(function (LogLevel) {
    LogLevel["ERROR"] = "error";
    LogLevel["WARN"] = "warn";
    LogLevel["INFO"] = "info";
    LogLevel["HTTP"] = "http";
    LogLevel["VERBOSE"] = "verbose";
    LogLevel["DEBUG"] = "debug";
    LogLevel["SILLY"] = "silly";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
let EnhancedLoggerService = EnhancedLoggerService_1 = class EnhancedLoggerService {
    winstonLogger;
    correlationService;
    constructor(winstonLogger, correlationService) {
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
    }
    error(message, error, context) {
        this.log(LogLevel.ERROR, message, { ...context, error: this.formatError(error) });
    }
    warn(message, context) {
        this.log(LogLevel.WARN, message, context);
    }
    log(levelOrMessage, messageOrContext, context) {
        if (typeof levelOrMessage === 'string') {
            this.writeLog(LogLevel.INFO, levelOrMessage, messageOrContext);
        }
        else {
            this.writeLog(levelOrMessage, messageOrContext, context);
        }
    }
    debug(message, context) {
        this.log(LogLevel.DEBUG, message, context);
    }
    verbose(message, context) {
        this.log(LogLevel.VERBOSE, message, context);
    }
    http(message, context) {
        this.log(LogLevel.HTTP, message, context);
    }
    business(event, data, context) {
        this.log(LogLevel.INFO, `Business Event: ${event}`, {
            ...context,
            eventType: 'business',
            event,
            data,
        });
    }
    security(event, data, context) {
        this.log(LogLevel.WARN, `Security Event: ${event}`, {
            ...context,
            eventType: 'security',
            event,
            data,
        });
    }
    audit(action, resource, data, context) {
        this.log(LogLevel.INFO, `Audit: ${action} ${resource}`, {
            ...context,
            eventType: 'audit',
            action,
            resource,
            data,
        });
    }
    performance(operation, duration, context) {
        const level = duration > 5000 ? LogLevel.WARN : LogLevel.DEBUG;
        this.log(level, `Performance: ${operation} took ${duration}ms`, {
            ...context,
            eventType: 'performance',
            operation,
            duration,
        });
    }
    database(operation, table, duration, context) {
        this.log(LogLevel.DEBUG, `Database: ${operation}${table ? ` on ${table}` : ''}`, {
            ...context,
            eventType: 'database',
            operation,
            table,
            duration,
        });
    }
    external(service, operation, duration, statusCode, context) {
        const level = statusCode && statusCode >= 400 ? LogLevel.WARN : LogLevel.DEBUG;
        this.log(level, `External: ${service} ${operation}`, {
            ...context,
            eventType: 'external',
            service,
            operation,
            duration,
            statusCode,
        });
    }
    userAction(action, userId, data, context) {
        this.log(LogLevel.INFO, `User Action: ${action}`, {
            ...context,
            eventType: 'userAction',
            action,
            userId: userId || context?.userId,
            data,
        });
    }
    system(event, data, context) {
        this.log(LogLevel.INFO, `System: ${event}`, {
            ...context,
            eventType: 'system',
            event,
            data,
        });
    }
    child(additionalContext) {
        const childLogger = new EnhancedLoggerService_1(this.winstonLogger, this.correlationService);
        childLogger.defaultContext = additionalContext;
        return childLogger;
    }
    startTimer(operation) {
        const startTime = Date.now();
        return () => {
            const duration = Date.now() - startTime;
            this.performance(operation, duration);
        };
    }
    writeLog(level, message, context) {
        const correlationContext = this.correlationService.getContext();
        const defaultContext = this.defaultContext || {};
        const logContext = {
            ...defaultContext,
            ...correlationContext,
            ...context,
            timestamp: new Date().toISOString(),
            level,
        };
        Object.keys(logContext).forEach(key => {
            if (logContext[key] === undefined) {
                delete logContext[key];
            }
        });
        this.winstonLogger.log(level, message, logContext);
    }
    formatError(error) {
        if (!error)
            return undefined;
        if (error instanceof Error) {
            return {
                name: error.name,
                message: error.message,
                stack: error.stack,
                ...error.code && { code: error.code },
                ...error.statusCode && { statusCode: error.statusCode },
            };
        }
        if (typeof error === 'object') {
            return {
                ...error,
                toString: error.toString?.(),
            };
        }
        return { value: error };
    }
    flush() {
        this.winstonLogger.end?.();
    }
    setLevel(level) {
        this.winstonLogger.level = level;
    }
    getLevel() {
        return this.winstonLogger.level;
    }
    isLevelEnabled(level) {
        return this.winstonLogger.isLevelEnabled(level);
    }
    addMetadata(metadata) {
        this.winstonLogger.defaultMeta = {
            ...this.winstonLogger.defaultMeta,
            ...metadata,
        };
    }
    removeMetadata(keys) {
        if (this.winstonLogger.defaultMeta) {
            keys.forEach(key => {
                delete this.winstonLogger.defaultMeta[key];
            });
        }
    }
    structured(level, message, data) {
        this.log(level, message, {
            ...data,
            structured: true,
        });
    }
    custom(level, format, ...args) {
        const message = this.formatMessage(format, args);
        this.log(level, message);
    }
    formatMessage(format, args) {
        return format.replace(/%[sdj%]/g, (match) => {
            const arg = args.shift();
            switch (match) {
                case '%s': return String(arg);
                case '%d': return String(Number(arg));
                case '%j': return JSON.stringify(arg);
                case '%%': return '%';
                default: return match;
            }
        });
    }
};
exports.EnhancedLoggerService = EnhancedLoggerService;
exports.EnhancedLoggerService = EnhancedLoggerService = EnhancedLoggerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _a : Object, typeof (_b = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _b : Object])
], EnhancedLoggerService);


/***/ }),
/* 44 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppError = exports.ErrorCode = void 0;
__exportStar(__webpack_require__(45), exports);
__exportStar(__webpack_require__(48), exports);
__exportStar(__webpack_require__(49), exports);
__exportStar(__webpack_require__(50), exports);
__exportStar(__webpack_require__(51), exports);
var error_handler_util_1 = __webpack_require__(48);
Object.defineProperty(exports, "ErrorCode", ({ enumerable: true, get: function () { return error_handler_util_1.ErrorCode; } }));
Object.defineProperty(exports, "AppError", ({ enumerable: true, get: function () { return error_handler_util_1.AppError; } }));


/***/ }),
/* 45 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ValidationUtil = void 0;
const common_1 = __webpack_require__(2);
const class_validator_1 = __webpack_require__(46);
const class_transformer_1 = __webpack_require__(47);
class ValidationUtil {
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    static isValidPhoneNumber(phone) {
        const phoneRegex = /^\+?[1-9]\d{1,14}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
    static validatePasswordStrength(password) {
        const errors = [];
        let score = 0;
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        else {
            score += 1;
        }
        if (password.length > 128) {
            errors.push('Password must not exceed 128 characters');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        else {
            score += 1;
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        else {
            score += 1;
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        else {
            score += 1;
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        else {
            score += 1;
        }
        const commonPatterns = [
            /(.)\1{2,}/,
            /123456|654321|abcdef|qwerty|password/i,
        ];
        for (const pattern of commonPatterns) {
            if (pattern.test(password)) {
                errors.push('Password contains common patterns and is not secure');
                score = Math.max(0, score - 1);
                break;
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
            score: Math.min(5, score),
        };
    }
    static isValidUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }
    static isValidTenantId(tenantId) {
        const tenantRegex = /^[a-zA-Z0-9_-]{3,50}$/;
        return tenantRegex.test(tenantId);
    }
    static isValidEmployeeId(employeeId) {
        const employeeRegex = /^[a-zA-Z0-9]{3,20}$/;
        return employeeRegex.test(employeeId);
    }
    static isValidDateString(dateString) {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && dateString === date.toISOString().split('T')[0];
    }
    static isValidDateRange(startDate, endDate) {
        if (!this.isValidDateString(startDate) || !this.isValidDateString(endDate)) {
            return false;
        }
        return new Date(startDate) <= new Date(endDate);
    }
    static isValidCurrencyCode(currency) {
        const validCurrencies = [
            'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
            'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'ZAR', 'BRL', 'INR', 'KRW', 'PLN',
        ];
        return validCurrencies.includes(currency.toUpperCase());
    }
    static isValidSalaryAmount(amount, currency = 'USD') {
        if (amount < 0)
            return false;
        const limits = {
            USD: { min: 0, max: 10000000 },
            EUR: { min: 0, max: 10000000 },
            GBP: { min: 0, max: 8000000 },
            JPY: { min: 0, max: 1000000000 },
            INR: { min: 0, max: 750000000 },
        };
        const limit = limits[currency] || limits.USD;
        return amount >= limit.min && amount <= limit.max;
    }
    static sanitizeString(input) {
        if (!input || typeof input !== 'string')
            return '';
        return input
            .trim()
            .replace(/[<>]/g, '')
            .replace(/[\x00-\x1F\x7F]/g, '')
            .substring(0, 1000);
    }
    static async validateAndTransform(dtoClass, data) {
        const dto = (0, class_transformer_1.plainToClass)(dtoClass, data);
        const errors = await (0, class_validator_1.validate)(dto);
        if (errors.length > 0) {
            const errorMessages = this.formatValidationErrors(errors);
            throw new common_1.BadRequestException({
                message: 'Validation failed',
                errors: errorMessages,
            });
        }
        return dto;
    }
    static formatValidationErrors(errors) {
        const messages = [];
        for (const error of errors) {
            if (error.constraints) {
                messages.push(...Object.values(error.constraints));
            }
            if (error.children && error.children.length > 0) {
                const childMessages = this.formatValidationErrors(error.children);
                messages.push(...childMessages.map(msg => `${error.property}.${msg}`));
            }
        }
        return messages;
    }
    static validateFileUpload(file, options = {}) {
        const errors = [];
        const { maxSize = 10 * 1024 * 1024, allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'], allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf'], } = options;
        if (file.size > maxSize) {
            errors.push(`File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`);
        }
        if (!allowedMimeTypes.includes(file.mimetype)) {
            errors.push(`File type ${file.mimetype} is not allowed`);
        }
        const extension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
        if (!allowedExtensions.includes(extension)) {
            errors.push(`File extension ${extension} is not allowed`);
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    static isValidJSON(jsonString) {
        try {
            JSON.parse(jsonString);
            return true;
        }
        catch {
            return false;
        }
    }
    static isValidURL(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
    static isValidIPAddress(ip) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    static isValidTimeFormat(time) {
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
        return timeRegex.test(time);
    }
    static isValidTimezone(timezone) {
        try {
            Intl.DateTimeFormat(undefined, { timeZone: timezone });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.ValidationUtil = ValidationUtil;


/***/ }),
/* 46 */
/***/ ((module) => {

module.exports = require("class-validator");

/***/ }),
/* 47 */
/***/ ((module) => {

module.exports = require("class-transformer");

/***/ }),
/* 48 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ErrorHandler = exports.AppError = exports.ErrorCode = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(22);
var ErrorCode;
(function (ErrorCode) {
    ErrorCode["INVALID_CREDENTIALS"] = "INVALID_CREDENTIALS";
    ErrorCode["TOKEN_EXPIRED"] = "TOKEN_EXPIRED";
    ErrorCode["TOKEN_INVALID"] = "TOKEN_INVALID";
    ErrorCode["INSUFFICIENT_PERMISSIONS"] = "INSUFFICIENT_PERMISSIONS";
    ErrorCode["ACCOUNT_LOCKED"] = "ACCOUNT_LOCKED";
    ErrorCode["ACCOUNT_DISABLED"] = "ACCOUNT_DISABLED";
    ErrorCode["VALIDATION_FAILED"] = "VALIDATION_FAILED";
    ErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorCode["MISSING_REQUIRED_FIELD"] = "MISSING_REQUIRED_FIELD";
    ErrorCode["INVALID_FORMAT"] = "INVALID_FORMAT";
    ErrorCode["RESOURCE_NOT_FOUND"] = "RESOURCE_NOT_FOUND";
    ErrorCode["RESOURCE_ALREADY_EXISTS"] = "RESOURCE_ALREADY_EXISTS";
    ErrorCode["OPERATION_NOT_ALLOWED"] = "OPERATION_NOT_ALLOWED";
    ErrorCode["BUSINESS_RULE_VIOLATION"] = "BUSINESS_RULE_VIOLATION";
    ErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
    ErrorCode["CONSTRAINT_VIOLATION"] = "CONSTRAINT_VIOLATION";
    ErrorCode["DUPLICATE_ENTRY"] = "DUPLICATE_ENTRY";
    ErrorCode["FOREIGN_KEY_VIOLATION"] = "FOREIGN_KEY_VIOLATION";
    ErrorCode["EXTERNAL_SERVICE_ERROR"] = "EXTERNAL_SERVICE_ERROR";
    ErrorCode["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorCode["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    ErrorCode["INTERNAL_ERROR"] = "INTERNAL_ERROR";
    ErrorCode["CONFIGURATION_ERROR"] = "CONFIGURATION_ERROR";
    ErrorCode["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
    ErrorCode["TENANT_NOT_FOUND"] = "TENANT_NOT_FOUND";
    ErrorCode["TENANT_DISABLED"] = "TENANT_DISABLED";
    ErrorCode["CROSS_TENANT_ACCESS"] = "CROSS_TENANT_ACCESS";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class AppError extends Error {
    code;
    statusCode;
    isOperational;
    context;
    correlationId;
    constructor(message, code, statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR, isOperational = true, context, correlationId) {
        super(message);
        this.name = this.constructor.name;
        this.code = code;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.context = context;
        this.correlationId = correlationId;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ErrorHandler {
    static logger = new common_1.Logger(ErrorHandler.name);
    static handleError(error, correlationId) {
        if (error instanceof common_1.HttpException) {
            return error;
        }
        if (error instanceof AppError) {
            return this.createHttpException(error.message, error.statusCode, error.code, error.context, correlationId);
        }
        if (error instanceof typeorm_1.QueryFailedError) {
            return this.handleDatabaseError(error, correlationId);
        }
        if (error.name === 'ValidationError') {
            return new common_1.BadRequestException({
                message: 'Validation failed',
                code: ErrorCode.VALIDATION_FAILED,
                errors: error.errors || [error.message],
                correlationId,
            });
        }
        if (error.name === 'JsonWebTokenError') {
            return new common_1.UnauthorizedException({
                message: 'Invalid token',
                code: ErrorCode.TOKEN_INVALID,
                correlationId,
            });
        }
        if (error.name === 'TokenExpiredError') {
            return new common_1.UnauthorizedException({
                message: 'Token expired',
                code: ErrorCode.TOKEN_EXPIRED,
                correlationId,
            });
        }
        if (error.name === 'TimeoutError' || error.code === 'ETIMEDOUT') {
            return new common_1.InternalServerErrorException({
                message: 'Request timeout',
                code: ErrorCode.TIMEOUT_ERROR,
                correlationId,
            });
        }
        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            return new common_1.InternalServerErrorException({
                message: 'Network error',
                code: ErrorCode.NETWORK_ERROR,
                correlationId,
            });
        }
        this.logger.error('Unhandled error', {
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
            },
            correlationId,
        });
        return new common_1.InternalServerErrorException({
            message: process.env.NODE_ENV === 'production'
                ? 'Internal server error'
                : error.message,
            code: ErrorCode.INTERNAL_ERROR,
            correlationId,
        });
    }
    static handleDatabaseError(error, correlationId) {
        const { code, detail, constraint } = error.driverError;
        switch (code) {
            case '23505':
                return new common_1.ConflictException({
                    message: 'Resource already exists',
                    code: ErrorCode.DUPLICATE_ENTRY,
                    detail: this.sanitizeDatabaseError(detail),
                    correlationId,
                });
            case '23503':
                return new common_1.BadRequestException({
                    message: 'Referenced resource does not exist',
                    code: ErrorCode.FOREIGN_KEY_VIOLATION,
                    detail: this.sanitizeDatabaseError(detail),
                    correlationId,
                });
            case '23502':
                return new common_1.BadRequestException({
                    message: 'Required field is missing',
                    code: ErrorCode.MISSING_REQUIRED_FIELD,
                    detail: this.sanitizeDatabaseError(detail),
                    correlationId,
                });
            case '23514':
                return new common_1.BadRequestException({
                    message: 'Invalid data format or value',
                    code: ErrorCode.CONSTRAINT_VIOLATION,
                    detail: this.sanitizeDatabaseError(detail),
                    correlationId,
                });
            default:
                this.logger.error('Database error', {
                    code,
                    detail,
                    constraint,
                    correlationId,
                });
                return new common_1.InternalServerErrorException({
                    message: 'Database operation failed',
                    code: ErrorCode.DATABASE_ERROR,
                    correlationId,
                });
        }
    }
    static createHttpException(message, statusCode, code, context, correlationId) {
        const response = {
            message,
            code,
            correlationId,
            ...(context && { context }),
        };
        switch (statusCode) {
            case common_1.HttpStatus.BAD_REQUEST:
                return new common_1.BadRequestException(response);
            case common_1.HttpStatus.UNAUTHORIZED:
                return new common_1.UnauthorizedException(response);
            case common_1.HttpStatus.FORBIDDEN:
                return new common_1.ForbiddenException(response);
            case common_1.HttpStatus.NOT_FOUND:
                return new common_1.NotFoundException(response);
            case common_1.HttpStatus.CONFLICT:
                return new common_1.ConflictException(response);
            default:
                return new common_1.InternalServerErrorException(response);
        }
    }
    static sanitizeDatabaseError(detail) {
        if (!detail)
            return 'Database constraint violation';
        return detail
            .replace(/Key \([^)]+\)/g, 'Key')
            .replace(/relation "[^"]+"/g, 'table')
            .replace(/column "[^"]+"/g, 'field')
            .replace(/constraint "[^"]+"/g, 'constraint');
    }
    static createBusinessError(message, code = ErrorCode.BUSINESS_RULE_VIOLATION, context, correlationId) {
        return new AppError(message, code, common_1.HttpStatus.BAD_REQUEST, true, context, correlationId);
    }
    static createNotFoundError(resource, identifier, correlationId) {
        const message = identifier
            ? `${resource} with identifier '${identifier}' not found`
            : `${resource} not found`;
        return new AppError(message, ErrorCode.RESOURCE_NOT_FOUND, common_1.HttpStatus.NOT_FOUND, true, { resource, identifier }, correlationId);
    }
    static createUnauthorizedError(message = 'Unauthorized access', code = ErrorCode.INSUFFICIENT_PERMISSIONS, correlationId) {
        return new AppError(message, code, common_1.HttpStatus.UNAUTHORIZED, true, undefined, correlationId);
    }
    static createForbiddenError(message = 'Access forbidden', code = ErrorCode.INSUFFICIENT_PERMISSIONS, correlationId) {
        return new AppError(message, code, common_1.HttpStatus.FORBIDDEN, true, undefined, correlationId);
    }
    static createValidationError(message, errors = [], correlationId) {
        return new AppError(message, ErrorCode.VALIDATION_FAILED, common_1.HttpStatus.BAD_REQUEST, true, { errors }, correlationId);
    }
    static isOperationalError(error) {
        if (error instanceof AppError) {
            return error.isOperational;
        }
        if (error instanceof common_1.HttpException) {
            return true;
        }
        return false;
    }
}
exports.ErrorHandler = ErrorHandler;


/***/ }),
/* 49 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ResponseUtil = void 0;
const common_1 = __webpack_require__(2);
class ResponseUtil {
    static success(data, message = 'Operation successful', statusCode = common_1.HttpStatus.OK, meta, correlationId) {
        return {
            success: true,
            statusCode,
            message,
            data,
            meta,
            correlationId,
            timestamp: new Date().toISOString(),
        };
    }
    static successNoData(message = 'Operation successful', statusCode = common_1.HttpStatus.OK, correlationId) {
        return {
            success: true,
            statusCode,
            message,
            correlationId,
            timestamp: new Date().toISOString(),
        };
    }
    static paginated(data, pagination, message = 'Data retrieved successfully', correlationId) {
        return {
            success: true,
            statusCode: common_1.HttpStatus.OK,
            message,
            data,
            meta: { pagination },
            correlationId,
            timestamp: new Date().toISOString(),
        };
    }
    static created(data, message = 'Resource created successfully', correlationId) {
        return this.success(data, message, common_1.HttpStatus.CREATED, undefined, correlationId);
    }
    static updated(data, message = 'Resource updated successfully', correlationId) {
        return this.success(data, message, common_1.HttpStatus.OK, undefined, correlationId);
    }
    static deleted(message = 'Resource deleted successfully', correlationId) {
        return this.successNoData(message, common_1.HttpStatus.OK, correlationId);
    }
    static error(message, statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR, error = 'Internal Server Error', errors, correlationId, path, method) {
        return {
            success: false,
            statusCode,
            message,
            error,
            errors,
            correlationId,
            timestamp: new Date().toISOString(),
            path,
            method,
        };
    }
    static createPaginationMeta(page, limit, total) {
        const totalPages = Math.ceil(total / limit);
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrevious: page > 1,
        };
    }
    static createMeta(startTime, pagination, filters, sort) {
        const meta = {
            executionTime: Date.now() - startTime,
            version: process.env.API_VERSION || '1.0.0',
        };
        if (pagination)
            meta.pagination = pagination;
        if (filters)
            meta.filters = filters;
        if (sort)
            meta.sort = sort;
        return meta;
    }
    static transformPaginatedResult(result, page, limit, message = 'Data retrieved successfully', correlationId) {
        const [data, total] = result;
        const pagination = this.createPaginationMeta(page, limit, total);
        return this.paginated(data, pagination, message, correlationId);
    }
    static validationError(errors, message = 'Validation failed', correlationId) {
        return this.error(message, common_1.HttpStatus.BAD_REQUEST, 'Validation Error', errors, correlationId);
    }
    static unauthorized(message = 'Unauthorized access', correlationId) {
        return this.error(message, common_1.HttpStatus.UNAUTHORIZED, 'Unauthorized', undefined, correlationId);
    }
    static forbidden(message = 'Access forbidden', correlationId) {
        return this.error(message, common_1.HttpStatus.FORBIDDEN, 'Forbidden', undefined, correlationId);
    }
    static notFound(message = 'Resource not found', correlationId) {
        return this.error(message, common_1.HttpStatus.NOT_FOUND, 'Not Found', undefined, correlationId);
    }
    static conflict(message = 'Resource already exists', correlationId) {
        return this.error(message, common_1.HttpStatus.CONFLICT, 'Conflict', undefined, correlationId);
    }
    static rateLimitExceeded(message = 'Rate limit exceeded', correlationId) {
        return this.error(message, common_1.HttpStatus.TOO_MANY_REQUESTS, 'Too Many Requests', undefined, correlationId);
    }
    static maintenance(message = 'Service temporarily unavailable', correlationId) {
        return this.error(message, common_1.HttpStatus.SERVICE_UNAVAILABLE, 'Service Unavailable', undefined, correlationId);
    }
    static isSuccess(response) {
        return response.success === true;
    }
    static extractData(response) {
        return this.isSuccess(response) ? response.data : undefined;
    }
    static extractPagination(response) {
        return this.isSuccess(response) ? response.meta?.pagination : undefined;
    }
    static healthCheck(status, checks = {}, correlationId) {
        const statusCode = status === 'healthy' ? common_1.HttpStatus.OK : common_1.HttpStatus.SERVICE_UNAVAILABLE;
        return this.success({
            status,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            checks,
        }, `Service is ${status}`, statusCode, undefined, correlationId);
    }
}
exports.ResponseUtil = ResponseUtil;


/***/ }),
/* 50 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DateUtil = void 0;
class DateUtil {
    static getCurrentDate() {
        return new Date().toISOString().split('T')[0];
    }
    static getCurrentTimestamp() {
        return new Date().toISOString();
    }
    static formatToISODate(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toISOString().split('T')[0];
    }
    static formatToISODateTime(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toISOString();
    }
    static parseDate(dateString) {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            throw new Error(`Invalid date string: ${dateString}`);
        }
        return date;
    }
    static isValidDate(date) {
        return date instanceof Date && !isNaN(date.getTime());
    }
    static isValidDateString(dateString) {
        try {
            const date = new Date(dateString);
            return this.isValidDate(date);
        }
        catch {
            return false;
        }
    }
    static addDays(date, days) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        d.setDate(d.getDate() + days);
        return d;
    }
    static addMonths(date, months) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        d.setMonth(d.getMonth() + months);
        return d;
    }
    static addYears(date, years) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        d.setFullYear(d.getFullYear() + years);
        return d;
    }
    static subtractDays(date, days) {
        return this.addDays(date, -days);
    }
    static subtractMonths(date, months) {
        return this.addMonths(date, -months);
    }
    static subtractYears(date, years) {
        return this.addYears(date, -years);
    }
    static getDifferenceInDays(date1, date2) {
        const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
        const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
        const diffTime = Math.abs(d2.getTime() - d1.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    static getDifferenceInHours(date1, date2) {
        const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
        const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
        const diffTime = Math.abs(d2.getTime() - d1.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60));
    }
    static getDifferenceInMinutes(date1, date2) {
        const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
        const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
        const diffTime = Math.abs(d2.getTime() - d1.getTime());
        return Math.ceil(diffTime / (1000 * 60));
    }
    static isInPast(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d < new Date();
    }
    static isInFuture(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d > new Date();
    }
    static isToday(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        return d.toDateString() === today.toDateString();
    }
    static isWeekend(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        const day = d.getDay();
        return day === 0 || day === 6;
    }
    static isWeekday(date) {
        return !this.isWeekend(date);
    }
    static getStartOfDay(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        d.setHours(0, 0, 0, 0);
        return d;
    }
    static getEndOfDay(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        d.setHours(23, 59, 59, 999);
        return d;
    }
    static getStartOfMonth(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        return new Date(d.getFullYear(), d.getMonth(), 1);
    }
    static getEndOfMonth(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        return new Date(d.getFullYear(), d.getMonth() + 1, 0, 23, 59, 59, 999);
    }
    static getStartOfYear(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        return new Date(d.getFullYear(), 0, 1);
    }
    static getEndOfYear(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        return new Date(d.getFullYear(), 11, 31, 23, 59, 59, 999);
    }
    static getAge(birthDate) {
        const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
        const today = new Date();
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        return age;
    }
    static formatForDisplay(date, locale = 'en-US', options = {}) {
        const d = typeof date === 'string' ? new Date(date) : date;
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        };
        return d.toLocaleDateString(locale, { ...defaultOptions, ...options });
    }
    static formatTimeForDisplay(date, locale = 'en-US', options = {}) {
        const d = typeof date === 'string' ? new Date(date) : date;
        const defaultOptions = {
            hour: '2-digit',
            minute: '2-digit',
        };
        return d.toLocaleTimeString(locale, { ...defaultOptions, ...options });
    }
    static getBusinessDaysBetween(startDate, endDate) {
        const start = typeof startDate === 'string' ? new Date(startDate) : new Date(startDate);
        const end = typeof endDate === 'string' ? new Date(endDate) : new Date(endDate);
        let businessDays = 0;
        const currentDate = new Date(start);
        while (currentDate <= end) {
            if (this.isWeekday(currentDate)) {
                businessDays++;
            }
            currentDate.setDate(currentDate.getDate() + 1);
        }
        return businessDays;
    }
    static getNextBusinessDay(date) {
        let nextDay = this.addDays(date, 1);
        while (this.isWeekend(nextDay)) {
            nextDay = this.addDays(nextDay, 1);
        }
        return nextDay;
    }
    static getPreviousBusinessDay(date) {
        let prevDay = this.subtractDays(date, 1);
        while (this.isWeekend(prevDay)) {
            prevDay = this.subtractDays(prevDay, 1);
        }
        return prevDay;
    }
    static convertToTimezone(date, timezone) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return new Date(d.toLocaleString('en-US', { timeZone: timezone }));
    }
    static getTimezoneOffset(timezone) {
        const date = new Date();
        const utc = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
        const target = new Date(utc.toLocaleString('en-US', { timeZone: timezone }));
        return (target.getTime() - utc.getTime()) / 60000;
    }
    static isLeapYear(year) {
        return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    }
    static getDaysInMonth(year, month) {
        return new Date(year, month, 0).getDate();
    }
    static getQuarter(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return Math.floor((d.getMonth() + 3) / 3);
    }
    static getWeekNumber(date) {
        const d = typeof date === 'string' ? new Date(date) : new Date(date);
        const firstDayOfYear = new Date(d.getFullYear(), 0, 1);
        const pastDaysOfYear = (d.getTime() - firstDayOfYear.getTime()) / 86400000;
        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
    }
}
exports.DateUtil = DateUtil;


/***/ }),
/* 51 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.StringUtil = void 0;
class StringUtil {
    static toCamelCase(str) {
        return str
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
            return index === 0 ? word.toLowerCase() : word.toUpperCase();
        })
            .replace(/\s+/g, '');
    }
    static toPascalCase(str) {
        return str
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => {
            return word.toUpperCase();
        })
            .replace(/\s+/g, '');
    }
    static toSnakeCase(str) {
        return str
            .replace(/\W+/g, ' ')
            .split(/ |\B(?=[A-Z])/)
            .map(word => word.toLowerCase())
            .join('_');
    }
    static toKebabCase(str) {
        return str
            .replace(/\W+/g, ' ')
            .split(/ |\B(?=[A-Z])/)
            .map(word => word.toLowerCase())
            .join('-');
    }
    static toTitleCase(str) {
        return str.replace(/\w\S*/g, (txt) => {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        });
    }
    static capitalize(str) {
        if (!str)
            return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
    static uncapitalize(str) {
        if (!str)
            return str;
        return str.charAt(0).toLowerCase() + str.slice(1);
    }
    static truncate(str, length, suffix = '...') {
        if (str.length <= length)
            return str;
        return str.substring(0, length - suffix.length) + suffix;
    }
    static truncateWords(str, wordCount, suffix = '...') {
        const words = str.split(' ');
        if (words.length <= wordCount)
            return str;
        return words.slice(0, wordCount).join(' ') + suffix;
    }
    static normalizeWhitespace(str) {
        return str.replace(/\s+/g, ' ').trim();
    }
    static removeWhitespace(str) {
        return str.replace(/\s/g, '');
    }
    static isEmpty(str) {
        return !str || str.trim().length === 0;
    }
    static isNotEmpty(str) {
        return !this.isEmpty(str);
    }
    static generateRandom(length, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return result;
    }
    static generateAlphanumeric(length) {
        return this.generateRandom(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');
    }
    static generateNumeric(length) {
        return this.generateRandom(length, '0123456789');
    }
    static generateAlphabetic(length) {
        return this.generateRandom(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz');
    }
    static mask(str, maskChar = '*', visibleStart = 2, visibleEnd = 2) {
        if (str.length <= visibleStart + visibleEnd) {
            return maskChar.repeat(str.length);
        }
        const start = str.substring(0, visibleStart);
        const end = str.substring(str.length - visibleEnd);
        const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);
        return start + middle + end;
    }
    static maskEmail(email) {
        const [username, domain] = email.split('@');
        if (!domain)
            return this.mask(email);
        const maskedUsername = this.mask(username, '*', 1, 1);
        return `${maskedUsername}@${domain}`;
    }
    static maskPhone(phone) {
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length < 4)
            return this.mask(phone);
        return this.mask(cleaned, '*', 2, 2);
    }
    static getInitials(name, maxInitials = 2) {
        return name
            .split(' ')
            .filter(word => word.length > 0)
            .slice(0, maxInitials)
            .map(word => word.charAt(0).toUpperCase())
            .join('');
    }
    static slugify(str) {
        return str
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
    static escapeHtml(str) {
        const htmlEscapes = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '/': '&#x2F;',
        };
        return str.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
    }
    static unescapeHtml(str) {
        const htmlUnescapes = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#x27;': "'",
            '&#x2F;': '/',
        };
        return str.replace(/&(?:amp|lt|gt|quot|#x27|#x2F);/g, (match) => htmlUnescapes[match]);
    }
    static countWords(str) {
        return str.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    static countCharacters(str, includeSpaces = true) {
        return includeSpaces ? str.length : str.replace(/\s/g, '').length;
    }
    static reverse(str) {
        return str.split('').reverse().join('');
    }
    static isPalindrome(str) {
        const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
        return cleaned === this.reverse(cleaned);
    }
    static repeat(str, count) {
        return str.repeat(Math.max(0, count));
    }
    static pad(str, length, padChar = ' ', padLeft = true) {
        if (str.length >= length)
            return str;
        const padLength = length - str.length;
        const padding = padChar.repeat(Math.ceil(padLength / padChar.length)).substring(0, padLength);
        return padLeft ? padding + str : str + padding;
    }
    static padLeft(str, length, padChar = ' ') {
        return this.pad(str, length, padChar, true);
    }
    static padRight(str, length, padChar = ' ') {
        return this.pad(str, length, padChar, false);
    }
    static removePrefix(str, prefix) {
        return str.startsWith(prefix) ? str.substring(prefix.length) : str;
    }
    static removeSuffix(str, suffix) {
        return str.endsWith(suffix) ? str.substring(0, str.length - suffix.length) : str;
    }
    static ensurePrefix(str, prefix) {
        return str.startsWith(prefix) ? str : prefix + str;
    }
    static ensureSuffix(str, suffix) {
        return str.endsWith(suffix) ? str : str + suffix;
    }
    static chunk(str, size) {
        const chunks = [];
        for (let i = 0; i < str.length; i += size) {
            chunks.push(str.substring(i, i + size));
        }
        return chunks;
    }
    static toBoolean(str) {
        const truthyValues = ['true', '1', 'yes', 'on', 'enabled'];
        return truthyValues.includes(str.toLowerCase().trim());
    }
    static template(template, variables) {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return variables.hasOwnProperty(key) ? String(variables[key]) : match;
        });
    }
    static extractNumbers(str) {
        const matches = str.match(/\d+/g);
        return matches ? matches.map(Number) : [];
    }
    static removeNumbers(str) {
        return str.replace(/\d/g, '');
    }
    static isAlpha(str) {
        return /^[a-zA-Z]+$/.test(str);
    }
    static isNumeric(str) {
        return /^\d+$/.test(str);
    }
    static isAlphanumeric(str) {
        return /^[a-zA-Z0-9]+$/.test(str);
    }
}
exports.StringUtil = StringUtil;


/***/ }),
/* 52 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthModule = void 0;
const common_1 = __webpack_require__(2);
const jwt_1 = __webpack_require__(53);
const passport_1 = __webpack_require__(54);
const config_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(8);
const refresh_token_entity_1 = __webpack_require__(29);
const password_reset_entity_1 = __webpack_require__(30);
const login_attempt_entity_1 = __webpack_require__(31);
const auth_service_1 = __webpack_require__(55);
const token_service_1 = __webpack_require__(57);
const password_service_1 = __webpack_require__(58);
const auth_controller_1 = __webpack_require__(59);
const jwt_strategy_1 = __webpack_require__(66);
const jwt_auth_guard_1 = __webpack_require__(64);
const user_module_1 = __webpack_require__(68);
const common_module_1 = __webpack_require__(35);
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            user_module_1.UserModule,
            common_module_1.CommonModule,
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('jwt.accessToken.secret'),
                    signOptions: {
                        expiresIn: configService.get('jwt.accessToken.expiresIn'),
                        issuer: configService.get('jwt.accessToken.issuer'),
                        audience: configService.get('jwt.accessToken.audience'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            typeorm_1.TypeOrmModule.forFeature([
                refresh_token_entity_1.RefreshToken,
                password_reset_entity_1.PasswordReset,
                login_attempt_entity_1.LoginAttempt,
            ]),
        ],
        controllers: [
            auth_controller_1.AuthController,
        ],
        providers: [
            auth_service_1.AuthService,
            token_service_1.TokenService,
            password_service_1.PasswordService,
            jwt_strategy_1.JwtStrategy,
            jwt_auth_guard_1.JwtAuthGuard,
        ],
        exports: [
            auth_service_1.AuthService,
            token_service_1.TokenService,
            password_service_1.PasswordService,
            jwt_auth_guard_1.JwtAuthGuard,
            jwt_strategy_1.JwtStrategy,
        ],
    })
], AuthModule);


/***/ }),
/* 53 */
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),
/* 54 */
/***/ ((module) => {

module.exports = require("@nestjs/passport");

/***/ }),
/* 55 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthService = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const bcrypt = __webpack_require__(56);
const user_entity_1 = __webpack_require__(28);
const refresh_token_entity_1 = __webpack_require__(29);
const login_attempt_entity_1 = __webpack_require__(31);
const token_service_1 = __webpack_require__(57);
const password_service_1 = __webpack_require__(58);
const correlation_service_1 = __webpack_require__(23);
let AuthService = AuthService_1 = class AuthService {
    userRepository;
    refreshTokenRepository;
    loginAttemptRepository;
    tokenService;
    passwordService;
    configService;
    correlationService;
    winstonLogger;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(userRepository, refreshTokenRepository, loginAttemptRepository, tokenService, passwordService, configService, correlationService, winstonLogger) {
        this.userRepository = userRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.loginAttemptRepository = loginAttemptRepository;
        this.tokenService = tokenService;
        this.passwordService = passwordService;
        this.configService = configService;
        this.correlationService = correlationService;
        this.winstonLogger = winstonLogger;
    }
    async login(loginDto, context) {
        const { email, password } = loginDto;
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Login attempt started', {
            context: AuthService_1.name,
            correlationId,
            email,
            tenantId: context.tenantId,
            ipAddress: context.ipAddress,
        });
        try {
            await this.checkRateLimit(email, context.ipAddress);
            const user = await this.findUserByEmailAndTenant(email, context.tenantId);
            if (!user) {
                await this.recordLoginAttempt(email, null, login_attempt_entity_1.LoginAttemptStatus.FAILED_INVALID_CREDENTIALS, context, 'User not found');
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
            if (!isPasswordValid) {
                await this.recordLoginAttempt(email, user.id, login_attempt_entity_1.LoginAttemptStatus.FAILED_INVALID_CREDENTIALS, context, 'Invalid password');
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            if (!user.isActive) {
                await this.recordLoginAttempt(email, user.id, login_attempt_entity_1.LoginAttemptStatus.FAILED_ACCOUNT_DISABLED, context, 'Account disabled');
                throw new common_1.UnauthorizedException('Account is disabled');
            }
            if (!user.isEmailVerified) {
                await this.recordLoginAttempt(email, user.id, login_attempt_entity_1.LoginAttemptStatus.FAILED_EMAIL_NOT_VERIFIED, context, 'Email not verified');
                throw new common_1.UnauthorizedException('Email not verified');
            }
            if (user.isLocked) {
                await this.recordLoginAttempt(email, user.id, login_attempt_entity_1.LoginAttemptStatus.FAILED_ACCOUNT_LOCKED, context, 'Account locked');
                throw new common_1.UnauthorizedException('Account is locked');
            }
            const accessToken = await this.tokenService.generateAccessToken(user);
            const refreshToken = await this.tokenService.generateRefreshToken(user, context);
            await this.updateUserLastLogin(user, context);
            await this.recordLoginAttempt(email, user.id, login_attempt_entity_1.LoginAttemptStatus.SUCCESS, context);
            this.winstonLogger.info('Login successful', {
                context: AuthService_1.name,
                correlationId,
                userId: user.id,
                email,
                tenantId: context.tenantId,
            });
            return {
                user,
                accessToken,
                refreshToken: refreshToken.token,
                expiresIn: this.configService.get('jwt.accessToken.expiresIn'),
            };
        }
        catch (error) {
            this.winstonLogger.error('Login failed', {
                context: AuthService_1.name,
                correlationId,
                email,
                tenantId: context.tenantId,
                error: error.message,
            });
            throw error;
        }
    }
    async register(registerDto, context) {
        const { email, password, firstName, lastName } = registerDto;
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Registration attempt started', {
            context: AuthService_1.name,
            correlationId,
            email,
            tenantId: context.tenantId,
        });
        const existingUser = await this.findUserByEmailAndTenant(email, context.tenantId);
        if (existingUser) {
            throw new common_1.ConflictException('User already exists');
        }
        await this.passwordService.validatePasswordStrength(password);
        const passwordHash = await this.passwordService.hashPassword(password);
        const user = this.userRepository.create({
            email,
            passwordHash,
            firstName,
            lastName,
            tenantId: context.tenantId,
            isActive: true,
            isEmailVerified: false,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
        const savedUser = await this.userRepository.save(user);
        this.winstonLogger.info('User registered successfully', {
            context: AuthService_1.name,
            correlationId,
            userId: savedUser.id,
            email,
            tenantId: context.tenantId,
        });
        return savedUser;
    }
    async refreshToken(refreshTokenDto) {
        const { refreshToken } = refreshTokenDto;
        const correlationId = this.correlationService.getCorrelationId();
        const tokenData = await this.tokenService.verifyRefreshToken(refreshToken);
        const storedToken = await this.refreshTokenRepository.findOne({
            where: { token: refreshToken },
            relations: ['user'],
        });
        if (!storedToken || !storedToken.isValid()) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
        const accessToken = await this.tokenService.generateAccessToken(storedToken.user);
        let newRefreshToken = refreshToken;
        if (this.configService.get('jwt.security.rotateRefreshTokens')) {
            storedToken.revoke('system', 'Token rotation');
            await this.refreshTokenRepository.save(storedToken);
            const newToken = await this.tokenService.generateRefreshToken(storedToken.user, {
                ipAddress: storedToken.ipAddress || '',
                userAgent: storedToken.userAgent,
                deviceId: storedToken.deviceId,
                deviceType: storedToken.deviceType,
                tenantId: storedToken.tenantId,
            });
            newRefreshToken = newToken.token;
        }
        this.winstonLogger.info('Token refreshed successfully', {
            context: AuthService_1.name,
            correlationId,
            userId: storedToken.user.id,
        });
        return {
            user: storedToken.user,
            accessToken,
            refreshToken: newRefreshToken,
            expiresIn: this.configService.get('jwt.accessToken.expiresIn'),
        };
    }
    async findUserByEmailAndTenant(email, tenantId) {
        const whereCondition = { email };
        if (tenantId) {
            whereCondition.tenantId = tenantId;
        }
        return this.userRepository.findOne({
            where: whereCondition,
            relations: ['roles', 'permissions'],
        });
    }
    async checkRateLimit(email, ipAddress) {
        const windowMs = this.configService.get('jwt.security.rateLimiting.login.windowMs');
        const maxAttempts = this.configService.get('jwt.security.rateLimiting.login.max');
        const since = new Date(Date.now() - windowMs);
        const recentAttempts = await this.loginAttemptRepository.count({
            where: [
                { email, createdAt: (0, typeorm_2.MoreThanOrEqual)(since) },
                { ipAddress, createdAt: (0, typeorm_2.MoreThanOrEqual)(since) },
            ],
        });
        if (recentAttempts >= maxAttempts) {
            throw new common_1.UnauthorizedException('Too many login attempts. Please try again later.');
        }
    }
    async recordLoginAttempt(email, userId, status, context, failureReason) {
        const loginAttempt = this.loginAttemptRepository.create({
            email,
            userId,
            status,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            deviceId: context.deviceId,
            deviceType: context.deviceType,
            tenantId: context.tenantId,
            correlationId: this.correlationService.getCorrelationId(),
            failureReason,
            createdAt: new Date(),
        });
        await this.loginAttemptRepository.save(loginAttempt);
    }
    async updateUserLastLogin(user, context) {
        user.lastLoginAt = new Date();
        user.lastLoginIp = context.ipAddress;
        await this.userRepository.save(user);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(refresh_token_entity_1.RefreshToken)),
    __param(2, (0, typeorm_1.InjectRepository)(login_attempt_entity_1.LoginAttempt)),
    __param(7, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof token_service_1.TokenService !== "undefined" && token_service_1.TokenService) === "function" ? _d : Object, typeof (_e = typeof password_service_1.PasswordService !== "undefined" && password_service_1.PasswordService) === "function" ? _e : Object, typeof (_f = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _f : Object, typeof (_g = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _g : Object, typeof (_h = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _h : Object])
], AuthService);


/***/ }),
/* 56 */
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),
/* 57 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TokenService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TokenService = void 0;
const common_1 = __webpack_require__(2);
const jwt_1 = __webpack_require__(53);
const config_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const uuid_1 = __webpack_require__(25);
const refresh_token_entity_1 = __webpack_require__(29);
const correlation_service_1 = __webpack_require__(23);
let TokenService = TokenService_1 = class TokenService {
    jwtService;
    configService;
    refreshTokenRepository;
    correlationService;
    winstonLogger;
    logger = new common_1.Logger(TokenService_1.name);
    constructor(jwtService, configService, refreshTokenRepository, correlationService, winstonLogger) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.refreshTokenRepository = refreshTokenRepository;
        this.correlationService = correlationService;
        this.winstonLogger = winstonLogger;
    }
    async generateAccessToken(user) {
        const payload = {
            sub: user.id,
            email: user.email,
            tenantId: user.tenantId,
            roles: user.roles?.map(role => role.name) || [],
            permissions: user.permissions?.map(permission => permission.name) || [],
            jti: (0, uuid_1.v4)(),
        };
        const token = this.jwtService.sign(payload, {
            secret: this.configService.get('jwt.accessToken.secret'),
            expiresIn: this.configService.get('jwt.accessToken.expiresIn'),
            issuer: this.configService.get('jwt.accessToken.issuer'),
            audience: this.configService.get('jwt.accessToken.audience'),
        });
        this.winstonLogger.debug('Access token generated', {
            context: TokenService_1.name,
            correlationId: this.correlationService.getCorrelationId(),
            userId: user.id,
            tenantId: user.tenantId,
            jti: payload.jti,
        });
        return token;
    }
    async generateRefreshToken(user, context) {
        const expiresIn = this.configService.get('jwt.refreshToken.expiresIn');
        const expiresAt = this.calculateExpirationDate(expiresIn);
        const refreshTokenEntity = this.refreshTokenRepository.create({
            userId: user.id,
            tenantId: context.tenantId,
            expiresAt,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            deviceId: context.deviceId,
            deviceType: context.deviceType,
            sessionId: (0, uuid_1.v4)(),
        });
        const savedToken = await this.refreshTokenRepository.save(refreshTokenEntity);
        const payload = {
            sub: user.id,
            tokenId: savedToken.id,
            tenantId: context.tenantId,
        };
        const token = this.jwtService.sign(payload, {
            secret: this.configService.get('jwt.refreshToken.secret'),
            expiresIn: this.configService.get('jwt.refreshToken.expiresIn'),
            issuer: this.configService.get('jwt.refreshToken.issuer'),
            audience: this.configService.get('jwt.refreshToken.audience'),
        });
        savedToken.token = token;
        await this.refreshTokenRepository.save(savedToken);
        this.winstonLogger.debug('Refresh token generated', {
            context: TokenService_1.name,
            correlationId: this.correlationService.getCorrelationId(),
            userId: user.id,
            tenantId: context.tenantId,
            tokenId: savedToken.id,
        });
        return savedToken;
    }
    async verifyAccessToken(token) {
        try {
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('jwt.accessToken.secret'),
                issuer: this.configService.get('jwt.accessToken.issuer'),
                audience: this.configService.get('jwt.accessToken.audience'),
            });
            return payload;
        }
        catch (error) {
            this.winstonLogger.warn('Access token verification failed', {
                context: TokenService_1.name,
                correlationId: this.correlationService.getCorrelationId(),
                error: error.message,
            });
            throw new common_1.UnauthorizedException('Invalid access token');
        }
    }
    async verifyRefreshToken(token) {
        try {
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('jwt.refreshToken.secret'),
                issuer: this.configService.get('jwt.refreshToken.issuer'),
                audience: this.configService.get('jwt.refreshToken.audience'),
            });
            const storedToken = await this.refreshTokenRepository.findOne({
                where: { id: payload.tokenId },
            });
            if (!storedToken || !storedToken.isValid()) {
                throw new common_1.UnauthorizedException('Refresh token is invalid or expired');
            }
            return payload;
        }
        catch (error) {
            this.winstonLogger.warn('Refresh token verification failed', {
                context: TokenService_1.name,
                correlationId: this.correlationService.getCorrelationId(),
                error: error.message,
            });
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async revokeRefreshToken(tokenId, revokedBy, reason) {
        const token = await this.refreshTokenRepository.findOne({
            where: { id: tokenId },
        });
        if (token) {
            token.revoke(revokedBy, reason);
            await this.refreshTokenRepository.save(token);
            this.winstonLogger.info('Refresh token revoked', {
                context: TokenService_1.name,
                correlationId: this.correlationService.getCorrelationId(),
                tokenId,
                revokedBy,
                reason,
            });
        }
    }
    async revokeAllUserTokens(userId, revokedBy, reason) {
        const tokens = await this.refreshTokenRepository.find({
            where: { userId, isRevoked: false },
        });
        for (const token of tokens) {
            token.revoke(revokedBy, reason);
        }
        await this.refreshTokenRepository.save(tokens);
        this.winstonLogger.info('All user tokens revoked', {
            context: TokenService_1.name,
            correlationId: this.correlationService.getCorrelationId(),
            userId,
            tokenCount: tokens.length,
            revokedBy,
            reason,
        });
    }
    async cleanupExpiredTokens() {
        const result = await this.refreshTokenRepository.delete({
            expiresAt: (0, typeorm_2.LessThan)(new Date()),
        });
        const deletedCount = result.affected || 0;
        this.winstonLogger.info('Expired tokens cleaned up', {
            context: TokenService_1.name,
            deletedCount,
        });
        return deletedCount;
    }
    calculateExpirationDate(duration) {
        const now = new Date();
        const match = duration.match(/^(\d+)([smhd])$/);
        if (!match) {
            throw new Error(`Invalid duration format: ${duration}`);
        }
        const value = parseInt(match[1], 10);
        const unit = match[2];
        switch (unit) {
            case 's':
                return new Date(now.getTime() + value * 1000);
            case 'm':
                return new Date(now.getTime() + value * 60 * 1000);
            case 'h':
                return new Date(now.getTime() + value * 60 * 60 * 1000);
            case 'd':
                return new Date(now.getTime() + value * 24 * 60 * 60 * 1000);
            default:
                throw new Error(`Unsupported duration unit: ${unit}`);
        }
    }
};
exports.TokenService = TokenService;
exports.TokenService = TokenService = TokenService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, typeorm_1.InjectRepository)(refresh_token_entity_1.RefreshToken)),
    __param(4, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _d : Object, typeof (_e = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _e : Object])
], TokenService);


/***/ }),
/* 58 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PasswordService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PasswordService = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const bcrypt = __webpack_require__(56);
const uuid_1 = __webpack_require__(25);
const user_entity_1 = __webpack_require__(28);
const password_reset_entity_1 = __webpack_require__(30);
const correlation_service_1 = __webpack_require__(23);
let PasswordService = PasswordService_1 = class PasswordService {
    configService;
    userRepository;
    passwordResetRepository;
    correlationService;
    winstonLogger;
    logger = new common_1.Logger(PasswordService_1.name);
    saltRounds = 12;
    constructor(configService, userRepository, passwordResetRepository, correlationService, winstonLogger) {
        this.configService = configService;
        this.userRepository = userRepository;
        this.passwordResetRepository = passwordResetRepository;
        this.correlationService = correlationService;
        this.winstonLogger = winstonLogger;
    }
    async hashPassword(password) {
        return bcrypt.hash(password, this.saltRounds);
    }
    async comparePassword(password, hash) {
        return bcrypt.compare(password, hash);
    }
    async validatePasswordStrength(password) {
        const minLength = this.configService.get('jwt.security.password.minLength', 8);
        const requireUppercase = this.configService.get('jwt.security.password.requireUppercase', true);
        const requireLowercase = this.configService.get('jwt.security.password.requireLowercase', true);
        const requireNumbers = this.configService.get('jwt.security.password.requireNumbers', true);
        const requireSpecialChars = this.configService.get('jwt.security.password.requireSpecialChars', true);
        const requirements = {
            minLength: password.length >= minLength,
            hasUppercase: /[A-Z]/.test(password),
            hasLowercase: /[a-z]/.test(password),
            hasNumbers: /\d/.test(password),
            hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
        };
        const feedback = [];
        let score = 0;
        if (!requirements.minLength) {
            feedback.push(`Password must be at least ${minLength} characters long`);
        }
        else {
            score += 20;
        }
        if (requireUppercase && !requirements.hasUppercase) {
            feedback.push('Password must contain at least one uppercase letter');
        }
        else if (requirements.hasUppercase) {
            score += 20;
        }
        if (requireLowercase && !requirements.hasLowercase) {
            feedback.push('Password must contain at least one lowercase letter');
        }
        else if (requirements.hasLowercase) {
            score += 20;
        }
        if (requireNumbers && !requirements.hasNumbers) {
            feedback.push('Password must contain at least one number');
        }
        else if (requirements.hasNumbers) {
            score += 20;
        }
        if (requireSpecialChars && !requirements.hasSpecialChars) {
            feedback.push('Password must contain at least one special character');
        }
        else if (requirements.hasSpecialChars) {
            score += 20;
        }
        if (password.length >= 12)
            score += 5;
        if (password.length >= 16)
            score += 5;
        if (/[A-Z].*[A-Z]/.test(password))
            score += 5;
        if (/\d.*\d/.test(password))
            score += 5;
        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?].*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password))
            score += 5;
        if (/(.)\1{2,}/.test(password))
            score -= 10;
        if (/123|abc|qwe|password|admin/i.test(password))
            score -= 20;
        score = Math.max(0, Math.min(100, score));
        const isValid = feedback.length === 0;
        if (!isValid) {
            throw new common_1.BadRequestException({
                message: 'Password does not meet security requirements',
                feedback,
                requirements,
                score,
            });
        }
        return {
            isValid,
            score,
            feedback,
            requirements,
        };
    }
    async generatePasswordResetToken(email, tenantId, ipAddress, userAgent) {
        const correlationId = this.correlationService.getCorrelationId();
        const whereCondition = { email };
        if (tenantId) {
            whereCondition.tenantId = tenantId;
        }
        const user = await this.userRepository.findOne({
            where: whereCondition,
        });
        if (!user) {
            this.winstonLogger.warn('Password reset requested for non-existent user', {
                context: PasswordService_1.name,
                correlationId,
                email,
                tenantId,
                ipAddress,
            });
            return 'token-sent';
        }
        await this.passwordResetRepository.update({ userId: user.id, isUsed: false }, { isUsed: true, usedAt: new Date() });
        const resetToken = (0, uuid_1.v4)();
        const expiresIn = this.configService.get('jwt.passwordResetToken.expiresIn', '1h');
        const expiresAt = this.calculateExpirationDate(expiresIn);
        const passwordReset = this.passwordResetRepository.create({
            token: resetToken,
            userId: user.id,
            tenantId,
            email,
            expiresAt,
            requestIpAddress: ipAddress,
            requestUserAgent: userAgent,
        });
        await this.passwordResetRepository.save(passwordReset);
        this.winstonLogger.info('Password reset token generated', {
            context: PasswordService_1.name,
            correlationId,
            userId: user.id,
            email,
            tenantId,
            resetTokenId: passwordReset.id,
        });
        return resetToken;
    }
    async resetPassword(token, newPassword, ipAddress, userAgent) {
        const correlationId = this.correlationService.getCorrelationId();
        const passwordReset = await this.passwordResetRepository.findOne({
            where: { token },
            relations: ['user'],
        });
        if (!passwordReset || !passwordReset.isValid()) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        if (passwordReset.hasExceededMaxAttempts()) {
            throw new common_1.BadRequestException('Reset token has exceeded maximum attempts');
        }
        passwordReset.incrementAttempt();
        try {
            await this.validatePasswordStrength(newPassword);
            const passwordHash = await this.hashPassword(newPassword);
            const user = passwordReset.user;
            user.passwordHash = passwordHash;
            user.passwordChangedAt = new Date();
            user.updatedAt = new Date();
            await this.userRepository.save(user);
            passwordReset.markAsUsed(ipAddress, userAgent);
            await this.passwordResetRepository.save(passwordReset);
            this.winstonLogger.info('Password reset successful', {
                context: PasswordService_1.name,
                correlationId,
                userId: user.id,
                email: user.email,
                resetTokenId: passwordReset.id,
            });
        }
        catch (error) {
            await this.passwordResetRepository.save(passwordReset);
            throw error;
        }
    }
    async changePassword(userId, currentPassword, newPassword) {
        const correlationId = this.correlationService.getCorrelationId();
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.BadRequestException('User not found');
        }
        const isCurrentPasswordValid = await this.comparePassword(currentPassword, user.passwordHash);
        if (!isCurrentPasswordValid) {
            throw new common_1.BadRequestException('Current password is incorrect');
        }
        await this.validatePasswordStrength(newPassword);
        const isSamePassword = await this.comparePassword(newPassword, user.passwordHash);
        if (isSamePassword) {
            throw new common_1.BadRequestException('New password must be different from current password');
        }
        const passwordHash = await this.hashPassword(newPassword);
        user.passwordHash = passwordHash;
        user.passwordChangedAt = new Date();
        user.updatedAt = new Date();
        await this.userRepository.save(user);
        this.winstonLogger.info('Password changed successfully', {
            context: PasswordService_1.name,
            correlationId,
            userId,
            email: user.email,
        });
    }
    calculateExpirationDate(duration) {
        const now = new Date();
        const match = duration.match(/^(\d+)([smhd])$/);
        if (!match) {
            throw new Error(`Invalid duration format: ${duration}`);
        }
        const value = parseInt(match[1], 10);
        const unit = match[2];
        switch (unit) {
            case 's':
                return new Date(now.getTime() + value * 1000);
            case 'm':
                return new Date(now.getTime() + value * 60 * 1000);
            case 'h':
                return new Date(now.getTime() + value * 60 * 60 * 1000);
            case 'd':
                return new Date(now.getTime() + value * 24 * 60 * 60 * 1000);
            default:
                throw new Error(`Unsupported duration unit: ${unit}`);
        }
    }
};
exports.PasswordService = PasswordService;
exports.PasswordService = PasswordService = PasswordService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(password_reset_entity_1.PasswordReset)),
    __param(4, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _d : Object, typeof (_e = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _e : Object])
], PasswordService);


/***/ }),
/* 59 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthController = void 0;
const common_1 = __webpack_require__(2);
const express_1 = __webpack_require__(60);
const swagger_1 = __webpack_require__(4);
const throttler_1 = __webpack_require__(10);
const auth_service_1 = __webpack_require__(55);
const password_service_1 = __webpack_require__(58);
const login_dto_1 = __webpack_require__(61);
const register_dto_1 = __webpack_require__(62);
const refresh_token_dto_1 = __webpack_require__(63);
const jwt_auth_guard_1 = __webpack_require__(64);
const tenant_required_decorator_1 = __webpack_require__(40);
const current_user_decorator_1 = __webpack_require__(65);
const user_entity_1 = __webpack_require__(28);
let AuthController = class AuthController {
    authService;
    passwordService;
    constructor(authService, passwordService) {
        this.authService = authService;
        this.passwordService = passwordService;
    }
    async login(loginDto, request) {
        const context = {
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'],
            deviceId: loginDto.deviceId,
            deviceType: loginDto.deviceType,
            tenantId: request.headers['x-tenant-id'],
        };
        return this.authService.login(loginDto, context);
    }
    async register(registerDto, request) {
        if (registerDto.password !== registerDto.confirmPassword) {
            throw new common_1.BadRequestException('Password confirmation does not match');
        }
        const context = {
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'],
            tenantId: request.headers['x-tenant-id'],
        };
        return this.authService.register(registerDto, context);
    }
    async refreshToken(refreshTokenDto) {
        return this.authService.refreshToken(refreshTokenDto);
    }
    async logout(user, refreshTokenDto) {
        return { message: 'Logout successful' };
    }
    async getProfile(user) {
        return user;
    }
    async forgotPassword(body, request) {
        await this.passwordService.generatePasswordResetToken(body.email, request.headers['x-tenant-id'], request.ip, request.headers['user-agent']);
        return { message: 'If the email exists, a password reset link has been sent' };
    }
    async resetPassword(body, request) {
        if (body.password !== body.confirmPassword) {
            throw new common_1.BadRequestException('Password confirmation does not match');
        }
        await this.passwordService.resetPassword(body.token, body.password, request.ip, request.headers['user-agent']);
        return { message: 'Password reset successful' };
    }
    async changePassword(user, body) {
        if (body.newPassword !== body.confirmPassword) {
            throw new common_1.BadRequestException('Password confirmation does not match');
        }
        await this.passwordService.changePassword(user.id, body.currentPassword, body.newPassword);
        return { message: 'Password changed successfully' };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    (0, tenant_required_decorator_1.TenantRequired)(),
    (0, swagger_1.ApiOperation)({ summary: 'User login' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Login successful',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                data: {
                    type: 'object',
                    properties: {
                        user: { type: 'object' },
                        accessToken: { type: 'string' },
                        refreshToken: { type: 'string' },
                        expiresIn: { type: 'number' },
                    },
                },
                message: { type: 'string', example: 'Login successful' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid credentials' }),
    (0, swagger_1.ApiResponse)({ status: 429, description: 'Too many requests' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof login_dto_1.LoginDto !== "undefined" && login_dto_1.LoginDto) === "function" ? _c : Object, typeof (_d = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _d : Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    (0, tenant_required_decorator_1.TenantRequired)(),
    (0, swagger_1.ApiOperation)({ summary: 'User registration' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Registration successful',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                data: { type: 'object' },
                message: { type: 'string', example: 'Registration successful' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'User already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof register_dto_1.RegisterDto !== "undefined" && register_dto_1.RegisterDto) === "function" ? _f : Object, typeof (_g = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _g : Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Refresh access token' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Token refreshed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                data: {
                    type: 'object',
                    properties: {
                        user: { type: 'object' },
                        accessToken: { type: 'string' },
                        refreshToken: { type: 'string' },
                        expiresIn: { type: 'number' },
                    },
                },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid refresh token' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_j = typeof refresh_token_dto_1.RefreshTokenDto !== "undefined" && refresh_token_dto_1.RefreshTokenDto) === "function" ? _j : Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'User logout' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Logout successful',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Logout successful' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_l = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _l : Object, typeof (_m = typeof refresh_token_dto_1.RefreshTokenDto !== "undefined" && refresh_token_dto_1.RefreshTokenDto) === "function" ? _m : Object]),
    __metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user profile' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User profile retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                data: { type: 'object' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_p = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _p : Object]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    (0, tenant_required_decorator_1.TenantRequired)(),
    (0, swagger_1.ApiOperation)({ summary: 'Request password reset' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Password reset email sent',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Password reset email sent' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_r = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _r : Object]),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], AuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Reset password using token' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Password reset successful',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Password reset successful' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid or expired token' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_t = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _t : Object]),
    __metadata("design:returntype", typeof (_u = typeof Promise !== "undefined" && Promise) === "function" ? _u : Object)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Post)('change-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Change password for authenticated user' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Password changed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Password changed successfully' },
                timestamp: { type: 'string' },
                correlationId: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid current password' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_v = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _v : Object, Object]),
    __metadata("design:returntype", typeof (_w = typeof Promise !== "undefined" && Promise) === "function" ? _w : Object)
], AuthController.prototype, "changePassword", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('Authentication'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object, typeof (_b = typeof password_service_1.PasswordService !== "undefined" && password_service_1.PasswordService) === "function" ? _b : Object])
], AuthController);


/***/ }),
/* 60 */
/***/ ((module) => {

module.exports = require("express");

/***/ }),
/* 61 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoginDto = void 0;
const class_validator_1 = __webpack_require__(46);
const swagger_1 = __webpack_require__(4);
class LoginDto {
    email;
    password;
    rememberMe;
    deviceId;
    deviceType;
}
exports.LoginDto = LoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email address',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)({}, { message: 'Please provide a valid email address' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Email is required' }),
    __metadata("design:type", String)
], LoginDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User password',
        example: 'SecurePassword123!',
        minLength: 8,
    }),
    (0, class_validator_1.IsString)({ message: 'Password must be a string' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Password is required' }),
    (0, class_validator_1.MinLength)(8, { message: 'Password must be at least 8 characters long' }),
    __metadata("design:type", String)
], LoginDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Remember me option for extended session',
        example: false,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], LoginDto.prototype, "rememberMe", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Device identifier for tracking',
        example: 'web-chrome-12345',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoginDto.prototype, "deviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Device type',
        example: 'web',
        enum: ['web', 'mobile', 'desktop'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoginDto.prototype, "deviceType", void 0);


/***/ }),
/* 62 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RegisterDto = void 0;
const class_validator_1 = __webpack_require__(46);
const swagger_1 = __webpack_require__(4);
class RegisterDto {
    email;
    password;
    confirmPassword;
    firstName;
    lastName;
    phoneNumber;
    jobTitle;
    department;
}
exports.RegisterDto = RegisterDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email address',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)({}, { message: 'Please provide a valid email address' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Email is required' }),
    __metadata("design:type", String)
], RegisterDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User password',
        example: 'SecurePassword123!',
        minLength: 8,
    }),
    (0, class_validator_1.IsString)({ message: 'Password must be a string' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Password is required' }),
    (0, class_validator_1.MinLength)(8, { message: 'Password must be at least 8 characters long' }),
    (0, class_validator_1.Matches)(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/, {
        message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    }),
    __metadata("design:type", String)
], RegisterDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Password confirmation',
        example: 'SecurePassword123!',
    }),
    (0, class_validator_1.IsString)({ message: 'Password confirmation must be a string' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Password confirmation is required' }),
    __metadata("design:type", String)
], RegisterDto.prototype, "confirmPassword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User first name',
        example: 'John',
        maxLength: 50,
    }),
    (0, class_validator_1.IsString)({ message: 'First name must be a string' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'First name is required' }),
    (0, class_validator_1.MaxLength)(50, { message: 'First name must not exceed 50 characters' }),
    __metadata("design:type", String)
], RegisterDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User last name',
        example: 'Doe',
        maxLength: 50,
    }),
    (0, class_validator_1.IsString)({ message: 'Last name must be a string' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Last name is required' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Last name must not exceed 50 characters' }),
    __metadata("design:type", String)
], RegisterDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User phone number',
        example: '+1234567890',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Phone number must be a string' }),
    (0, class_validator_1.Matches)(/^\+?[1-9]\d{1,14}$/, {
        message: 'Please provide a valid phone number',
    }),
    __metadata("design:type", String)
], RegisterDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User job title',
        example: 'Software Engineer',
        required: false,
        maxLength: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Job title must be a string' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Job title must not exceed 100 characters' }),
    __metadata("design:type", String)
], RegisterDto.prototype, "jobTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User department',
        example: 'Engineering',
        required: false,
        maxLength: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Department must be a string' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Department must not exceed 100 characters' }),
    __metadata("design:type", String)
], RegisterDto.prototype, "department", void 0);


/***/ }),
/* 63 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RefreshTokenDto = void 0;
const class_validator_1 = __webpack_require__(46);
const swagger_1 = __webpack_require__(4);
class RefreshTokenDto {
    refreshToken;
}
exports.RefreshTokenDto = RefreshTokenDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Refresh token for generating new access token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    (0, class_validator_1.IsString)({ message: 'Refresh token must be a string' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Refresh token is required' }),
    __metadata("design:type", String)
], RefreshTokenDto.prototype, "refreshToken", void 0);


/***/ }),
/* 64 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtAuthGuard = void 0;
const common_1 = __webpack_require__(2);
const passport_1 = __webpack_require__(54);
const core_1 = __webpack_require__(1);
let JwtAuthGuard = class JwtAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
    reflector;
    constructor(reflector) {
        super();
        this.reflector = reflector;
    }
    canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride('isPublic', [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        return super.canActivate(context);
    }
    handleRequest(err, user, info, context) {
        if (err || !user) {
            throw err || new common_1.UnauthorizedException('Invalid or expired token');
        }
        return user;
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], JwtAuthGuard);


/***/ }),
/* 65 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CurrentUser = void 0;
const common_1 = __webpack_require__(2);
exports.CurrentUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
});


/***/ }),
/* 66 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtStrategy = void 0;
const common_1 = __webpack_require__(2);
const passport_1 = __webpack_require__(54);
const config_1 = __webpack_require__(3);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const passport_jwt_1 = __webpack_require__(67);
const user_entity_1 = __webpack_require__(28);
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy, 'jwt') {
    configService;
    userRepository;
    constructor(configService, userRepository) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('jwt.accessToken.secret'),
            issuer: configService.get('jwt.accessToken.issuer'),
            audience: configService.get('jwt.accessToken.audience'),
        });
        this.configService = configService;
        this.userRepository = userRepository;
    }
    async validate(payload) {
        const { sub: userId, tenantId } = payload;
        const whereCondition = { id: userId };
        if (tenantId) {
            whereCondition.tenantId = tenantId;
        }
        const user = await this.userRepository.findOne({
            where: whereCondition,
            relations: ['roles', 'permissions', 'tenant'],
        });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        if (!user.isActive) {
            throw new common_1.UnauthorizedException('User account is disabled');
        }
        if (user.isLocked) {
            throw new common_1.UnauthorizedException('User account is locked');
        }
        if (!user.isEmailVerified) {
            throw new common_1.UnauthorizedException('Email not verified');
        }
        if (user.isPasswordExpired()) {
            throw new common_1.UnauthorizedException('Password has expired');
        }
        return user;
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object])
], JwtStrategy);


/***/ }),
/* 67 */
/***/ ((module) => {

module.exports = require("passport-jwt");

/***/ }),
/* 68 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(8);
const user_entity_1 = __webpack_require__(28);
const user_service_1 = __webpack_require__(69);
const user_controller_1 = __webpack_require__(70);
let UserModule = class UserModule {
};
exports.UserModule = UserModule;
exports.UserModule = UserModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User]),
        ],
        controllers: [user_controller_1.UserController],
        providers: [user_service_1.UserService],
        exports: [user_service_1.UserService, typeorm_1.TypeOrmModule],
    })
], UserModule);


/***/ }),
/* 69 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const user_entity_1 = __webpack_require__(28);
let UserService = class UserService {
    userRepository;
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async findById(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['roles', 'permissions'],
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async findByEmail(email, tenantId) {
        const whereCondition = { email };
        if (tenantId) {
            whereCondition.tenantId = tenantId;
        }
        return this.userRepository.findOne({
            where: whereCondition,
            relations: ['roles', 'permissions'],
        });
    }
    async create(userData) {
        const user = this.userRepository.create(userData);
        return this.userRepository.save(user);
    }
    async update(id, userData) {
        await this.userRepository.update(id, userData);
        return this.findById(id);
    }
    async delete(id) {
        const user = await this.findById(id);
        user.softDelete();
        await this.userRepository.save(user);
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object])
], UserService);


/***/ }),
/* 70 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(4);
const user_service_1 = __webpack_require__(69);
const jwt_auth_guard_1 = __webpack_require__(64);
const user_entity_1 = __webpack_require__(28);
let UserController = class UserController {
    userService;
    constructor(userService) {
        this.userService = userService;
    }
    async findById(id) {
        return this.userService.findById(id);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User found',
        type: user_entity_1.User,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], UserController.prototype, "findById", null);
exports.UserController = UserController = __decorate([
    (0, swagger_1.ApiTags)('Users'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [typeof (_a = typeof user_service_1.UserService !== "undefined" && user_service_1.UserService) === "function" ? _a : Object])
], UserController);


/***/ }),
/* 71 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RbacModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(8);
const role_entity_1 = __webpack_require__(32);
const permission_entity_1 = __webpack_require__(33);
const user_role_entity_1 = __webpack_require__(34);
const rbac_service_1 = __webpack_require__(72);
const rbac_seeder_service_1 = __webpack_require__(73);
const rbac_controller_1 = __webpack_require__(74);
const roles_guard_1 = __webpack_require__(75);
const permissions_guard_1 = __webpack_require__(77);
const user_module_1 = __webpack_require__(68);
const common_module_1 = __webpack_require__(35);
let RbacModule = class RbacModule {
};
exports.RbacModule = RbacModule;
exports.RbacModule = RbacModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                role_entity_1.Role,
                permission_entity_1.Permission,
                user_role_entity_1.UserRole,
            ]),
            (0, common_1.forwardRef)(() => user_module_1.UserModule),
            common_module_1.CommonModule,
        ],
        controllers: [
            rbac_controller_1.RbacController,
        ],
        providers: [
            rbac_service_1.RbacService,
            rbac_seeder_service_1.RbacSeederService,
            roles_guard_1.RolesGuard,
            permissions_guard_1.PermissionsGuard,
        ],
        exports: [
            rbac_service_1.RbacService,
            rbac_seeder_service_1.RbacSeederService,
            roles_guard_1.RolesGuard,
            permissions_guard_1.PermissionsGuard,
            typeorm_1.TypeOrmModule,
        ],
    })
], RbacModule);


/***/ }),
/* 72 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RbacService_1;
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RbacService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const role_entity_1 = __webpack_require__(32);
const permission_entity_1 = __webpack_require__(33);
const user_role_entity_1 = __webpack_require__(34);
const user_entity_1 = __webpack_require__(28);
const correlation_service_1 = __webpack_require__(23);
let RbacService = RbacService_1 = class RbacService {
    roleRepository;
    permissionRepository;
    userRoleRepository;
    userRepository;
    winstonLogger;
    correlationService;
    logger = new common_1.Logger(RbacService_1.name);
    constructor(roleRepository, permissionRepository, userRoleRepository, userRepository, winstonLogger, correlationService) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.userRoleRepository = userRoleRepository;
        this.userRepository = userRepository;
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
    }
    async createRole(createRoleDto, createdBy) {
        const correlationId = this.correlationService.getCorrelationId();
        const existingRole = await this.roleRepository.findOne({
            where: {
                name: createRoleDto.name,
                tenantId: createRoleDto.tenantId,
                deletedAt: (0, typeorm_2.IsNull)(),
            },
        });
        if (existingRole) {
            throw new common_1.ConflictException('Role with this name already exists');
        }
        const role = this.roleRepository.create({
            ...createRoleDto,
            createdBy,
        });
        const savedRole = await this.roleRepository.save(role);
        this.winstonLogger.info('Role created successfully', {
            context: RbacService_1.name,
            correlationId,
            roleId: savedRole.id,
            roleName: savedRole.name,
            createdBy,
        });
        return savedRole;
    }
    async updateRole(id, updateRoleDto, updatedBy) {
        const correlationId = this.correlationService.getCorrelationId();
        const role = await this.findRoleById(id);
        if (role.isSystemRole) {
            throw new common_1.BadRequestException('System roles cannot be modified');
        }
        if (updateRoleDto.name && updateRoleDto.name !== role.name) {
            const existingRole = await this.roleRepository.findOne({
                where: {
                    name: updateRoleDto.name,
                    tenantId: role.tenantId,
                    id: (0, typeorm_2.Not)(id),
                    deletedAt: (0, typeorm_2.IsNull)(),
                },
            });
            if (existingRole) {
                throw new common_1.ConflictException('Role with this name already exists');
            }
        }
        Object.assign(role, updateRoleDto, { updatedBy });
        const updatedRole = await this.roleRepository.save(role);
        this.winstonLogger.info('Role updated successfully', {
            context: RbacService_1.name,
            correlationId,
            roleId: updatedRole.id,
            roleName: updatedRole.name,
            updatedBy,
        });
        return updatedRole;
    }
    async deleteRole(id, deletedBy) {
        const correlationId = this.correlationService.getCorrelationId();
        const role = await this.findRoleById(id);
        if (role.isSystemRole) {
            throw new common_1.BadRequestException('System roles cannot be deleted');
        }
        role.softDelete();
        await this.roleRepository.save(role);
        this.winstonLogger.info('Role deleted successfully', {
            context: RbacService_1.name,
            correlationId,
            roleId: role.id,
            roleName: role.name,
            deletedBy,
        });
    }
    async findRoleById(id) {
        const role = await this.roleRepository.findOne({
            where: { id, deletedAt: (0, typeorm_2.IsNull)() },
            relations: ['permissions'],
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        return role;
    }
    async findRolesByTenant(tenantId) {
        const whereCondition = { deletedAt: (0, typeorm_2.IsNull)() };
        if (tenantId) {
            whereCondition.tenantId = tenantId;
        }
        return this.roleRepository.find({
            where: whereCondition,
            relations: ['permissions'],
            order: { priority: 'DESC', name: 'ASC' },
        });
    }
    async createPermission(createPermissionDto, createdBy) {
        const correlationId = this.correlationService.getCorrelationId();
        const existingPermission = await this.permissionRepository.findOne({
            where: {
                name: createPermissionDto.name,
                deletedAt: (0, typeorm_2.IsNull)(),
            },
        });
        if (existingPermission) {
            throw new common_1.ConflictException('Permission with this name already exists');
        }
        const permission = this.permissionRepository.create({
            ...createPermissionDto,
            createdBy,
        });
        const savedPermission = await this.permissionRepository.save(permission);
        this.winstonLogger.info('Permission created successfully', {
            context: RbacService_1.name,
            correlationId,
            permissionId: savedPermission.id,
            permissionName: savedPermission.name,
            createdBy,
        });
        return savedPermission;
    }
    async findPermissionById(id) {
        const permission = await this.permissionRepository.findOne({
            where: { id, deletedAt: (0, typeorm_2.IsNull)() },
        });
        if (!permission) {
            throw new common_1.NotFoundException('Permission not found');
        }
        return permission;
    }
    async findAllPermissions() {
        return this.permissionRepository.find({
            where: { deletedAt: (0, typeorm_2.IsNull)() },
            order: { resource: 'ASC', action: 'ASC' },
        });
    }
    async assignPermissionsToRole(roleId, permissionIds) {
        const correlationId = this.correlationService.getCorrelationId();
        const role = await this.findRoleById(roleId);
        const permissions = await this.permissionRepository.find({
            where: { id: (0, typeorm_2.In)(permissionIds), deletedAt: (0, typeorm_2.IsNull)() },
        });
        if (permissions.length !== permissionIds.length) {
            throw new common_1.BadRequestException('Some permissions not found');
        }
        role.permissions = permissions;
        const updatedRole = await this.roleRepository.save(role);
        this.winstonLogger.info('Permissions assigned to role', {
            context: RbacService_1.name,
            correlationId,
            roleId,
            permissionIds,
        });
        return updatedRole;
    }
    async assignRoleToUser(userId, assignRoleDto, assignedBy) {
        const correlationId = this.correlationService.getCorrelationId();
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const role = await this.findRoleById(assignRoleDto.roleId);
        const existingUserRole = await this.userRoleRepository.findOne({
            where: {
                userId,
                roleId: assignRoleDto.roleId,
                status: user_role_entity_1.UserRoleStatus.ACTIVE,
            },
        });
        if (existingUserRole) {
            throw new common_1.ConflictException('User already has this role');
        }
        const userRole = this.userRoleRepository.create({
            userId,
            roleId: assignRoleDto.roleId,
            tenantId: user.tenantId,
            expiresAt: assignRoleDto.expiresAt,
            assignedBy,
            assignmentReason: assignRoleDto.reason,
        });
        userRole.activate(assignedBy);
        const savedUserRole = await this.userRoleRepository.save(userRole);
        this.winstonLogger.info('Role assigned to user', {
            context: RbacService_1.name,
            correlationId,
            userId,
            roleId: assignRoleDto.roleId,
            assignedBy,
        });
        return savedUserRole;
    }
    async revokeRoleFromUser(userId, roleId, revokedBy, reason) {
        const correlationId = this.correlationService.getCorrelationId();
        const userRole = await this.userRoleRepository.findOne({
            where: {
                userId,
                roleId,
                status: user_role_entity_1.UserRoleStatus.ACTIVE,
            },
        });
        if (!userRole) {
            throw new common_1.NotFoundException('User role assignment not found');
        }
        userRole.revoke(revokedBy, reason);
        await this.userRoleRepository.save(userRole);
        this.winstonLogger.info('Role revoked from user', {
            context: RbacService_1.name,
            correlationId,
            userId,
            roleId,
            revokedBy,
            reason,
        });
    }
    async getUserRoles(userId) {
        return this.userRoleRepository.find({
            where: { userId },
            relations: ['role', 'role.permissions'],
            order: { createdAt: 'DESC' },
        });
    }
    async checkUserPermission(userId, permissionName, context) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['roles', 'roles.permissions', 'permissions'],
        });
        if (!user) {
            return false;
        }
        return user.hasPermission(permissionName);
    }
    async checkUserRole(userId, roleName) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['roles'],
        });
        if (!user) {
            return false;
        }
        return user.hasRole(roleName);
    }
};
exports.RbacService = RbacService;
exports.RbacService = RbacService = RbacService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(1, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __param(2, (0, typeorm_1.InjectRepository)(user_role_entity_1.UserRole)),
    __param(3, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(4, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _e : Object, typeof (_f = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _f : Object])
], RbacService);


/***/ }),
/* 73 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RbacSeederService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RbacSeederService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(8);
const typeorm_2 = __webpack_require__(22);
const nest_winston_1 = __webpack_require__(13);
const winston_1 = __webpack_require__(19);
const role_entity_1 = __webpack_require__(32);
const permission_entity_1 = __webpack_require__(33);
const correlation_service_1 = __webpack_require__(23);
let RbacSeederService = RbacSeederService_1 = class RbacSeederService {
    roleRepository;
    permissionRepository;
    winstonLogger;
    correlationService;
    logger = new common_1.Logger(RbacSeederService_1.name);
    constructor(roleRepository, permissionRepository, winstonLogger, correlationService) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.winstonLogger = winstonLogger;
        this.correlationService = correlationService;
    }
    async seedDefaultPermissions() {
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Starting permission seeding', {
            context: RbacSeederService_1.name,
            correlationId,
        });
        const defaultPermissions = this.getDefaultPermissions();
        for (const permissionData of defaultPermissions) {
            const existingPermission = await this.permissionRepository.findOne({
                where: { name: permissionData.name },
            });
            if (!existingPermission) {
                const permission = this.permissionRepository.create(permissionData);
                await this.permissionRepository.save(permission);
                this.logger.log(`Created permission: ${permissionData.name}`);
            }
        }
        this.winstonLogger.info('Permission seeding completed', {
            context: RbacSeederService_1.name,
            correlationId,
            totalPermissions: defaultPermissions.length,
        });
    }
    async seedDefaultRoles() {
        const correlationId = this.correlationService.getCorrelationId();
        this.winstonLogger.info('Starting role seeding', {
            context: RbacSeederService_1.name,
            correlationId,
        });
        const defaultRoles = this.getDefaultRoles();
        for (const roleData of defaultRoles) {
            const existingRole = await this.roleRepository.findOne({
                where: { name: roleData.name },
            });
            if (!existingRole) {
                const permissions = await this.permissionRepository.find({
                    where: roleData.permissionNames.map(name => ({ name })),
                });
                const role = this.roleRepository.create({
                    ...roleData,
                    permissions,
                });
                await this.roleRepository.save(role);
                this.logger.log(`Created role: ${roleData.name} with ${permissions.length} permissions`);
            }
        }
        this.winstonLogger.info('Role seeding completed', {
            context: RbacSeederService_1.name,
            correlationId,
            totalRoles: defaultRoles.length,
        });
    }
    getDefaultPermissions() {
        return [
            { name: 'user:create', description: 'Create new users', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.USER, module: 'user-management', isSystemPermission: true },
            { name: 'user:read', description: 'View user information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.USER, module: 'user-management', isSystemPermission: true },
            { name: 'user:update', description: 'Update user information', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.USER, module: 'user-management', isSystemPermission: true },
            { name: 'user:delete', description: 'Delete users', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.USER, module: 'user-management', isSystemPermission: true },
            { name: 'role:create', description: 'Create new roles', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.ROLE, module: 'rbac', isSystemPermission: true },
            { name: 'role:read', description: 'View role information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.ROLE, module: 'rbac', isSystemPermission: true },
            { name: 'role:update', description: 'Update role information', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.ROLE, module: 'rbac', isSystemPermission: true },
            { name: 'role:delete', description: 'Delete roles', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.ROLE, module: 'rbac', isSystemPermission: true },
            { name: 'role:assign', description: 'Assign roles to users', action: permission_entity_1.PermissionAction.MANAGE, resource: permission_entity_1.PermissionResource.ROLE, module: 'rbac', isSystemPermission: true },
            { name: 'role:revoke', description: 'Revoke roles from users', action: permission_entity_1.PermissionAction.MANAGE, resource: permission_entity_1.PermissionResource.ROLE, module: 'rbac', isSystemPermission: true },
            { name: 'permission:create', description: 'Create new permissions', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.PERMISSION, module: 'rbac', isSystemPermission: true },
            { name: 'permission:read', description: 'View permission information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.PERMISSION, module: 'rbac', isSystemPermission: true },
            { name: 'permission:update', description: 'Update permission information', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.PERMISSION, module: 'rbac', isSystemPermission: true },
            { name: 'permission:delete', description: 'Delete permissions', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.PERMISSION, module: 'rbac', isSystemPermission: true },
            { name: 'permission:assign', description: 'Assign permissions to roles', action: permission_entity_1.PermissionAction.MANAGE, resource: permission_entity_1.PermissionResource.PERMISSION, module: 'rbac', isSystemPermission: true },
            { name: 'employee:create', description: 'Create new employees', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.EMPLOYEE, module: 'hr', isSystemPermission: true },
            { name: 'employee:read', description: 'View employee information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.EMPLOYEE, module: 'hr', isSystemPermission: true },
            { name: 'employee:update', description: 'Update employee information', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.EMPLOYEE, module: 'hr', isSystemPermission: true },
            { name: 'employee:delete', description: 'Delete employees', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.EMPLOYEE, module: 'hr', isSystemPermission: true },
            { name: 'department:create', description: 'Create new departments', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.DEPARTMENT, module: 'hr', isSystemPermission: true },
            { name: 'department:read', description: 'View department information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.DEPARTMENT, module: 'hr', isSystemPermission: true },
            { name: 'department:update', description: 'Update department information', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.DEPARTMENT, module: 'hr', isSystemPermission: true },
            { name: 'department:delete', description: 'Delete departments', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.DEPARTMENT, module: 'hr', isSystemPermission: true },
            { name: 'payroll:create', description: 'Create payroll records', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.PAYROLL, module: 'payroll', isSystemPermission: true },
            { name: 'payroll:read', description: 'View payroll information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.PAYROLL, module: 'payroll', isSystemPermission: true },
            { name: 'payroll:update', description: 'Update payroll records', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.PAYROLL, module: 'payroll', isSystemPermission: true },
            { name: 'payroll:delete', description: 'Delete payroll records', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.PAYROLL, module: 'payroll', isSystemPermission: true },
            { name: 'payroll:approve', description: 'Approve payroll records', action: permission_entity_1.PermissionAction.APPROVE, resource: permission_entity_1.PermissionResource.PAYROLL, module: 'payroll', isSystemPermission: true },
            { name: 'attendance:create', description: 'Create attendance records', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.ATTENDANCE, module: 'attendance', isSystemPermission: true },
            { name: 'attendance:read', description: 'View attendance information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.ATTENDANCE, module: 'attendance', isSystemPermission: true },
            { name: 'attendance:update', description: 'Update attendance records', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.ATTENDANCE, module: 'attendance', isSystemPermission: true },
            { name: 'attendance:delete', description: 'Delete attendance records', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.ATTENDANCE, module: 'attendance', isSystemPermission: true },
            { name: 'leave:create', description: 'Create leave requests', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.LEAVE, module: 'leave', isSystemPermission: true },
            { name: 'leave:read', description: 'View leave information', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.LEAVE, module: 'leave', isSystemPermission: true },
            { name: 'leave:update', description: 'Update leave requests', action: permission_entity_1.PermissionAction.UPDATE, resource: permission_entity_1.PermissionResource.LEAVE, module: 'leave', isSystemPermission: true },
            { name: 'leave:delete', description: 'Delete leave requests', action: permission_entity_1.PermissionAction.DELETE, resource: permission_entity_1.PermissionResource.LEAVE, module: 'leave', isSystemPermission: true },
            { name: 'leave:approve', description: 'Approve leave requests', action: permission_entity_1.PermissionAction.APPROVE, resource: permission_entity_1.PermissionResource.LEAVE, module: 'leave', isSystemPermission: true },
            { name: 'leave:reject', description: 'Reject leave requests', action: permission_entity_1.PermissionAction.REJECT, resource: permission_entity_1.PermissionResource.LEAVE, module: 'leave', isSystemPermission: true },
            { name: 'system:manage', description: 'Manage system settings', action: permission_entity_1.PermissionAction.MANAGE, resource: permission_entity_1.PermissionResource.SYSTEM, module: 'system', isSystemPermission: true },
            { name: 'audit:read', description: 'View audit logs', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.AUDIT, module: 'system', isSystemPermission: true },
            { name: 'report:read', description: 'View reports', action: permission_entity_1.PermissionAction.READ, resource: permission_entity_1.PermissionResource.REPORT, module: 'reporting', isSystemPermission: true },
            { name: 'report:create', description: 'Create reports', action: permission_entity_1.PermissionAction.CREATE, resource: permission_entity_1.PermissionResource.REPORT, module: 'reporting', isSystemPermission: true },
        ];
    }
    getDefaultRoles() {
        return [
            {
                name: 'super_admin',
                description: 'Super Administrator with full system access',
                type: role_entity_1.RoleType.SYSTEM,
                scope: role_entity_1.RoleScope.GLOBAL,
                isSystemRole: true,
                priority: 1000,
                permissionNames: [
                    'user:create', 'user:read', 'user:update', 'user:delete',
                    'role:create', 'role:read', 'role:update', 'role:delete', 'role:assign', 'role:revoke',
                    'permission:create', 'permission:read', 'permission:update', 'permission:delete', 'permission:assign',
                    'employee:create', 'employee:read', 'employee:update', 'employee:delete',
                    'department:create', 'department:read', 'department:update', 'department:delete',
                    'payroll:create', 'payroll:read', 'payroll:update', 'payroll:delete', 'payroll:approve',
                    'attendance:create', 'attendance:read', 'attendance:update', 'attendance:delete',
                    'leave:create', 'leave:read', 'leave:update', 'leave:delete', 'leave:approve', 'leave:reject',
                    'system:manage', 'audit:read', 'report:read', 'report:create',
                ],
            },
            {
                name: 'tenant_admin',
                description: 'Tenant Administrator with full tenant access',
                type: role_entity_1.RoleType.TENANT,
                scope: role_entity_1.RoleScope.TENANT,
                isSystemRole: true,
                priority: 900,
                permissionNames: [
                    'user:create', 'user:read', 'user:update', 'user:delete',
                    'role:read', 'role:assign', 'role:revoke',
                    'employee:create', 'employee:read', 'employee:update', 'employee:delete',
                    'department:create', 'department:read', 'department:update', 'department:delete',
                    'payroll:create', 'payroll:read', 'payroll:update', 'payroll:delete', 'payroll:approve',
                    'attendance:create', 'attendance:read', 'attendance:update', 'attendance:delete',
                    'leave:create', 'leave:read', 'leave:update', 'leave:delete', 'leave:approve', 'leave:reject',
                    'report:read', 'report:create',
                ],
            },
            {
                name: 'hr_manager',
                description: 'HR Manager with HR module access',
                type: role_entity_1.RoleType.TENANT,
                scope: role_entity_1.RoleScope.TENANT,
                isSystemRole: true,
                priority: 800,
                permissionNames: [
                    'user:read', 'user:update',
                    'employee:create', 'employee:read', 'employee:update', 'employee:delete',
                    'department:read', 'department:update',
                    'payroll:read', 'payroll:update', 'payroll:approve',
                    'attendance:read', 'attendance:update',
                    'leave:read', 'leave:approve', 'leave:reject',
                    'report:read',
                ],
            },
            {
                name: 'hr_employee',
                description: 'HR Employee with limited HR access',
                type: role_entity_1.RoleType.TENANT,
                scope: role_entity_1.RoleScope.TENANT,
                isSystemRole: true,
                priority: 700,
                permissionNames: [
                    'employee:read', 'employee:update',
                    'department:read',
                    'attendance:read', 'attendance:update',
                    'leave:read', 'leave:create', 'leave:update',
                ],
            },
            {
                name: 'employee',
                description: 'Regular Employee with basic access',
                type: role_entity_1.RoleType.TENANT,
                scope: role_entity_1.RoleScope.TENANT,
                isSystemRole: true,
                isDefault: true,
                priority: 100,
                permissionNames: [
                    'employee:read',
                    'attendance:read',
                    'leave:read', 'leave:create',
                ],
            },
        ];
    }
};
exports.RbacSeederService = RbacSeederService;
exports.RbacSeederService = RbacSeederService = RbacSeederService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(1, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __param(2, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof winston_1.Logger !== "undefined" && winston_1.Logger) === "function" ? _c : Object, typeof (_d = typeof correlation_service_1.CorrelationService !== "undefined" && correlation_service_1.CorrelationService) === "function" ? _d : Object])
], RbacSeederService);


/***/ }),
/* 74 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RbacController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(4);
const jwt_auth_guard_1 = __webpack_require__(64);
const roles_guard_1 = __webpack_require__(75);
const permissions_guard_1 = __webpack_require__(77);
const roles_decorator_1 = __webpack_require__(76);
const permissions_decorator_1 = __webpack_require__(78);
const rbac_service_1 = __webpack_require__(72);
const create_role_dto_1 = __webpack_require__(79);
const update_role_dto_1 = __webpack_require__(80);
const create_permission_dto_1 = __webpack_require__(81);
const assign_role_dto_1 = __webpack_require__(82);
const role_entity_1 = __webpack_require__(32);
const permission_entity_1 = __webpack_require__(33);
const user_role_entity_1 = __webpack_require__(34);
let RbacController = class RbacController {
    rbacService;
    constructor(rbacService) {
        this.rbacService = rbacService;
    }
    async createRole(createRoleDto, req) {
        return this.rbacService.createRole(createRoleDto, req.user.id);
    }
    async getRoles(tenantId) {
        return this.rbacService.findRolesByTenant(tenantId);
    }
    async getRoleById(id) {
        return this.rbacService.findRoleById(id);
    }
    async updateRole(id, updateRoleDto, req) {
        return this.rbacService.updateRole(id, updateRoleDto, req.user.id);
    }
    async deleteRole(id, req) {
        return this.rbacService.deleteRole(id, req.user.id);
    }
    async createPermission(createPermissionDto, req) {
        return this.rbacService.createPermission(createPermissionDto, req.user.id);
    }
    async getPermissions() {
        return this.rbacService.findAllPermissions();
    }
    async getPermissionById(id) {
        return this.rbacService.findPermissionById(id);
    }
    async assignPermissionsToRole(roleId, body) {
        return this.rbacService.assignPermissionsToRole(roleId, body.permissionIds);
    }
    async assignRoleToUser(userId, assignRoleDto, req) {
        return this.rbacService.assignRoleToUser(userId, assignRoleDto, req.user.id);
    }
    async revokeRoleFromUser(userId, roleId, req, reason) {
        return this.rbacService.revokeRoleFromUser(userId, roleId, req.user.id, reason);
    }
    async getUserRoles(userId) {
        return this.rbacService.getUserRoles(userId);
    }
    async checkUserPermission(userId, permission) {
        const hasPermission = await this.rbacService.checkUserPermission(userId, permission);
        return { hasPermission };
    }
    async checkUserRole(userId, role) {
        const hasRole = await this.rbacService.checkUserRole(userId, role);
        return { hasRole };
    }
};
exports.RbacController = RbacController;
__decorate([
    (0, common_1.Post)('roles'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin'),
    (0, permissions_decorator_1.Permissions)('role:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new role' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Role created successfully', type: role_entity_1.Role }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Role already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_role_dto_1.CreateRoleDto !== "undefined" && create_role_dto_1.CreateRoleDto) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], RbacController.prototype, "createRole", null);
__decorate([
    (0, common_1.Get)('roles'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('role:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all roles' }),
    (0, swagger_1.ApiQuery)({ name: 'tenantId', required: false, description: 'Filter by tenant ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Roles retrieved successfully', type: [role_entity_1.Role] }),
    __param(0, (0, common_1.Query)('tenantId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], RbacController.prototype, "getRoles", null);
__decorate([
    (0, common_1.Get)('roles/:id'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('role:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get role by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Role ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Role retrieved successfully', type: role_entity_1.Role }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], RbacController.prototype, "getRoleById", null);
__decorate([
    (0, common_1.Put)('roles/:id'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin'),
    (0, permissions_decorator_1.Permissions)('role:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Update role' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Role ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Role updated successfully', type: role_entity_1.Role }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_f = typeof update_role_dto_1.UpdateRoleDto !== "undefined" && update_role_dto_1.UpdateRoleDto) === "function" ? _f : Object, Object]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], RbacController.prototype, "updateRole", null);
__decorate([
    (0, common_1.Delete)('roles/:id'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin'),
    (0, permissions_decorator_1.Permissions)('role:delete'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete role' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Role ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Role deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], RbacController.prototype, "deleteRole", null);
__decorate([
    (0, common_1.Post)('permissions'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin'),
    (0, permissions_decorator_1.Permissions)('permission:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new permission' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Permission created successfully', type: permission_entity_1.Permission }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Permission already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_j = typeof create_permission_dto_1.CreatePermissionDto !== "undefined" && create_permission_dto_1.CreatePermissionDto) === "function" ? _j : Object, Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], RbacController.prototype, "createPermission", null);
__decorate([
    (0, common_1.Get)('permissions'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('permission:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all permissions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Permissions retrieved successfully', type: [permission_entity_1.Permission] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], RbacController.prototype, "getPermissions", null);
__decorate([
    (0, common_1.Get)('permissions/:id'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('permission:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get permission by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Permission ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Permission retrieved successfully', type: permission_entity_1.Permission }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Permission not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], RbacController.prototype, "getPermissionById", null);
__decorate([
    (0, common_1.Put)('roles/:roleId/permissions'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin'),
    (0, permissions_decorator_1.Permissions)('role:update', 'permission:assign'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign permissions to role' }),
    (0, swagger_1.ApiParam)({ name: 'roleId', description: 'Role ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Permissions assigned successfully', type: role_entity_1.Role }),
    __param(0, (0, common_1.Param)('roleId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], RbacController.prototype, "assignPermissionsToRole", null);
__decorate([
    (0, common_1.Post)('users/:userId/roles'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('user:update', 'role:assign'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign role to user' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Role assigned successfully', type: user_role_entity_1.UserRole }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_p = typeof assign_role_dto_1.AssignRoleDto !== "undefined" && assign_role_dto_1.AssignRoleDto) === "function" ? _p : Object, Object]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], RbacController.prototype, "assignRoleToUser", null);
__decorate([
    (0, common_1.Delete)('users/:userId/roles/:roleId'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('user:update', 'role:revoke'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Revoke role from user' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: 'User ID' }),
    (0, swagger_1.ApiParam)({ name: 'roleId', description: 'Role ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Role revoked successfully' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('roleId')),
    __param(2, (0, common_1.Request)()),
    __param(3, (0, common_1.Query)('reason')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, String]),
    __metadata("design:returntype", typeof (_r = typeof Promise !== "undefined" && Promise) === "function" ? _r : Object)
], RbacController.prototype, "revokeRoleFromUser", null);
__decorate([
    (0, common_1.Get)('users/:userId/roles'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('user:read', 'role:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user roles' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User roles retrieved successfully', type: [user_role_entity_1.UserRole] }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], RbacController.prototype, "getUserRoles", null);
__decorate([
    (0, common_1.Get)('users/:userId/check-permission/:permission'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('user:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Check if user has specific permission' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: 'User ID' }),
    (0, swagger_1.ApiParam)({ name: 'permission', description: 'Permission name' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Permission check result' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('permission')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", typeof (_t = typeof Promise !== "undefined" && Promise) === "function" ? _t : Object)
], RbacController.prototype, "checkUserPermission", null);
__decorate([
    (0, common_1.Get)('users/:userId/check-role/:role'),
    (0, roles_decorator_1.Roles)('admin', 'super_admin', 'hr_manager'),
    (0, permissions_decorator_1.Permissions)('user:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Check if user has specific role' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: 'User ID' }),
    (0, swagger_1.ApiParam)({ name: 'role', description: 'Role name' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Role check result' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", typeof (_u = typeof Promise !== "undefined" && Promise) === "function" ? _u : Object)
], RbacController.prototype, "checkUserRole", null);
exports.RbacController = RbacController = __decorate([
    (0, swagger_1.ApiTags)('RBAC'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('rbac'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard, permissions_guard_1.PermissionsGuard),
    __metadata("design:paramtypes", [typeof (_a = typeof rbac_service_1.RbacService !== "undefined" && rbac_service_1.RbacService) === "function" ? _a : Object])
], RbacController);


/***/ }),
/* 75 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RolesGuard_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RolesGuard = void 0;
const common_1 = __webpack_require__(2);
const core_1 = __webpack_require__(1);
const roles_decorator_1 = __webpack_require__(76);
let RolesGuard = RolesGuard_1 = class RolesGuard {
    reflector;
    logger = new common_1.Logger(RolesGuard_1.name);
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.getAllAndOverride(roles_decorator_1.ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredRoles || requiredRoles.length === 0) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            this.logger.warn('No user found in request for roles check');
            throw new common_1.ForbiddenException('Authentication required');
        }
        const hasRole = this.checkUserRoles(user, requiredRoles);
        if (!hasRole) {
            this.logger.warn('User does not have required roles', {
                userId: user.id,
                userRoles: user.roles?.map(role => role.name) || [],
                requiredRoles,
            });
            throw new common_1.ForbiddenException('Insufficient permissions');
        }
        return true;
    }
    checkUserRoles(user, requiredRoles) {
        if (!user.roles || user.roles.length === 0) {
            return false;
        }
        return requiredRoles.some(role => user.hasRole(role));
    }
};
exports.RolesGuard = RolesGuard;
exports.RolesGuard = RolesGuard = RolesGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], RolesGuard);


/***/ }),
/* 76 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Roles = exports.ROLES_KEY = void 0;
const common_1 = __webpack_require__(2);
exports.ROLES_KEY = 'roles';
const Roles = (...roles) => (0, common_1.SetMetadata)(exports.ROLES_KEY, roles);
exports.Roles = Roles;


/***/ }),
/* 77 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PermissionsGuard_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PermissionsGuard = void 0;
const common_1 = __webpack_require__(2);
const core_1 = __webpack_require__(1);
const permissions_decorator_1 = __webpack_require__(78);
let PermissionsGuard = PermissionsGuard_1 = class PermissionsGuard {
    reflector;
    logger = new common_1.Logger(PermissionsGuard_1.name);
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredPermissions = this.reflector.getAllAndOverride(permissions_decorator_1.PERMISSIONS_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredPermissions || requiredPermissions.length === 0) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            this.logger.warn('No user found in request for permissions check');
            throw new common_1.ForbiddenException('Authentication required');
        }
        const hasPermission = this.checkUserPermissions(user, requiredPermissions);
        if (!hasPermission) {
            this.logger.warn('User does not have required permissions', {
                userId: user.id,
                userPermissions: user.getActivePermissions().map(permission => permission.name),
                requiredPermissions,
            });
            throw new common_1.ForbiddenException('Insufficient permissions');
        }
        return true;
    }
    checkUserPermissions(user, requiredPermissions) {
        return requiredPermissions.some(permission => user.hasPermission(permission));
    }
};
exports.PermissionsGuard = PermissionsGuard;
exports.PermissionsGuard = PermissionsGuard = PermissionsGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], PermissionsGuard);


/***/ }),
/* 78 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Permissions = exports.PERMISSIONS_KEY = void 0;
const common_1 = __webpack_require__(2);
exports.PERMISSIONS_KEY = 'permissions';
const Permissions = (...permissions) => (0, common_1.SetMetadata)(exports.PERMISSIONS_KEY, permissions);
exports.Permissions = Permissions;


/***/ }),
/* 79 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateRoleDto = void 0;
const class_validator_1 = __webpack_require__(46);
const swagger_1 = __webpack_require__(4);
const role_entity_1 = __webpack_require__(32);
class CreateRoleDto {
    name;
    description;
    type;
    scope;
    tenantId;
    departmentId;
    teamId;
    isActive;
    isDefault;
    priority;
    metadata;
}
exports.CreateRoleDto = CreateRoleDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role name',
        example: 'hr_manager',
        minLength: 2,
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 100),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Role description',
        example: 'Human Resources Manager with full HR module access',
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Role type',
        enum: role_entity_1.RoleType,
        default: role_entity_1.RoleType.CUSTOM,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(role_entity_1.RoleType),
    __metadata("design:type", typeof (_a = typeof role_entity_1.RoleType !== "undefined" && role_entity_1.RoleType) === "function" ? _a : Object)
], CreateRoleDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Role scope',
        enum: role_entity_1.RoleScope,
        default: role_entity_1.RoleScope.TENANT,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(role_entity_1.RoleScope),
    __metadata("design:type", typeof (_b = typeof role_entity_1.RoleScope !== "undefined" && role_entity_1.RoleScope) === "function" ? _b : Object)
], CreateRoleDto.prototype, "scope", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tenant ID for tenant-scoped roles',
        example: 'tenant-123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "tenantId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Department ID for department-scoped roles',
        example: 'dept-456',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "departmentId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Team ID for team-scoped roles',
        example: 'team-789',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "teamId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether the role is active',
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateRoleDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether this is a default role for new users',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateRoleDto.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Role priority (higher values take precedence)',
        example: 100,
        minimum: 0,
        maximum: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], CreateRoleDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata for the role',
        example: { department: 'HR', level: 'manager' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], CreateRoleDto.prototype, "metadata", void 0);


/***/ }),
/* 80 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateRoleDto = void 0;
const swagger_1 = __webpack_require__(4);
const create_role_dto_1 = __webpack_require__(79);
class UpdateRoleDto extends (0, swagger_1.PartialType)(create_role_dto_1.CreateRoleDto) {
}
exports.UpdateRoleDto = UpdateRoleDto;


/***/ }),
/* 81 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreatePermissionDto = void 0;
const class_validator_1 = __webpack_require__(46);
const swagger_1 = __webpack_require__(4);
const permission_entity_1 = __webpack_require__(33);
class CreatePermissionDto {
    name;
    description;
    type;
    action;
    resource;
    module;
    isActive;
    priority;
    conditions;
    metadata;
}
exports.CreatePermissionDto = CreatePermissionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permission name (usually resource:action format)',
        example: 'user:create',
        minLength: 2,
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 100),
    __metadata("design:type", String)
], CreatePermissionDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Permission description',
        example: 'Allows creating new users in the system',
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], CreatePermissionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Permission type',
        enum: permission_entity_1.PermissionType,
        default: permission_entity_1.PermissionType.RESOURCE,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(permission_entity_1.PermissionType),
    __metadata("design:type", typeof (_a = typeof permission_entity_1.PermissionType !== "undefined" && permission_entity_1.PermissionType) === "function" ? _a : Object)
], CreatePermissionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permission action',
        enum: permission_entity_1.PermissionAction,
        example: permission_entity_1.PermissionAction.CREATE,
    }),
    (0, class_validator_1.IsEnum)(permission_entity_1.PermissionAction),
    __metadata("design:type", typeof (_b = typeof permission_entity_1.PermissionAction !== "undefined" && permission_entity_1.PermissionAction) === "function" ? _b : Object)
], CreatePermissionDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permission resource',
        enum: permission_entity_1.PermissionResource,
        example: permission_entity_1.PermissionResource.USER,
    }),
    (0, class_validator_1.IsEnum)(permission_entity_1.PermissionResource),
    __metadata("design:type", typeof (_c = typeof permission_entity_1.PermissionResource !== "undefined" && permission_entity_1.PermissionResource) === "function" ? _c : Object)
], CreatePermissionDto.prototype, "resource", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'HR module this permission belongs to',
        example: 'user-management',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePermissionDto.prototype, "module", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether the permission is active',
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePermissionDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Permission priority for hierarchy',
        example: 100,
        minimum: 0,
        maximum: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], CreatePermissionDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional conditions for permission evaluation',
        example: { ownResource: true, departmentOnly: true },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_d = typeof Record !== "undefined" && Record) === "function" ? _d : Object)
], CreatePermissionDto.prototype, "conditions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata for the permission',
        example: { category: 'user-management', riskLevel: 'medium' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], CreatePermissionDto.prototype, "metadata", void 0);


/***/ }),
/* 82 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AssignRoleDto = void 0;
const class_validator_1 = __webpack_require__(46);
const swagger_1 = __webpack_require__(4);
class AssignRoleDto {
    roleId;
    expiresAt;
    reason;
}
exports.AssignRoleDto = AssignRoleDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role ID to assign',
        example: 'role-123-456-789',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AssignRoleDto.prototype, "roleId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Role assignment expiration date (ISO string)',
        example: '2024-12-31T23:59:59.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], AssignRoleDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Reason for role assignment',
        example: 'Promoted to HR Manager position',
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], AssignRoleDto.prototype, "reason", void 0);


/***/ }),
/* 83 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.TenantModule = void 0;
const common_1 = __webpack_require__(2);
let TenantModule = class TenantModule {
};
exports.TenantModule = TenantModule;
exports.TenantModule = TenantModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [],
        providers: [],
        exports: [],
    })
], TenantModule);


/***/ }),
/* 84 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthModule = void 0;
const common_1 = __webpack_require__(2);
let HealthModule = class HealthModule {
};
exports.HealthModule = HealthModule;
exports.HealthModule = HealthModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [],
        providers: [],
        exports: [],
    })
], HealthModule);


/***/ })
/******/ 	]);
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(1);
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(3);
const swagger_1 = __webpack_require__(4);
const helmet_1 = __webpack_require__(5);
const compression = __webpack_require__(6);
const app_module_1 = __webpack_require__(7);
const nest_winston_1 = __webpack_require__(13);
const winston_config_1 = __webpack_require__(18);
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    try {
        const winstonLogger = nest_winston_1.WinstonModule.createLogger((0, winston_config_1.createWinstonConfig)());
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger: winstonLogger,
            cors: {
                origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
                credentials: true,
                methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
                allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID', 'X-Correlation-ID'],
            },
        });
        const configService = app.get(config_1.ConfigService);
        const port = configService.get('PORT', 3001);
        const environment = configService.get('NODE_ENV', 'development');
        app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
            crossOriginEmbedderPolicy: false,
        }));
        app.use(compression());
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
            transformOptions: {
                enableImplicitConversion: false,
            },
            validateCustomDecorators: true,
            forbidUnknownValues: true,
        }));
        app.setGlobalPrefix('api/v1');
        if (environment === 'development') {
            const config = new swagger_1.DocumentBuilder()
                .setTitle('PeopleNest HRMS API')
                .setDescription('AI-Enabled Enterprise HRMS Platform API Documentation')
                .setVersion('1.0')
                .addBearerAuth({
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                name: 'JWT',
                description: 'Enter JWT token',
                in: 'header',
            }, 'JWT-auth')
                .addApiKey({
                type: 'apiKey',
                name: 'X-Tenant-ID',
                in: 'header',
                description: 'Tenant identifier for multi-tenancy',
            }, 'tenant-key')
                .build();
            const document = swagger_1.SwaggerModule.createDocument(app, config);
            swagger_1.SwaggerModule.setup('api/docs', app, document, {
                swaggerOptions: {
                    persistAuthorization: true,
                },
            });
        }
        process.on('SIGTERM', async () => {
            logger.log('SIGTERM received, shutting down gracefully');
            await app.close();
            process.exit(0);
        });
        process.on('SIGINT', async () => {
            logger.log('SIGINT received, shutting down gracefully');
            await app.close();
            process.exit(0);
        });
        await app.listen(port);
        logger.log(`🚀 PeopleNest HRMS is running on: http://localhost:${port}`);
        logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
        logger.log(`🌍 Environment: ${environment}`);
    }
    catch (error) {
        logger.error('Failed to start application', error);
        process.exit(1);
    }
}
bootstrap();

})();

/******/ })()
;