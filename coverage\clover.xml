<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751361656331" clover="3.2.0">
  <project timestamp="1751361656331" name="All files">
    <metrics statements="10" coveredstatements="0" conditionals="116" coveredconditionals="0" methods="7" coveredmethods="0" elements="133" coveredelements="0" complexity="0" loc="10" ncloc="10" packages="2" files="5" classes="5"/>
    <package name="common.decorators">
      <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="skip-transform.decorator.ts" path="C:\PeopleNest\src\common\decorators\skip-transform.decorator.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
      <file name="tenant-required.decorator.ts" path="C:\PeopleNest\src\common\decorators\tenant-required.decorator.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
      </file>
    </package>
    <package name="config">
      <metrics statements="5" coveredstatements="0" conditionals="116" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="database.config.ts" path="C:\PeopleNest\src\config\database.config.ts">
        <metrics statements="1" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="jwt.config.ts" path="C:\PeopleNest\src\config\jwt.config.ts">
        <metrics statements="1" coveredstatements="0" conditionals="58" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="redis.config.ts" path="C:\PeopleNest\src\config\redis.config.ts">
        <metrics statements="3" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
