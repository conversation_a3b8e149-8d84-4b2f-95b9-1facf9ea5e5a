import { Injectable, Logger, Inject, OnModuleInit } from '@nestjs/common';
import { DataSource, QueryRunner } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger as WinstonLogger } from 'winston';

// Services
import { CorrelationService } from '@common/services/correlation.service';

export interface DatabaseHealthCheck {
  isHealthy: boolean;
  responseTime: number;
  activeConnections: number;
  maxConnections: number;
  error?: string;
}

export interface DatabaseStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingConnections: number;
  maxConnections: number;
  uptime: number;
}

@Injectable()
export class DatabaseService implements OnModuleInit {
  private readonly logger = new Logger(DatabaseService.name);
  private readonly startTime = Date.now();

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
    @Inject(WINSTON_MODULE_PROVIDER)
    private readonly winstonLogger: WinstonLogger,
    private readonly correlationService: CorrelationService,
  ) {}

  async onModuleInit(): Promise<void> {
    const correlationId = this.correlationService.getCorrelationId();

    this.winstonLogger.info('Initializing database service', {
      context: DatabaseService.name,
      correlationId,
    });

    // Verify database connection
    try {
      await this.healthCheck();
      this.logger.log('Database connection established successfully');
    } catch (error) {
      this.winstonLogger.error('Failed to establish database connection', {
        context: DatabaseService.name,
        correlationId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Perform database health check
   */
  async healthCheck(): Promise<DatabaseHealthCheck> {
    const startTime = Date.now();
    const correlationId = this.correlationService.getCorrelationId();

    try {
      // Simple query to test connection
      await this.dataSource.query('SELECT 1');
      
      const responseTime = Date.now() - startTime;
      const stats = await this.getConnectionStats();

      const healthCheck: DatabaseHealthCheck = {
        isHealthy: true,
        responseTime,
        activeConnections: stats.activeConnections,
        maxConnections: stats.maxConnections,
      };

      this.winstonLogger.debug('Database health check successful', {
        context: DatabaseService.name,
        correlationId,
        healthCheck,
      });

      return healthCheck;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      const healthCheck: DatabaseHealthCheck = {
        isHealthy: false,
        responseTime,
        activeConnections: 0,
        maxConnections: 0,
        error: error.message,
      };

      this.winstonLogger.error('Database health check failed', {
        context: DatabaseService.name,
        correlationId,
        healthCheck,
        error: error.message,
      });

      return healthCheck;
    }
  }

  /**
   * Get database connection statistics
   */
  async getConnectionStats(): Promise<DatabaseStats> {
    try {
      // Get connection pool information
      const driver = this.dataSource.driver as any;
      const pool = driver.master || driver.pool;

      const stats: DatabaseStats = {
        totalConnections: pool?.totalCount || 0,
        activeConnections: pool?.acquiredCount || 0,
        idleConnections: pool?.freeCount || 0,
        waitingConnections: pool?.pendingCount || 0,
        maxConnections: pool?.max || 0,
        uptime: Date.now() - this.startTime,
      };

      return stats;
    } catch (error) {
      this.logger.warn('Failed to get connection stats', error.message);
      return {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        waitingConnections: 0,
        maxConnections: 0,
        uptime: Date.now() - this.startTime,
      };
    }
  }

  /**
   * Create a new query runner for transactions
   */
  createQueryRunner(): QueryRunner {
    return this.dataSource.createQueryRunner();
  }

  /**
   * Execute a transaction with automatic rollback on error
   */
  async executeTransaction<T>(
    operation: (queryRunner: QueryRunner) => Promise<T>,
  ): Promise<T> {
    const queryRunner = this.createQueryRunner();
    const correlationId = this.correlationService.getCorrelationId();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.winstonLogger.debug('Starting database transaction', {
        context: DatabaseService.name,
        correlationId,
      });

      const result = await operation(queryRunner);

      await queryRunner.commitTransaction();

      this.winstonLogger.debug('Database transaction committed successfully', {
        context: DatabaseService.name,
        correlationId,
      });

      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      this.winstonLogger.error('Database transaction rolled back', {
        context: DatabaseService.name,
        correlationId,
        error: error.message,
      });

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Check if a schema exists
   */
  async schemaExists(schemaName: string): Promise<boolean> {
    try {
      const result = await this.dataSource.query(
        'SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1',
        [schemaName],
      );
      return result.length > 0;
    } catch (error) {
      this.logger.error(`Failed to check if schema ${schemaName} exists`, error.message);
      return false;
    }
  }

  /**
   * Create a new schema
   */
  async createSchema(schemaName: string): Promise<void> {
    const correlationId = this.correlationService.getCorrelationId();

    try {
      await this.dataSource.query(`CREATE SCHEMA IF NOT EXISTS "${schemaName}"`);
      
      this.winstonLogger.info('Schema created successfully', {
        context: DatabaseService.name,
        correlationId,
        schemaName,
      });
    } catch (error) {
      this.winstonLogger.error('Failed to create schema', {
        context: DatabaseService.name,
        correlationId,
        schemaName,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Drop a schema
   */
  async dropSchema(schemaName: string, cascade = false): Promise<void> {
    const correlationId = this.correlationService.getCorrelationId();

    try {
      const cascadeClause = cascade ? 'CASCADE' : 'RESTRICT';
      await this.dataSource.query(`DROP SCHEMA IF EXISTS "${schemaName}" ${cascadeClause}`);
      
      this.winstonLogger.info('Schema dropped successfully', {
        context: DatabaseService.name,
        correlationId,
        schemaName,
        cascade,
      });
    } catch (error) {
      this.winstonLogger.error('Failed to drop schema', {
        context: DatabaseService.name,
        correlationId,
        schemaName,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get all schemas
   */
  async getAllSchemas(): Promise<string[]> {
    try {
      const result = await this.dataSource.query(
        `SELECT schema_name FROM information_schema.schemata 
         WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
         ORDER BY schema_name`,
      );
      return result.map(row => row.schema_name);
    } catch (error) {
      this.logger.error('Failed to get all schemas', error.message);
      return [];
    }
  }

  /**
   * Get database version and info
   */
  async getDatabaseInfo(): Promise<any> {
    try {
      const versionResult = await this.dataSource.query('SELECT version()');
      const sizeResult = await this.dataSource.query(
        `SELECT pg_size_pretty(pg_database_size(current_database())) as size`,
      );

      const options = this.dataSource.options as any;

      return {
        version: versionResult[0]?.version,
        size: sizeResult[0]?.size,
        name: options.database,
        host: options.host,
        port: options.port,
      };
    } catch (error) {
      this.logger.error('Failed to get database info', error.message);
      return null;
    }
  }

  /**
   * Get the main data source
   */
  getDataSource(): DataSource {
    return this.dataSource;
  }
}
