import { HttpStatus } from '@nestjs/common';

/**
 * Standard API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  statusCode: number;
  message: string;
  data?: T;
  meta?: ResponseMeta;
  correlationId?: string;
  timestamp: string;
}

/**
 * Pagination metadata interface
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * Response metadata interface
 */
export interface ResponseMeta {
  pagination?: PaginationMeta;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    order: 'ASC' | 'DESC';
  };
  executionTime?: number;
  version?: string;
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  success: false;
  statusCode: number;
  message: string;
  error: string;
  errors?: string[];
  correlationId?: string;
  timestamp: string;
  path?: string;
  method?: string;
}

/**
 * Response utility class for creating consistent API responses
 */
export class ResponseUtil {
  /**
   * Create a successful response
   */
  static success<T>(
    data: T,
    message: string = 'Operation successful',
    statusCode: number = HttpStatus.OK,
    meta?: ResponseMeta,
    correlationId?: string,
  ): ApiResponse<T> {
    return {
      success: true,
      statusCode,
      message,
      data,
      meta,
      correlationId,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create a successful response without data
   */
  static successNoData(
    message: string = 'Operation successful',
    statusCode: number = HttpStatus.OK,
    correlationId?: string,
  ): Omit<ApiResponse, 'data'> {
    return {
      success: true,
      statusCode,
      message,
      correlationId,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create a paginated response
   */
  static paginated<T>(
    data: T[],
    pagination: PaginationMeta,
    message: string = 'Data retrieved successfully',
    correlationId?: string,
  ): ApiResponse<T[]> {
    return {
      success: true,
      statusCode: HttpStatus.OK,
      message,
      data,
      meta: { pagination },
      correlationId,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create a created response (201)
   */
  static created<T>(
    data: T,
    message: string = 'Resource created successfully',
    correlationId?: string,
  ): ApiResponse<T> {
    return this.success(data, message, HttpStatus.CREATED, undefined, correlationId);
  }

  /**
   * Create an updated response
   */
  static updated<T>(
    data: T,
    message: string = 'Resource updated successfully',
    correlationId?: string,
  ): ApiResponse<T> {
    return this.success(data, message, HttpStatus.OK, undefined, correlationId);
  }

  /**
   * Create a deleted response
   */
  static deleted(
    message: string = 'Resource deleted successfully',
    correlationId?: string,
  ): Omit<ApiResponse, 'data'> {
    return this.successNoData(message, HttpStatus.OK, correlationId);
  }

  /**
   * Create an error response
   */
  static error(
    message: string,
    statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR,
    error: string = 'Internal Server Error',
    errors?: string[],
    correlationId?: string,
    path?: string,
    method?: string,
  ): ErrorResponse {
    return {
      success: false,
      statusCode,
      message,
      error,
      errors,
      correlationId,
      timestamp: new Date().toISOString(),
      path,
      method,
    };
  }

  /**
   * Create pagination metadata
   */
  static createPaginationMeta(
    page: number,
    limit: number,
    total: number,
  ): PaginationMeta {
    const totalPages = Math.ceil(total / limit);
    
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Create response metadata with execution time
   */
  static createMeta(
    startTime: number,
    pagination?: PaginationMeta,
    filters?: Record<string, any>,
    sort?: { field: string; order: 'ASC' | 'DESC' },
  ): ResponseMeta {
    const meta: ResponseMeta = {
      executionTime: Date.now() - startTime,
      version: process.env.API_VERSION || '1.0.0',
    };

    if (pagination) meta.pagination = pagination;
    if (filters) meta.filters = filters;
    if (sort) meta.sort = sort;

    return meta;
  }

  /**
   * Transform database results to paginated response
   */
  static transformPaginatedResult<T>(
    result: [T[], number],
    page: number,
    limit: number,
    message: string = 'Data retrieved successfully',
    correlationId?: string,
  ): ApiResponse<T[]> {
    const [data, total] = result;
    const pagination = this.createPaginationMeta(page, limit, total);
    
    return this.paginated(data, pagination, message, correlationId);
  }

  /**
   * Create a validation error response
   */
  static validationError(
    errors: string[],
    message: string = 'Validation failed',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.BAD_REQUEST,
      'Validation Error',
      errors,
      correlationId,
    );
  }

  /**
   * Create an unauthorized error response
   */
  static unauthorized(
    message: string = 'Unauthorized access',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.UNAUTHORIZED,
      'Unauthorized',
      undefined,
      correlationId,
    );
  }

  /**
   * Create a forbidden error response
   */
  static forbidden(
    message: string = 'Access forbidden',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.FORBIDDEN,
      'Forbidden',
      undefined,
      correlationId,
    );
  }

  /**
   * Create a not found error response
   */
  static notFound(
    message: string = 'Resource not found',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.NOT_FOUND,
      'Not Found',
      undefined,
      correlationId,
    );
  }

  /**
   * Create a conflict error response
   */
  static conflict(
    message: string = 'Resource already exists',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.CONFLICT,
      'Conflict',
      undefined,
      correlationId,
    );
  }

  /**
   * Create a rate limit error response
   */
  static rateLimitExceeded(
    message: string = 'Rate limit exceeded',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.TOO_MANY_REQUESTS,
      'Too Many Requests',
      undefined,
      correlationId,
    );
  }

  /**
   * Create a maintenance mode response
   */
  static maintenance(
    message: string = 'Service temporarily unavailable',
    correlationId?: string,
  ): ErrorResponse {
    return this.error(
      message,
      HttpStatus.SERVICE_UNAVAILABLE,
      'Service Unavailable',
      undefined,
      correlationId,
    );
  }

  /**
   * Check if response is successful
   */
  static isSuccess(response: ApiResponse | ErrorResponse): response is ApiResponse {
    return response.success === true;
  }

  /**
   * Extract data from response
   */
  static extractData<T>(response: ApiResponse<T>): T | undefined {
    return this.isSuccess(response) ? response.data : undefined;
  }

  /**
   * Extract pagination from response
   */
  static extractPagination(response: ApiResponse): PaginationMeta | undefined {
    return this.isSuccess(response) ? response.meta?.pagination : undefined;
  }

  /**
   * Create a health check response
   */
  static healthCheck(
    status: 'healthy' | 'unhealthy' | 'degraded',
    checks: Record<string, any> = {},
    correlationId?: string,
  ): ApiResponse<any> {
    const statusCode = status === 'healthy' ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
    
    return this.success(
      {
        status,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        checks,
      },
      `Service is ${status}`,
      statusCode,
      undefined,
      correlationId,
    );
  }
}
