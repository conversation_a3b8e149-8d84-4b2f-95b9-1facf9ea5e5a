
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/common/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/common/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.78% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>166/428</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.35% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>64/234</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.72% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>29/147</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40.09% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>164/409</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="date.util.ts"><a href="date.util.ts.html">date.util.ts</a></td>
	<td data-value="0.91" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0.91" class="pct low">0.91%</td>
	<td data-value="109" class="abs low">1/109</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="75" class="abs low">0/75</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0.91" class="pct low">0.91%</td>
	<td data-value="109" class="abs low">1/109</td>
	</tr>

<tr>
	<td class="file high" data-value="error-handler.util.ts"><a href="error-handler.util.ts.html">error-handler.util.ts</a></td>
	<td data-value="86.51" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.51" class="pct high">86.51%</td>
	<td data-value="89" class="abs high">77/89</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="44" class="abs medium">28/44</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="88" class="abs high">77/88</td>
	</tr>

<tr>
	<td class="file low" data-value="response.util.ts"><a href="response.util.ts.html">response.util.ts</a></td>
	<td data-value="5.88" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.88" class="pct low">5.88%</td>
	<td data-value="34" class="abs low">2/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="6.45" class="pct low">6.45%</td>
	<td data-value="31" class="abs low">2/31</td>
	</tr>

<tr>
	<td class="file low" data-value="string.util.ts"><a href="string.util.ts.html">string.util.ts</a></td>
	<td data-value="1.06" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 1%"></div><div class="cover-empty" style="width: 99%"></div></div>
	</td>
	<td data-value="1.06" class="pct low">1.06%</td>
	<td data-value="94" class="abs low">1/94</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="40" class="abs low">0/40</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="55" class="abs low">0/55</td>
	<td data-value="1.21" class="pct low">1.21%</td>
	<td data-value="82" class="abs low">1/82</td>
	</tr>

<tr>
	<td class="file medium" data-value="validation.util.ts"><a href="validation.util.ts.html">validation.util.ts</a></td>
	<td data-value="83.33" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.33" class="pct medium">83.33%</td>
	<td data-value="102" class="abs medium">85/102</td>
	<td data-value="80" class="pct medium">80%</td>
	<td data-value="45" class="abs medium">36/45</td>
	<td data-value="85" class="pct high">85%</td>
	<td data-value="20" class="abs high">17/20</td>
	<td data-value="83.83" class="pct medium">83.83%</td>
	<td data-value="99" class="abs medium">83/99</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T10:46:30.962Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    