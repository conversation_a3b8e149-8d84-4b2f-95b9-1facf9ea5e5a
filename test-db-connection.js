const { Client } = require('pg');
const redis = require('redis');

// Test PostgreSQL
async function testPostgreSQL() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'peoplenest_user',
    password: 'peoplenest_password',
    database: 'peoplenest_hrms',
  });

  try {
    await client.connect();
    const result = await client.query('SELECT version()');
    console.log('✅ PostgreSQL connected:', result.rows[0].version);
    await client.end();
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
  }
}

// Test Redis
async function testRedis() {
  const client = redis.createClient({
    host: 'localhost',
    port: 6379,
  });

  try {
    await client.connect();
    await client.ping();
    console.log('✅ Redis connected successfully');
    await client.disconnect();
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
  }
}

// Run tests
testPostgreSQL();
testRedis();