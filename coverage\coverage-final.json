{"C:\\PeopleNest\\src\\common\\decorators\\skip-transform.decorator.ts": {"path": "C:\\PeopleNest\\src\\common\\decorators\\skip-transform.decorator.ts", "statementMap": {"0": {"start": {"line": 3, "column": 34}, "end": {"line": 3, "column": 49}}, "1": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 72}}, "2": {"start": {"line": 9, "column": 35}, "end": {"line": 9, "column": 72}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 30}}, "loc": {"start": {"line": 9, "column": 35}, "end": {"line": 9, "column": 72}}, "line": 9}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "C:\\PeopleNest\\src\\common\\decorators\\tenant-required.decorator.ts": {"path": "C:\\PeopleNest\\src\\common\\decorators\\tenant-required.decorator.ts", "statementMap": {"0": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 51}}, "1": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": 74}}, "2": {"start": {"line": 9, "column": 36}, "end": {"line": 9, "column": 74}}, "3": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 75}}, "4": {"start": {"line": 15, "column": 36}, "end": {"line": 15, "column": 75}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": 31}}, "loc": {"start": {"line": 9, "column": 36}, "end": {"line": 9, "column": 74}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 31}}, "loc": {"start": {"line": 15, "column": 36}, "end": {"line": 15, "column": 75}}, "line": 15}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "C:\\PeopleNest\\src\\config\\database.config.ts": {"path": "C:\\PeopleNest\\src\\config\\database.config.ts", "statementMap": {"0": {"start": {"line": 3, "column": 30}, "end": {"line": 49, "column": 3}}, "1": {"start": {"line": 3, "column": 60}, "end": {"line": 49, "column": 1}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 53}, "end": {"line": 3, "column": 54}}, "loc": {"start": {"line": 3, "column": 60}, "end": {"line": 49, "column": 1}}, "line": 3}}, "branchMap": {"0": {"loc": {"start": {"line": 5, "column": 8}, "end": {"line": 5, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 8}, "end": {"line": 5, "column": 27}}, {"start": {"line": 5, "column": 31}, "end": {"line": 5, "column": 42}}], "line": 5}, "1": {"loc": {"start": {"line": 6, "column": 17}, "end": {"line": 6, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 17}, "end": {"line": 6, "column": 36}}, {"start": {"line": 6, "column": 40}, "end": {"line": 6, "column": 46}}], "line": 6}, "2": {"loc": {"start": {"line": 7, "column": 12}, "end": {"line": 7, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 12}, "end": {"line": 7, "column": 35}}, {"start": {"line": 7, "column": 39}, "end": {"line": 7, "column": 49}}], "line": 7}, "3": {"loc": {"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 8, "column": 12}, "end": {"line": 8, "column": 35}}, {"start": {"line": 8, "column": 39}, "end": {"line": 8, "column": 49}}], "line": 8}, "4": {"loc": {"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 27}}, {"start": {"line": 9, "column": 31}, "end": {"line": 9, "column": 48}}], "line": 9}, "5": {"loc": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 45}}, {"start": {"line": 12, "column": 49}, "end": {"line": 12, "column": 53}}], "line": 12}, "6": {"loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 57}}, {"start": {"line": 13, "column": 61}, "end": {"line": 13, "column": 66}}], "line": 13}, "7": {"loc": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 57}}, {"start": {"line": 14, "column": 61}, "end": {"line": 14, "column": 68}}], "line": 14}, "8": {"loc": {"start": {"line": 15, "column": 20}, "end": {"line": 15, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 20}, "end": {"line": 15, "column": 42}}, {"start": {"line": 15, "column": 46}, "end": {"line": 15, "column": 53}}], "line": 15}, "9": {"loc": {"start": {"line": 18, "column": 7}, "end": {"line": 23, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 18, "column": 47}, "end": {"line": 23, "column": 3}}, {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 11}}], "line": 18}, "10": {"loc": {"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 37}}, {"start": {"line": 34, "column": 41}, "end": {"line": 34, "column": 49}}], "line": 34}, "11": {"loc": {"start": {"line": 35, "column": 17}, "end": {"line": 35, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 17}, "end": {"line": 35, "column": 43}}, {"start": {"line": 35, "column": 47}, "end": {"line": 35, "column": 56}}], "line": 35}, "12": {"loc": {"start": {"line": 39, "column": 23}, "end": {"line": 39, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 23}, "end": {"line": 39, "column": 52}}, {"start": {"line": 39, "column": 56}, "end": {"line": 39, "column": 63}}], "line": 39}, "13": {"loc": {"start": {"line": 46, "column": 14}, "end": {"line": 46, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 14}, "end": {"line": 46, "column": 44}}, {"start": {"line": 46, "column": 48}, "end": {"line": 46, "column": 59}}], "line": 46}, "14": {"loc": {"start": {"line": 47, "column": 24}, "end": {"line": 47, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 24}, "end": {"line": 47, "column": 55}}, {"start": {"line": 47, "column": 59}, "end": {"line": 47, "column": 63}}], "line": 47}}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0]}}, "C:\\PeopleNest\\src\\config\\jwt.config.ts": {"path": "C:\\PeopleNest\\src\\config\\jwt.config.ts", "statementMap": {"0": {"start": {"line": 3, "column": 25}, "end": {"line": 88, "column": 3}}, "1": {"start": {"line": 3, "column": 50}, "end": {"line": 88, "column": 1}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 43}, "end": {"line": 3, "column": 44}}, "loc": {"start": {"line": 3, "column": 50}, "end": {"line": 88, "column": 1}}, "line": 3}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 96}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 41}}, {"start": {"line": 6, "column": 45}, "end": {"line": 6, "column": 96}}], "line": 6}, "1": {"loc": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 48}}, {"start": {"line": 7, "column": 52}, "end": {"line": 7, "column": 57}}], "line": 7}, "2": {"loc": {"start": {"line": 9, "column": 12}, "end": {"line": 9, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 12}, "end": {"line": 9, "column": 34}}, {"start": {"line": 9, "column": 38}, "end": {"line": 9, "column": 55}}], "line": 9}, "3": {"loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 38}}, {"start": {"line": 10, "column": 42}, "end": {"line": 10, "column": 60}}], "line": 10}, "4": {"loc": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 98}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 42}}, {"start": {"line": 15, "column": 46}, "end": {"line": 15, "column": 98}}], "line": 15}, "5": {"loc": {"start": {"line": 16, "column": 15}, "end": {"line": 16, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 15}, "end": {"line": 16, "column": 49}}, {"start": {"line": 16, "column": 53}, "end": {"line": 16, "column": 57}}], "line": 16}, "6": {"loc": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 34}}, {"start": {"line": 18, "column": 38}, "end": {"line": 18, "column": 55}}], "line": 18}, "7": {"loc": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 38}}, {"start": {"line": 19, "column": 42}, "end": {"line": 19, "column": 60}}], "line": 19}, "8": {"loc": {"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 91}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 12}, "end": {"line": 24, "column": 49}}, {"start": {"line": 24, "column": 53}, "end": {"line": 24, "column": 91}}], "line": 24}, "9": {"loc": {"start": {"line": 25, "column": 15}, "end": {"line": 25, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 15}, "end": {"line": 25, "column": 56}}, {"start": {"line": 25, "column": 60}, "end": {"line": 25, "column": 64}}], "line": 25}, "10": {"loc": {"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 99}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 53}}, {"start": {"line": 31, "column": 57}, "end": {"line": 31, "column": 99}}], "line": 31}, "11": {"loc": {"start": {"line": 32, "column": 15}, "end": {"line": 32, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 15}, "end": {"line": 32, "column": 60}}, {"start": {"line": 32, "column": 64}, "end": {"line": 32, "column": 69}}], "line": 32}, "12": {"loc": {"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 48}}, {"start": {"line": 41, "column": 52}, "end": {"line": 41, "column": 75}}], "line": 41}, "13": {"loc": {"start": {"line": 42, "column": 13}, "end": {"line": 42, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 13}, "end": {"line": 42, "column": 39}}, {"start": {"line": 42, "column": 43}, "end": {"line": 42, "column": 55}}], "line": 42}, "14": {"loc": {"start": {"line": 43, "column": 16}, "end": {"line": 43, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 43, "column": 16}, "end": {"line": 43, "column": 46}}, {"start": {"line": 43, "column": 50}, "end": {"line": 43, "column": 69}}], "line": 43}, "15": {"loc": {"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": 54}}, {"start": {"line": 44, "column": 58}, "end": {"line": 44, "column": 60}}], "line": 44}, "16": {"loc": {"start": {"line": 45, "column": 19}, "end": {"line": 45, "column": 109}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 19}, "end": {"line": 45, "column": 52}}, {"start": {"line": 45, "column": 56}, "end": {"line": 45, "column": 109}}], "line": 45}, "17": {"loc": {"start": {"line": 51, "column": 24}, "end": {"line": 51, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 51, "column": 24}, "end": {"line": 51, "column": 60}}, {"start": {"line": 51, "column": 64}, "end": {"line": 51, "column": 66}}], "line": 51}, "18": {"loc": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 44}}, {"start": {"line": 52, "column": 48}, "end": {"line": 52, "column": 50}}], "line": 52}, "19": {"loc": {"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 44}}, {"start": {"line": 53, "column": 48}, "end": {"line": 53, "column": 50}}], "line": 53}, "20": {"loc": {"start": {"line": 54, "column": 20}, "end": {"line": 54, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 54, "column": 20}, "end": {"line": 54, "column": 52}}, {"start": {"line": 54, "column": 56}, "end": {"line": 54, "column": 58}}], "line": 54}, "21": {"loc": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 50}}, {"start": {"line": 55, "column": 54}, "end": {"line": 55, "column": 56}}], "line": 55}, "22": {"loc": {"start": {"line": 56, "column": 13}, "end": {"line": 56, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 56, "column": 13}, "end": {"line": 56, "column": 49}}, {"start": {"line": 56, "column": 53}, "end": {"line": 56, "column": 83}}], "line": 56}, "23": {"loc": {"start": {"line": 69, "column": 27}, "end": {"line": 69, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 27}, "end": {"line": 69, "column": 61}}, {"start": {"line": 69, "column": 65}, "end": {"line": 69, "column": 73}}], "line": 69}, "24": {"loc": {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 53}}, {"start": {"line": 70, "column": 57}, "end": {"line": 70, "column": 60}}], "line": 70}, "25": {"loc": {"start": {"line": 73, "column": 27}, "end": {"line": 73, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 73, "column": 27}, "end": {"line": 73, "column": 65}}, {"start": {"line": 73, "column": 69}, "end": {"line": 73, "column": 78}}], "line": 73}, "26": {"loc": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 57}}, {"start": {"line": 74, "column": 61}, "end": {"line": 74, "column": 64}}], "line": 74}, "27": {"loc": {"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 57}}, {"start": {"line": 80, "column": 61}, "end": {"line": 80, "column": 64}}], "line": 80}, "28": {"loc": {"start": {"line": 85, "column": 23}, "end": {"line": 85, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 23}, "end": {"line": 85, "column": 51}}, {"start": {"line": 85, "column": 55}, "end": {"line": 85, "column": 64}}], "line": 85}}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0]}}, "C:\\PeopleNest\\src\\config\\redis.config.ts": {"path": "C:\\PeopleNest\\src\\config\\redis.config.ts", "statementMap": {"0": {"start": {"line": 3, "column": 27}, "end": {"line": 59, "column": 3}}, "1": {"start": {"line": 3, "column": 54}, "end": {"line": 59, "column": 1}}, "2": {"start": {"line": 54, "column": 27}, "end": {"line": 54, "column": 46}}, "3": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 48}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 47}, "end": {"line": 3, "column": 48}}, "loc": {"start": {"line": 3, "column": 54}, "end": {"line": 59, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 53, "column": 59}, "end": {"line": 53, "column": 60}}, "loc": {"start": {"line": 53, "column": 71}, "end": {"line": 56, "column": 5}}, "line": 53}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 8}, "end": {"line": 4, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 8}, "end": {"line": 4, "column": 30}}, {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 45}}], "line": 4}, "1": {"loc": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 39}}, {"start": {"line": 5, "column": 43}, "end": {"line": 5, "column": 49}}], "line": 5}, "2": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 38}}, {"start": {"line": 6, "column": 42}, "end": {"line": 6, "column": 51}}], "line": 6}, "3": {"loc": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 35}}, {"start": {"line": 7, "column": 39}, "end": {"line": 7, "column": 42}}], "line": 7}, "4": {"loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 60}}, {"start": {"line": 10, "column": 64}, "end": {"line": 10, "column": 71}}], "line": 10}, "5": {"loc": {"start": {"line": 12, "column": 33}, "end": {"line": 12, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 33}, "end": {"line": 12, "column": 62}}, {"start": {"line": 12, "column": 66}, "end": {"line": 12, "column": 69}}], "line": 12}, "6": {"loc": {"start": {"line": 13, "column": 33}, "end": {"line": 13, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 33}, "end": {"line": 13, "column": 62}}, {"start": {"line": 13, "column": 66}, "end": {"line": 13, "column": 71}}], "line": 13}, "7": {"loc": {"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": 37}}, {"start": {"line": 20, "column": 41}, "end": {"line": 20, "column": 46}}], "line": 20}, "8": {"loc": {"start": {"line": 21, "column": 16}, "end": {"line": 21, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 16}, "end": {"line": 21, "column": 43}}, {"start": {"line": 21, "column": 47}, "end": {"line": 21, "column": 53}}], "line": 21}, "9": {"loc": {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 47}}, {"start": {"line": 25, "column": 51}, "end": {"line": 25, "column": 58}}], "line": 25}, "10": {"loc": {"start": {"line": 26, "column": 12}, "end": {"line": 26, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 12}, "end": {"line": 26, "column": 44}}, {"start": {"line": 26, "column": 48}, "end": {"line": 26, "column": 55}}], "line": 26}, "11": {"loc": {"start": {"line": 42, "column": 11}, "end": {"line": 42, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 11}, "end": {"line": 42, "column": 54}}, {"start": {"line": 42, "column": 58}, "end": {"line": 42, "column": 60}}], "line": 42}, "12": {"loc": {"start": {"line": 53, "column": 15}, "end": {"line": 56, "column": 12}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 15}, "end": {"line": 56, "column": 6}}, {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 12}}], "line": 53}, "13": {"loc": {"start": {"line": 57, "column": 10}, "end": {"line": 57, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 10}, "end": {"line": 57, "column": 41}}, {"start": {"line": 57, "column": 45}, "end": {"line": 57, "column": 55}}], "line": 57}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}}