import { Injectable, Logger, Inject } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger as WinstonLogger } from 'winston';

// Services
import { CorrelationService } from '@common/services/correlation.service';
import { DatabaseService } from './database.service';

export interface MigrationInfo {
  id: number;
  timestamp: number;
  name: string;
  executed: boolean;
  executedAt?: Date;
}

export interface MigrationResult {
  success: boolean;
  migrationsRun: number;
  error?: string;
  migrations: MigrationInfo[];
}

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
    @Inject(WINSTON_MODULE_PROVIDER)
    private readonly winstonLogger: WinstonLogger,
    private readonly correlationService: CorrelationService,
    private readonly databaseService: DatabaseService,
  ) {}

  /**
   * Run pending migrations
   */
  async runMigrations(): Promise<MigrationResult> {
    const correlationId = this.correlationService.getCorrelationId();

    this.winstonLogger.info('Starting database migrations', {
      context: MigrationService.name,
      correlationId,
    });

    try {
      // Get pending migrations
      const pendingMigrations = await this.dataSource.showMigrations();

      if (!pendingMigrations || (Array.isArray(pendingMigrations) && pendingMigrations.length === 0)) {
        this.winstonLogger.info('No pending migrations found', {
          context: MigrationService.name,
          correlationId,
        });

        return {
          success: true,
          migrationsRun: 0,
          migrations: await this.getMigrationHistory(),
        };
      }

      // Run migrations
      const migrations = await this.dataSource.runMigrations({
        transaction: 'each', // Run each migration in its own transaction
      });

      const migrationsCount = Array.isArray(migrations) ? migrations.length : 0;
      const migrationNames = Array.isArray(migrations) ? migrations.map(m => m.name) : [];

      this.winstonLogger.info('Database migrations completed successfully', {
        context: MigrationService.name,
        correlationId,
        migrationsRun: migrationsCount,
        migrations: migrationNames,
      });

      return {
        success: true,
        migrationsRun: migrationsCount,
        migrations: await this.getMigrationHistory(),
      };
    } catch (error) {
      this.winstonLogger.error('Database migrations failed', {
        context: MigrationService.name,
        correlationId,
        error: error.message,
      });

      return {
        success: false,
        migrationsRun: 0,
        error: error.message,
        migrations: await this.getMigrationHistory(),
      };
    }
  }

  /**
   * Revert the last migration
   */
  async revertLastMigration(): Promise<MigrationResult> {
    const correlationId = this.correlationService.getCorrelationId();

    this.winstonLogger.info('Reverting last migration', {
      context: MigrationService.name,
      correlationId,
    });

    try {
      await this.dataSource.undoLastMigration({
        transaction: 'each',
      });

      this.winstonLogger.info('Last migration reverted successfully', {
        context: MigrationService.name,
        correlationId,
      });

      return {
        success: true,
        migrationsRun: -1, // Negative indicates revert
        migrations: await this.getMigrationHistory(),
      };
    } catch (error) {
      this.winstonLogger.error('Failed to revert last migration', {
        context: MigrationService.name,
        correlationId,
        error: error.message,
      });

      return {
        success: false,
        migrationsRun: 0,
        error: error.message,
        migrations: await this.getMigrationHistory(),
      };
    }
  }

  /**
   * Get migration history
   */
  async getMigrationHistory(): Promise<MigrationInfo[]> {
    try {
      const executedMigrations = await this.dataSource.query(
        'SELECT * FROM migrations ORDER BY timestamp ASC',
      );

      return executedMigrations.map(migration => ({
        id: migration.id,
        timestamp: migration.timestamp,
        name: migration.name,
        executed: true,
        executedAt: migration.timestamp ? new Date(migration.timestamp) : undefined,
      }));
    } catch (error) {
      this.logger.warn('Failed to get migration history', error.message);
      return [];
    }
  }

  /**
   * Check if migrations are pending
   */
  async hasPendingMigrations(): Promise<boolean> {
    try {
      const pendingMigrations = await this.dataSource.showMigrations();
      return Array.isArray(pendingMigrations) && pendingMigrations.length > 0;
    } catch (error) {
      this.logger.error('Failed to check pending migrations', error.message);
      return false;
    }
  }

  /**
   * Generate a new migration
   */
  async generateMigration(name: string): Promise<string> {
    const correlationId = this.correlationService.getCorrelationId();

    try {
      this.winstonLogger.info('Generating new migration', {
        context: MigrationService.name,
        correlationId,
        name,
      });

      // This would typically be done via CLI, but we can provide the structure
      const timestamp = Date.now();
      const className = name.replace(/[^a-zA-Z0-9]/g, '');
      
      const migrationContent = `import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${className}${timestamp} implements MigrationInterface {
  name = '${className}${timestamp}';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add your migration logic here
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add your rollback logic here
  }
}
`;

      const fileName = `${timestamp}-${name}.ts`;
      
      this.winstonLogger.info('Migration template generated', {
        context: MigrationService.name,
        correlationId,
        fileName,
      });

      return migrationContent;
    } catch (error) {
      this.winstonLogger.error('Failed to generate migration', {
        context: MigrationService.name,
        correlationId,
        name,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Initialize database schema (for fresh installations)
   */
  async initializeSchema(): Promise<void> {
    const correlationId = this.correlationService.getCorrelationId();

    this.winstonLogger.info('Initializing database schema', {
      context: MigrationService.name,
      correlationId,
    });

    try {
      // Create extensions if they don't exist
      await this.createExtensions();

      // Synchronize schema (only in development)
      if (this.configService.get<string>('NODE_ENV') === 'development') {
        await this.dataSource.synchronize();
        this.logger.log('Database schema synchronized');
      }

      // Run migrations
      const result = await this.runMigrations();
      
      if (!result.success) {
        throw new Error(`Migration failed: ${result.error}`);
      }

      this.winstonLogger.info('Database schema initialized successfully', {
        context: MigrationService.name,
        correlationId,
        migrationsRun: result.migrationsRun,
      });
    } catch (error) {
      this.winstonLogger.error('Failed to initialize database schema', {
        context: MigrationService.name,
        correlationId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Create required PostgreSQL extensions
   */
  private async createExtensions(): Promise<void> {
    const extensions = [
      'uuid-ossp',    // For UUID generation
      'pgcrypto',     // For cryptographic functions
      'pg_trgm',      // For text similarity and indexing
      'btree_gin',    // For GIN indexes on btree-indexable types
    ];

    for (const extension of extensions) {
      try {
        await this.dataSource.query(`CREATE EXTENSION IF NOT EXISTS "${extension}"`);
        this.logger.debug(`Extension ${extension} created/verified`);
      } catch (error) {
        this.logger.warn(`Failed to create extension ${extension}:`, error.message);
      }
    }
  }

  /**
   * Backup database schema
   */
  async backupSchema(): Promise<string> {
    const correlationId = this.correlationService.getCorrelationId();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `schema_backup_${timestamp}`;

    this.winstonLogger.info('Creating schema backup', {
      context: MigrationService.name,
      correlationId,
      backupName,
    });

    try {
      // This is a simplified version - in production you'd use pg_dump
      const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      `);

      const backupData = {
        timestamp: new Date(),
        tables: tables.map(t => t.table_name),
        migrations: await this.getMigrationHistory(),
      };

      this.winstonLogger.info('Schema backup created', {
        context: MigrationService.name,
        correlationId,
        backupName,
        tablesCount: tables.length,
      });

      return JSON.stringify(backupData, null, 2);
    } catch (error) {
      this.winstonLogger.error('Failed to create schema backup', {
        context: MigrationService.name,
        correlationId,
        backupName,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get database schema information
   */
  async getSchemaInfo(): Promise<any> {
    try {
      const tables = await this.dataSource.query(`
        SELECT 
          table_name,
          table_type,
          table_schema
        FROM information_schema.tables 
        WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
        ORDER BY table_schema, table_name
      `);

      const indexes = await this.dataSource.query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
        ORDER BY schemaname, tablename, indexname
      `);

      return {
        tables: tables.length,
        indexes: indexes.length,
        tableDetails: tables,
        indexDetails: indexes,
        migrations: await this.getMigrationHistory(),
      };
    } catch (error) {
      this.logger.error('Failed to get schema info', error.message);
      return null;
    }
  }
}
