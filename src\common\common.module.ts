import { Module, Global } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';

// Filters
import { GlobalExceptionFilter } from './filters/global-exception.filter';

// Interceptors
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { TenantInterceptor } from './interceptors/tenant.interceptor';
import { TransformInterceptor } from './interceptors/transform.interceptor';

// Services
import { CorrelationService } from './services/correlation.service';
import { EnhancedLoggerService } from './services/enhanced-logger.service';

// Utilities (exported for use in other modules)
import {
  ValidationUtil,
  ErrorHandler,
  ResponseUtil,
  DateUtil,
  StringUtil,
} from './utils';

@Global()
@Module({
  providers: [
    // Global exception filter
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },

    // Global interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TenantInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },

    // Services
    CorrelationService,
    EnhancedLoggerService,

    // Utility classes as providers (for dependency injection if needed)
    ValidationUtil,
    ErrorHandler,
    ResponseUtil,
    DateUtil,
    StringUtil,
  ],
  exports: [
    // Export services for use in other modules
    CorrelationService,
    EnhancedLoggerService,

    // Export utility classes
    ValidationUtil,
    ErrorHandler,
    ResponseUtil,
    DateUtil,
    StringUtil,
  ],
})
export class CommonModule {}
