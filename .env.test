# Test Environment Configuration
NODE_ENV=test

# Database Configuration
DB_TYPE=postgres
TEST_DB_HOST=localhost
TEST_DB_PORT=5433
TEST_DB_USERNAME=test_user
TEST_DB_PASSWORD=test_password
TEST_DB_NAME=peoplenest_test
DB_SYNCHRONIZE=true
DB_DROP_SCHEMA=true
DB_LOGGING=false

# Redis Configuration (Test)
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=
REDIS_DB=1

# JWT Configuration (Test)
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=test_refresh_secret_key_for_testing_only
JWT_REFRESH_EXPIRES_IN=7d

# OAuth Configuration (Test)
OAUTH_CLIENT_ID=test_client_id
OAUTH_CLIENT_SECRET=test_client_secret
OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback

# Email Configuration (Test - Mock)
EMAIL_SERVICE=test
EMAIL_HOST=localhost
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test_password
EMAIL_FROM=<EMAIL>

# File Storage Configuration (Test)
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=./test-uploads
STORAGE_MAX_FILE_SIZE=10485760

# Logging Configuration (Test)
LOG_LEVEL=error
LOG_FILE_ENABLED=false
LOG_CONSOLE_ENABLED=false

# Rate Limiting (Test)
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=1000

# CORS Configuration (Test)
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Security Configuration (Test)
BCRYPT_ROUNDS=4
SESSION_SECRET=test_session_secret

# API Configuration (Test)
API_PREFIX=api/v1
API_VERSION=1.0.0
API_TITLE=PeopleNest HRMS API (Test)
API_DESCRIPTION=AI-enabled Enterprise HRMS Platform API - Test Environment

# Monitoring Configuration (Test)
METRICS_ENABLED=false
HEALTH_CHECK_ENABLED=true

# Cache Configuration (Test)
CACHE_TTL=300
CACHE_MAX_ITEMS=100

# Audit Configuration (Test)
AUDIT_ENABLED=false
AUDIT_LOG_REQUESTS=false
AUDIT_LOG_RESPONSES=false

# Multi-tenancy Configuration (Test)
TENANT_ISOLATION=schema
DEFAULT_TENANT=test-tenant

# AI/ML Configuration (Test)
AI_SERVICE_ENABLED=false
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_API_KEY=test_ai_api_key

# External Services (Test - All Mocked)
EXTERNAL_HR_API_URL=http://localhost:9000
EXTERNAL_HR_API_KEY=test_external_api_key

# Performance Configuration (Test)
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30000
DATABASE_POOL_SIZE=5

# Feature Flags (Test)
FEATURE_ADVANCED_ANALYTICS=false
FEATURE_AI_RECOMMENDATIONS=false
FEATURE_REAL_TIME_NOTIFICATIONS=false
FEATURE_DOCUMENT_MANAGEMENT=true
FEATURE_PERFORMANCE_REVIEWS=true

# Test-specific Configuration
TEST_TIMEOUT=30000
TEST_PARALLEL_WORKERS=1
TEST_COVERAGE_THRESHOLD=85
TEST_MOCK_EXTERNAL_SERVICES=true
TEST_SEED_DATA=true
TEST_CLEANUP_AFTER_TESTS=true
