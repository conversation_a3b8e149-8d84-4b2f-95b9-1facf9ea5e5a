"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var l=["ala","a\xE7\u0131q b\u0259n\xF6v\u015F\u0259yi","a\u011F","boz","b\u0259n\xF6v\u015F\u0259yi","g\xF6y r\u0259ng","g\xFCm\xFC\u015F\xFC","kardinal","mavi","nar\u0131nc\u0131","qara","q\u0131rm\u0131z\u0131","q\u0259hv\u0259yi","t\xFCnd g\xF6y","t\xFCnd q\u0131rm\u0131z\u0131","xlorofil","ya\u015F\u0131l","\xE7\u0259hray\u0131"];var V={human:l},n=V;var o=["Avtomobil","Ayyaqqab\u0131","Elektronika","Ev","Filml\u0259r","Geyim","Kitablar","Kompyuterl\u0259r","Oyuncaqlar","S\u0259hiyy\u0259","b\u0259z\u0259k","g\xF6z\u0259llik","musiqi","oyunlar","turizm","u\u015Faq \xFC\xE7\xFCn","\u0130dman","\u0441\u0430\u0434\u0438\u043D\u0441\u0442\u0440\u0443\u043C\u0435\u043D\u0442"];var e={adjective:["Balaca","Ergonomik","Fantastik","Kobud","M\xF6ht\u0259\u015F\u0259m","M\xFCk\u0259mm\u0259l","Parlaq","\u0130nan\u0131lmaz","\u0130ntellektual","\u018Flveri\u015Fli"],material:["A\u011Fac","Beton","Pamb\u0131q","Plastik","Polad","Qranit","Rezin"],product:["Avtomobil","Beret","Kompyuter","Kulon","K\u0259m\u0259r","Stol","Stul","Sviter"]};var X={department:o,product_name:e},t=X;var m=["ASC","MMC","QSC"];var y=["{{company.legal_entity_type}} {{person.first_name.female}}","{{company.legal_entity_type}} {{person.first_name.male}}","{{company.legal_entity_type}} {{person.last_name.male}}"];var Z={legal_entity_type:m,name_pattern:y},d=Z;var v={wide:["aprel","avqust","dekabr","fevral","iyul","iyun","mart","may","noyabr","oktyabr","sentyabr","yanvar"],wide_context:["\u0430\u0432\u0433\u0443\u0441\u0442\u0430","\u0430\u043F\u0440\u0435\u043B\u044F","\u0434\u0435\u043A\u0430\u0431\u0440\u044F","\u0438\u044E\u043B\u044F","\u0438\u044E\u043D\u044F","\u043C\u0430\u0440\u0442\u0430","\u043C\u0430\u044F","\u043D\u043E\u044F\u0431\u0440\u044F","\u043E\u043A\u0442\u044F\u0431\u0440\u044F","\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F","\u0444\u0435\u0432\u0440\u0430\u043B\u044F","\u044F\u043D\u0432\u0430\u0440\u044F"],abbr:["\u0430\u0432\u0433.","\u0430\u043F\u0440.","\u0434\u0435\u043A.","\u0438\u044E\u043B\u044C","\u0438\u044E\u043D\u044C","\u043C\u0430\u0439","\u043C\u0430\u0440\u0442","\u043D\u043E\u044F\u0431.","\u043E\u043A\u0442.","\u0441\u0435\u043D\u0442.","\u0444\u0435\u0432\u0440.","\u044F\u043D\u0432."],abbr_context:["\u0430\u0432\u0433.","\u0430\u043F\u0440.","\u0434\u0435\u043A.","\u0438\u044E\u043B\u044F","\u0438\u044E\u043D\u044F","\u043C\u0430\u0440\u0442\u0430","\u043C\u0430\u044F","\u043D\u043E\u044F\u0431.","\u043E\u043A\u0442.","\u0441\u0435\u043D\u0442.","\u0444\u0435\u0432\u0440.","\u044F\u043D\u0432."]};var u={wide:["Bazar","Bazar ert\u0259si","C\xFCm\u0259","C\xFCm\u0259 ax\u015Fam\u0131","\xC7\u0259r\u015F\u0259nb\u0259","\xC7\u0259r\u015F\u0259nb\u0259 ax\u015Fam\u0131","\u015E\u0259nb\u0259"],wide_context:["\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A","\u043F\u044F\u0442\u043D\u0438\u0446\u0430","\u0441\u0440\u0435\u0434\u0430","\u0441\u0443\u0431\u0431\u043E\u0442\u0430","\u0447\u0435\u0442\u0432\u0435\u0440\u0433"],abbr:["BE","Ba","CA","C\xFC","\xC7A","\xC7\u0259","\u015E\u0259"],abbr_context:["\u0432\u0441","\u0432\u0442","\u043F\u043D","\u043F\u0442","\u0441\u0431","\u0441\u0440","\u0447\u0442"]};var w={month:v,weekday:u},s=w;var f=["az","com","com.az","info","net","org"];var b=["box.az","gmail.com","hotmail.com","mail.az","yahoo.com"];var O={domain_suffix:f,free_email:b},p=O;var z=["###"];var A=["A\u011Fcab\u0259di","A\u011Fdam","A\u011Fda\u015F","A\u011Fd\u0259r\u0259","A\u011Fstafa","A\u011Fsu","Astara","Bak\u0131","Balak\u0259n","Beyl\u0259qan","B\u0259rd\u0259","Bil\u0259suvar","C\u0259bray\u0131l","C\u0259lilabad","Culfa","Da\u015Fk\u0259s\u0259n","D\u0259lim\u0259mm\u0259dli","F\xFCzuli","G\u0259d\u0259b\u0259y","G\u0259nc\u0259","Goranboy","G\xF6y\xE7ay","G\xF6yg\xF6l","G\xF6yt\u0259p\u0259","Hac\u0131qabul","Horadiz","Xa\xE7maz","Xank\u0259ndi","Xocal\u0131","Xocav\u0259nd","X\u0131rdalan","X\u0131z\u0131","Xudat","\u0130mi\u015Fli","\u0130smay\u0131ll\u0131","K\u0259lb\u0259c\u0259r","K\xFCrd\u0259mir","Qax","Qazax","Q\u0259b\u0259l\u0259","Qobustan","Qovlar","Quba","Qubadl\u0131","Qusar","La\xE7\u0131n","Lerik","L\u0259nk\u0259ran","Liman","Masall\u0131","Ming\u0259\xE7evir","Naftalan","Nax\xE7\u0131van (\u015F\u0259h\u0259r)","Neft\xE7ala","O\u011Fuz","Ordubad","Saatl\u0131","Sabirabad","Salyan","Samux","Siy\u0259z\u0259n","Sumqay\u0131t","\u015Eabran","\u015Eahbuz","\u015Eamax\u0131","\u015E\u0259ki","\u015E\u0259mkir","\u015E\u0259rur","\u015Eirvan","\u015Eu\u015Fa","T\u0259rt\u0259r","Tovuz","Ucar","Yard\u0131ml\u0131","Yevlax","Zaqatala","Z\u0259ngilan","Z\u0259rdab"];var k=["{{location.city_name}}"];var h=["Akrotiri v\u0259 Dekeliya","Aland adalar\u0131","Albaniya","Almaniya","Amerika Samoas\u0131","Andorra","Angilya","Anqola","Antiqua v\u0259 Barbuda","Argentina","Aruba","Avstraliya","Avstriya","Az\u0259rbaycan","Baham adalar\u0131","Banqlade\u015F","Barbados","Bel\xE7ika","Beliz","Belarus","Benin","Bermud adalar\u0131","B\u018F\u018F","AB\u015E","Boliviya","Bolqar\u0131stan","Bosniya v\u0259 Herseqovina","Botsvana","B\xF6y\xFCk Britaniya","Braziliya","Bruney","Burkina-Faso","Burundi","Butan","B\u0259hreyn","Cersi","C\u0259b\u0259li-Tariq","CAR","C\u0259nubi Sudan","C\u0259nubi Koreya","Cibuti","\xC7ad","\xC7exiya","Monteneqro","\xC7ili","\xC7XR","Danimarka","Dominika","Dominikan Respublikas\u0131","Efiopiya","Ekvador","Ekvatorial Qvineya","Eritreya","Erm\u0259nistan","Estoniya","\u018Ffqan\u0131stan","\u018Flc\u0259zair","Farer adalar\u0131","F\u0259l\u0259stin D\xF6vl\u0259ti","Fici","Kot-d\u2019\u0130vuar","Filippin","Finlandiya","Folklend adalar\u0131","Fransa","Fransa Polineziyas\u0131","Gernsi","G\xFCrc\xFCstan","Haiti","Hindistan","Honduras","Honkonq","Xorvatiya","\u0130ndoneziya","\u0130ordaniya","\u0130raq","\u0130ran","\u0130rlandiya","\u0130slandiya","\u0130spaniya","\u0130srail","\u0130sve\xE7","\u0130sve\xE7r\u0259","\u0130taliya","Kabo-Verde","Kamboca","Kamerun","Kanada","Kayman adalar\u0131","Keniya","Kipr","Kiribati","Kokos adalar\u0131","Kolumbiya","Komor adalar\u0131","Konqo Respublikas\u0131","KDR","Kosovo","Kosta-Rika","Kuba","Kuk adalar\u0131","K\xFCveyt","Qabon","Qambiya","Qana","Q\u0259t\u0259r","Qayana","Qazax\u0131stan","Q\u0259rbi Sahara","Q\u0131r\u011F\u0131z\u0131stan","Qrenada","Qrenlandiya","Quam","Qvatemala","Qvineya","Qvineya-Bisau","Laos","Latviya","Lesoto","Liberiya","Litva","Livan","Liviya","Lixten\u015Fteyn","L\xFCksemburq","Macar\u0131stan","Madaqaskar","Makao","Makedoniya","Malavi","Malayziya","Maldiv adalar\u0131","Mali","Malta","Mar\u015Fall adalar\u0131","Mavriki","Mavritaniya","Mayotta","Meksika","Men adas\u0131","M\u0259rake\u015F","MAR","Mikroneziya","Milad adas\u0131","Misir","Myanma","Moldova","Monako","Monqolustan","Montserrat","Mozambik","M\xFCq\u0259dd\u0259s Yelena, Askenson v\u0259 Tristan-da-Kunya adalar\u0131","Namibiya","Nauru","Nepal","Niderland","Niderland Antil adalar\u0131","Niger","Nigeriya","Nikaraqua","Niue","Norfolk adas\u0131","Norve\xE7","Oman","\xD6zb\u0259kistan","Pakistan","Palau","Panama","Papua-Yeni Qvineya","Paraqvay","Peru","Pitkern adalar\u0131","Pol\u015Fa","Portuqaliya","Prednestroviya","Puerto-Riko","Ruanda","Rum\u0131niya","Rusiya","Salvador","Samoa","San-Marino","San-Tome v\u0259 Prinsipi","Seneqal","Sen-Bartelemi","Sent-Kits v\u0259 Nevis","Sent-L\xFCsiya","Sen-Marten","Sen-Pyer v\u0259 Mikelon","Sent-Vinsent v\u0259 Qrenadina","Serbiya","Sey\u015Fel adalar\u0131","S\u0259udiyy\u0259 \u018Fr\u0259bistan\u0131","Sinqapur","Slovakiya","Sloveniya","Solomon adalar\u0131","Somali","Somalilend","Sudan","Surinam","Suriya","Svazilend","Syerra-Leone","\u015E\u0259rqi Timor","\u015Eimali Marian adalar\u0131","\u015Episbergen v\u0259 Yan-Mayen","\u015Eri-Lanka","Tacikistan","Tanzaniya","Tailand","\xC7in Respublikas\u0131","T\xF6rks v\u0259 Kaykos adalar\u0131","Tokelau","Tonqa","Toqo","Trinidad v\u0259 Tobaqo","Tunis","Tuvalu","T\xFCrkiy\u0259","T\xFCrkm\u0259nistan","Ukrayna","Uollis v\u0259 Futuna","Uqanda","Uruqvay","Vanuatu","Vatikan","Venesuela","Amerika Virgin adalar\u0131","Britaniya Virgin adalar\u0131","Vyetnam","Yamayka","Yaponiya","Yeni Kaledoniya","Yeni Zelandiya","Y\u0259m\u0259n","Yunan\u0131stan","Zambiya","Zimbabve"];var M=["AZ####"];var c=["m. ###"];var x=null;var q={normal:"{{location.street}}, {{location.buildingNumber}}",full:"{{location.street}}, {{location.buildingNumber}} {{location.secondaryAddress}}"};var S=["Abbas F\u0259tullayev","Abbas Mirz\u0259 \u015E\u0259rifzad\u0259","Abbas S\u0259hh\u0259t","Abdulla \u015Eaiq","Afiy\u0259ddin C\u0259lilov","Axundov","A\u011Fa Nem\u0259tulla","A\u011Fadada\u015F Qurbanov","Akademik H\u0259s\u0259n \u018Fliyev","Akademik L\u0259tif \u0130manov","Al\u0131 Mustafayev","Almas \u0130ld\u0131r\u0131m","As\u0259f Zeynall\u0131","Asif \u018Fs\u0259dullayev","A\u015F\u0131q Al\u0131","A\u015F\u0131q \u018Fl\u0259sg\u0259r","Azadl\u0131q prospekti","Bak\u0131xanov","Balababa M\u0259cidov","Bala\u0259mi Dada\u015Fov","Behbud \u015Eaxtantinski","B\u0259kir \xC7obanzad\u0259","B\u0259sti Ba\u011F\u0131rova","B\u0259\u015Fir S\u0259f\u0259ro\u011Flu","B\xF6y\xFCk Qala","Cabir \u018Fliyev","Camal Hac\u0131\u0259liyev","Cavadxan","Cavan\u015Fir","Ceyhun S\u0259limov","Ceyhunb\u0259y Hac\u0131b\u0259yli","C\u0259biyev","C\u0259f\u0259r X\u0259ndan","C\u0259f\u0259r Cabbarl\u0131","C\u0259lal Qurbanov","C\u0259lil M\u0259mm\u0259dquluzad\u0259","\xC7ingiz Mustafayev","\xC7obanzad\u0259","Dada\u015F B\xFCnyadzad\u0259","Da\u011Fl\u0131 Yunus","Dilar\u0259 \u018Fliyeva","El\xE7in \u018Fzimov","Eldar v\u0259 Abdulla \u018Flib\u0259yovlar","Elxan H\u0259s\u0259nov","El\u015F\u0259n Mehdiyev","El\u015F\u0259n S\xFCleymanov","Etibar B\u0259kirov","\u018Fbd\xFCl\u0259z\u0259l D\u0259mir\xE7izad\u0259","\u018Fbd\xFClh\u0259s\u0259n Anapl\u0131","\u018Fbd\xFClk\u0259rim \u018Flizad\u0259","\u018Fhm\u0259d b\u0259y A\u011Fao\u011Flu","\u018Fhm\u0259d Cavad","\u018Fhm\u0259d C\u0259mil","\u018Fhm\u0259d Mehbal\u0131yev","\u018Fhm\u0259d R\u0259c\u0259bli","\u018Fjd\u0259r Xanbabayev","\u018Fkr\u0259m C\u0259f\u0259rov","\u018Fl\u0259sg\u0259r Qay\u0131bov","\u018Flia\u011Fa Vahid","\u018Fli B\u0259y H\xFCseynzad\u0259","\u018Flim\u0259rdan b\u0259y Top\xE7uba\u015Fov","\u018Fliyar \u018Fliyev","\u018Fl\xF6vs\u0259t Abdulr\u0259himov","\u018Fl\xF6vs\u0259t Quliyev","\u018Fmir Ba\u011F\u0131rov","\u018Fs\u0259d \u018Fhm\u0259dov","\u018F\u015Fr\u0259f Yunusov","\u018Fzim \u018Fzimzad\u0259","\u018Fziz \u018Fliyev","Heyb\u0259t Heyb\u0259tov","H\u0259qiq\u0259t Rzayeva","H\u0259mid Arasl\u0131","H\u0259nif\u0259 \u018Fl\u0259sg\u0259rova","H\u0259rb\xE7il\u0259r","H\u0259s\u0259no\u011Fu","H\u0259s\u0259n Seyidb\u0259yli","H\u0259t\u0259m Allahverdiyev","H\u0259zi Aslanov","H\xFCs\xFC Hac\u0131yev","H\xFCseynqulu Sarabski","F\u0259t\u0259li xan Xoyski","F\u0259zail Bayramov","Fikr\u0259t \u018Fmirov","Fuad \u0130brahimb\u0259yov","Fuad Yusifov","General \u018Flia\u011Fa \u015E\u0131xlinski","G\xFClay\u0259 Q\u0259dirb\u0259yova","G\u0259nclik","Xaqani","Xan \u015Eu\u015Finski","Xanlar","Xudu M\u0259mm\u0259dov","\u0130brahimpa\u015Fa Dada\u015Fov","\u0130dris S\xFCleymanov","\u0130lqar Abbasov","\u0130lqar \u0130smay\u0131lov","\u0130mran Qas\u0131mov","\u0130nqilab \u0130smay\u0131lov","\u0130sf\u0259ndiyar Z\xFClalov","\u0130slam Ab\u0131\u015Fov","\u0130slam S\u0259f\u0259rli","\u0130smay\u0131l b\u0259y Qutqa\u015F\u0131nl\u0131","\u0130smay\u0131l Mirz\u0259g\xFClov","\u0130stiqlaliyy\u0259t","28 May","\u0130sg\u0259nd\u0259rov","\u0130van Turgenev","\u0130zmir","\u0130zz\u0259t H\u0259midov","\u0130zz\u0259t Orucova","Kamal R\u0259himov","Kaz\u0131m Kaz\u0131mzad\u0259","Kaz\u0131ma\u011Fa K\u0259rimov","K\u0259r\u0259m \u0130smay\u0131lov","Ki\xE7ik Qala","Koro\u011Flu R\u0259himov","Qa\xE7aq N\u0259bi","Qaraba\u011F","Q\u0259dirb\u0259yov","Q\u0259z\u0259nf\u0259r Musab\u0259yov","Q\u0259z\u0259nf\u0259r V\u0259liyev","Leyla M\u0259mm\u0259db\u0259yova","Mahmud \u0130brahimov","Malik M\u0259mm\u0259dov","Mehdi Abbasov","Mehdi Mehdizad\u0259","M\u0259h\u0259mm\u0259d \u018Fmin R\u0259sulzad\u0259","M\u0259h\u0259mm\u0259d Hadi","M\u0259h\u0259mm\u0259d Xiyabani","M\u0259h\u0259mm\u0259d ibn Hindu\u015Fah Nax\xE7\u0131vani","M\u0259hs\u0259ti G\u0259nc\u0259vi","M\u0259mm\u0259dyarov","M\u0259rdanov qarda\u015Flar\u0131","M\u0259tl\u0259b A\u011Fayev","M\u0259\u015F\u0259di Hilal","M\u0259zahir R\xFCst\u0259mov","Mikay\u0131l M\xFC\u015Fviq","Ming\u0259\xE7evir","Mir\u0259li Qa\u015Fqay","Mir\u0259li Seyidov","Mirza\u011Fa \u018Fliyev","Mirz\u0259 \u0130brahimov","Mirz\u0259 M\u0259nsur","Mirz\u0259 Mustafayev","Murtuza Muxtarov","Mustafa Top\xE7uba\u015Fov","M\xFCqt\u0259dir Ayd\u0131nb\u0259yov","M\xFCsl\xFCm Maqomayev","M\xFCz\u0259ff\u0259r H\u0259s\u0259nov","Nabat A\u015Furb\u0259yova","Nax\xE7\u0131vani","Naximov","Nazim \u0130smaylov","Neapol","Neft\xE7i Qurban Abbasov","Neft\xE7il\u0259r prospekti","N\u0259c\u0259fb\u0259y V\u0259zirov","N\u0259c\u0259fqulu R\u0259fiyev","N\u0259riman N\u0259rimanov","N\u0259sir\u0259ddin Tusi","Nigar R\u0259fib\u0259yli","Niyazi","Nizami","Nizami Abdullayev","Nobel prospekti","Novruz","Novruzov qarda\u015Flar\u0131","Oqtay V\u0259liyev","Parlament","Pu\u015Fkin","Rafiq A\u011Fayev","Ramiz Q\u0259mb\u0259rov","R\u0259\u015Fid Behbudov","R\u0259\u015Fid M\u0259cidov","Ruhulla Axundov","Ruslan Allahverdiyev","R\xFCst\u0259m R\xFCst\u0259mov","Tahir Ba\u011F\u0131rov","Tarzan Hac\u0131 M\u0259mm\u0259dov","Tbilisi prospekti","T\u0259briz (Bak\u0131)","T\u0259briz X\u0259lilb\u0259yli","Tofiq M\u0259mm\u0259dov","Tolstoy","Sabit Orucov","Sabit R\u0259hman","Sahib H\xFCmm\u0259tov","Salat\u0131n \u018Fsg\u0259rova","Sarayevo","Seyid \u018Fzim \u015Eirvani","Seyid \u015Eu\u015Finski","Seyidov","S\u0259m\u0259d b\u0259y Mehmandarov","S\u0259m\u0259d Vur\u011Fun","S\u0259ttar B\u0259hlulzad\u0259","Sona xan\u0131m V\u0259lixanl\u0131","S\xFCbhi Salayev","S\xFCleyman \u018Fhm\u0259dov","S\xFCleyman R\u0259himov","S\xFCleyman R\xFCst\u0259m","S\xFCleyman Sani Axundov","S\xFCleyman V\u0259zirov","\u015Eahin S\u0259m\u0259dov","\u015Eamil \u018Fzizb\u0259yov","\u015Eamil Kamilov","\u015Eeyx \u015Eamil","\u015E\u0259fay\u0259t Mehdiyev","\u015E\u0259msi B\u0259d\u0259lb\u0259yli","\u015Eirin Mirz\u0259yev","\u015E\u0131x\u0259li Qurbanov","\u015E\xF6vk\u0259t \u018Fl\u0259kb\u0259rova","\xDClvi B\xFCnyadzad\u0259","\xDCzeyir Hac\u0131b\u0259yov","Vasif \u018Fliyev","V\u0259li M\u0259mm\u0259dov","Vladislav Plotnikov","V\xFCqar Quliyev","Vunq Tau","Yaqub \u018Fliyev","Ya\u015Far Abdullayev","Ya\u015Far \u018Fliyev","Yav\u0259r \u018Fliyev","Yesenin","Y\u0259hya H\xFCseynov","Y\u0131lmaz Axundzad\u0259","Y\xFCsif Eyvazov","Yusif Qas\u0131mov","Yusif M\u0259mm\u0259d\u0259liyev","Yusif S\u0259f\u0259rov","Yusif V\u0259zir \xC7\u0259m\u0259nz\u0259minli","Zahid \u018Fliyev","Zahid X\u0259lilov","Zaur K\u0259rimov","Zavod","Z\u0259rg\u0259rpalan"];var B=["{{location.street_suffix}} {{location.street_name}}","{{location.street_name}} {{location.street_suffix}}"];var N=["k\xFC\xE7.","k\xFC\xE7\u0259si","prospekti","pr.","sah\u0259si","sh."];var U={building_number:z,city_name:A,city_pattern:k,country:h,postcode:M,secondary_address:c,state:x,street_address:q,street_name:S,street_pattern:B,street_suffix:N},g=U;var J={title:"Azerbaijani",code:"az",language:"az",endonym:"az\u0259rbaycan dili",dir:"ltr",script:"Latn"},Q=J;var D={generic:["Abbas","Abdulla","Adeliya","Adil","Afaq","Af\u0259l","Af\u0259rim","Af\u0259t","Aid\u0259","Akif","Alsu","Amid","Anar","Anna","Aqil","Ayan","Aydan","Ayg\xFCl","Ayg\xFCn","Aylin","Aynur","Ayt\u0259n","Bahar","Banu","Billur\u0259","B\u0259hram","B\u0259hruz","B\u0259xtiyar","B\u0259yaz","Cansu","Ceyla","Damla","Diana","Dilar\u0259","D\u0259niz","Ella","Ellada","Elnar\u0259","Elnur","Elvira","Elyanora","Elza","Emil","Emin","Esmira","Estella","Faiq","Fatim\u0259","Fidan","Firuz\u0259","F\u0259rqan\u0259","F\u0259r\u0259h","F\u0259xriyy\u0259","G\xF6vh\u0259r","G\xFClay","G\xFCls\xFCm","G\xFCl\xE7in","G\xFCl\u0259r","G\xFCnay","Humay","H\xFClya","H\xFCriy\u0259","Jal\u0259","Jasmin","Kamran","K\xFCbra","Lal\u0259","Lamiy\u0259","Laura","Leyla","Liliya","L\u0259man","Maya","Mehriban","M\u0259l\u0259k","Nadir","Nahid","Natiq","Nigar","Nihad","Nuray","Nurg\xFCn","Nurlan","N\u0259rgiz","Ofelya","P\u0259ri","Rafiq","R\xF6ya","R\u0259\u015Fad","R\u0259\u015Fid","Selcan","S\u0259bin\u0259","Tahir","Tansu","Tuba","Tunar","T\u0259rlan","Ulduz","Zahir","Zaur","\xDClk\u0259r","\xDClviyy\u0259","\u0130lham","\u0130lqar","\u0130xtiyar","\u015Eaiq","\u015E\u0259hriyar","\u018Fhm\u0259d"],female:["Adeliya","Afaq","Af\u0259rim","Af\u0259t","Aid\u0259","Alsu","Anna","Ayan","Aydan","Ayg\xFCl","Ayg\xFCn","Aylin","Aynur","Ayt\u0259n","Bahar","Banu","Billur\u0259","B\u0259yaz","Cansu","Ceyla","Damla","Diana","Dilar\u0259","D\u0259niz","Ella","Ellada","Elnar\u0259","Elvira","Elyanora","Elza","Esmira","Estella","Fatim\u0259","Fidan","Firuz\u0259","F\u0259rqan\u0259","F\u0259r\u0259h","F\u0259xriyy\u0259","G\xF6vh\u0259r","G\xFClay","G\xFCls\xFCm","G\xFCl\xE7in","G\xFCl\u0259r","G\xFCnay","Humay","H\xFClya","H\xFCriy\u0259","Jal\u0259","Jasmin","K\xFCbra","Lal\u0259","Lamiy\u0259","Laura","Leyla","Liliya","L\u0259man","Maya","Mehriban","M\u0259l\u0259k","Nigar","Nuray","Nurg\xFCn","N\u0259rgiz","Ofelya","P\u0259ri","R\xF6ya","Selcan","S\u0259bin\u0259","Tansu","Tuba","Ulduz","\xDClk\u0259r","\xDClviyy\u0259"],male:["Abbas","Abdulla","Adil","Af\u0259l","Akif","Amid","Anar","Aqil","B\u0259hram","B\u0259hruz","B\u0259xtiyar","Elnur","Emil","Emin","Faiq","Kamran","Nadir","Nahid","Natiq","Nihad","Nurlan","Rafiq","R\u0259\u015Fad","R\u0259\u015Fid","Tahir","Tunar","T\u0259rlan","Zahir","Zaur","\u0130lham","\u0130lqar","\u0130xtiyar","\u015Eaiq","\u015E\u0259hriyar","\u018Fhm\u0259d"]};var K={generic:["Abdullayeva","M\u0259mm\u0259dov","N\u0259z\u0259rov","Qas\u0131mova","Quliyev","R\u0259himov","R\u0259\u015Fidova","Seyidova","Soltanov","Soltanova","Tahirova","V\u0259liyev","V\u0259siyeva","X\u0259lilov","\u018Ff\u0259ndiyeva","\u018Fhm\u0259dov","\u018Fliyev","\u018Fliyeva","\u018Fl\u0259kb\u0259rov","\u018Fl\u0259kb\u0259rova"],female:["Abdullayeva","Qas\u0131mova","R\u0259\u015Fidova","Seyidova","Soltanova","Tahirova","V\u0259siyeva","\u018Ff\u0259ndiyeva","\u018Fliyeva","\u018Fl\u0259kb\u0259rova"],male:["M\u0259mm\u0259dov","N\u0259z\u0259rov","Quliyev","R\u0259himov","Soltanov","V\u0259liyev","X\u0259lilov","\u018Fhm\u0259dov","\u018Fliyev","\u018Fl\u0259kb\u0259rov"]};var R={female:[{value:"{{person.last_name.female}}",weight:1}],male:[{value:"{{person.last_name.male}}",weight:1}]};var T=[{value:"{{person.firstName}}",weight:1},{value:"{{person.lastName}} {{person.firstName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:1}];var C=null;var _=null;var j={first_name:D,last_name:K,last_name_pattern:R,name:T,prefix:C,suffix:_},H=j;var E=["(9##)###-##-##"];var L=["+9949#########"];var F=["9#########"];var I={human:E,international:L,national:F},P=I;var W={format:P},Y=W;var $={color:n,commerce:t,company:d,date:s,internet:p,location:g,metadata:Q,person:H,phone_number:Y},G= exports.a =$;var Qi=new (0, _chunkZKNYQOPPcjs.n)({locale:[G,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = G; exports.b = Qi;
