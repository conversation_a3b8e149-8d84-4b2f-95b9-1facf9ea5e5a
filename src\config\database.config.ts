import { registerAs } from '@nestjs/config';

export const databaseConfig = registerAs('database', () => {
  const isTest = process.env.NODE_ENV === 'test';

  return {
    type: 'postgres',
    host: isTest ? (process.env.TEST_DB_HOST || 'localhost') : (process.env.DB_HOST || 'localhost'),
    port: isTest ? parseInt(process.env.TEST_DB_PORT || '5432', 10) : parseInt(process.env.DB_PORT || '5432', 10),
    username: isTest ? (process.env.TEST_DB_USERNAME || 'postgres') : (process.env.DB_USERNAME || 'postgres'),
    password: isTest ? (process.env.TEST_DB_PASSWORD || 'password') : (process.env.DB_PASSWORD || 'password'),
    database: isTest ? (process.env.TEST_DB_NAME || 'peoplenest_test') : (process.env.DB_NAME || 'peoplenest_hrms'),
  
  // Connection pool settings
  poolSize: parseInt(process.env.DB_POOL_SIZE || '20', 10),
  maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '100', 10),
  acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000', 10),
  timeout: parseInt(process.env.DB_TIMEOUT || '60000', 10),
  
  // SSL configuration for production
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false,
    ca: process.env.DB_SSL_CA,
    cert: process.env.DB_SSL_CERT,
    key: process.env.DB_SSL_KEY,
  } : false,
  
  // Logging
  logging: process.env.NODE_ENV === 'development',
  logger: 'advanced-console',
  
  // Migration settings
  migrationsRun: process.env.NODE_ENV === 'production',
  synchronize: isTest ? (process.env.DB_SYNCHRONIZE === 'true') : (process.env.NODE_ENV === 'development'),
  dropSchema: isTest ? (process.env.DB_DROP_SCHEMA === 'true') : false,
  
  // Multi-tenancy settings
  tenantMode: process.env.TENANT_MODE || 'schema', // 'schema' | 'database' | 'shared'
  defaultTenant: process.env.DEFAULT_TENANT || 'default',
  
  // Performance settings
  cache: {
    duration: parseInt(process.env.DB_CACHE_DURATION || '30000', 10), // 30 seconds
    type: 'redis',
  },
  
  // Backup settings
  backup: {
    enabled: process.env.DB_BACKUP_ENABLED === 'true',
    schedule: process.env.DB_BACKUP_SCHEDULE || '0 2 * * *', // Daily at 2 AM
    retention: parseInt(process.env.DB_BACKUP_RETENTION || '30', 10), // 30 days
  },
  };
});
