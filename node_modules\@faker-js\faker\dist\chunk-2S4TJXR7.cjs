"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var e=["Aeg<PERSON><PERSON>berger","Albino","Alt\xE9r real","American Paint Horse","American Saddlebred","Angloarab","Angloarab Shagya","Appaloosa","Australian Stock Horse","Austriacki ko\u0144 gor\u0105cokrwisty","Bali","Brumby","Caballo Chilen","Camargue","Campolina","Canadian cutting horse","Cavallino della Giara","Cimarron","Cleveland Bay","Clydesdale","Cob","Colorado Ranger","Comtois","Criollo","Crioulo","D\xF6le Gudbrandsdal","<PERSON>siedle","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>\xE4nder","<PERSON><PERSON><PERSON>","Groningery","Hack","Hackney","<PERSON><PERSON><PERSON>","<PERSON>","Hunter","Irish Draught","Jaf","Jutland","Kasztanowy ko\u0144 szwarcwaldzki","Knabstrub","Ko\u0144 bulo\u0144ski","Kuc Exmoor","K\u0142usak ameryka\u0144ski","Llareno","Lusitano","Mangalarga","Mangalarga Marchador","Maremmano","Missouri Fox Trotter","Morgan","Murakozi","Mustang","Nonius","Noriker","Paso Fino","Paso Peruano","Perszeron","Pinto","Poitevin","Quarter Horse","Rosyjski ko\u0144 zimnokrwisty","Salernitano","Sanfratellano","Schweike","Selle fran\xE7ais","Shiraz (Darashoori)","Shire","Siciliano","Suffolk Punch","Szwedzki ko\u0144 gor\u0105cokrwisty","Tarpan","Tchenerani","Tennessee Walker","Tinker \u2013 Gypsy Vanner","Waler","Walijski kuc g\xF3rski","Welsh Cob","Wiatka","Zweibr\xFCcker","ameryka\u0144ski kuc szetlandzki","angloarab sardy\u0144ski","bawarski ko\u0144 gor\u0105cokrwisty","belgijski ko\u0144 gor\u0105cokrwisty","brytyjski ko\u0144 gor\u0105cokrwisty","ci\u0119\u017Cki sakso\u0144ski ko\u0144 gor\u0105cokrwisty","czechos\u0142owacki ko\u0144 gor\u0105cokrwisty","czechos\u0142owacki ko\u0144 zimnokrwisty","g\xF3rski konik bo\u015Bniacki","hispano","holenderski ko\u0144 gor\u0105cokrwisty","holenderski ko\u0144 zimnokrwisty","indyjski ko\u0144 wojskowy","jugos\u0142owia\u0144ski ko\u0144 zimnokrwisty","karacabey","kathiawari","kleppery esto\u0144skie","kleppery \u0142otewskie i litewskie","konie kaimanawa","konik dulme\u0144ski","konik polski","konik \u017Cmudzki","ko\u0144 Dongolavi","ko\u0144 Jonaguni","ko\u0144 Przewalskiego","ko\u0144 abisy\u0144ski","ko\u0144 acha\u0142-teki\u0144ski","ko\u0144 afga\u0144ski i pakista\u0144ski","ko\u0144 andaluzyjski","ko\u0144 arde\u0144ski","ko\u0144 aztecki","ko\u0144 bade\u0144sko-wirtemberski","ko\u0144 belgijski","ko\u0144 berberyjski","ko\u0144 breto\u0144ski","ko\u0144 budionnowski","ko\u0144 burgundzki","ko\u0144 czystej krwi arabskiej","ko\u0144 do\u0144ski","ko\u0144 dunajski","ko\u0144 fiordzki","ko\u0144 fi\u0144ski","ko\u0144 fryderyksborski","ko\u0144 fryzyjski","ko\u0144 hanowerski","ko\u0144 heski","ko\u0144 holszty\u0144ski","ko\u0144 huculski","ko\u0144 islandzki","ko\u0144 jomudzki","ko\u0144 kabardy\u0144ski","ko\u0144 kalabryjski","ko\u0144 karabachski","ko\u0144 kazachski","ko\u0144 kiwlendzki","ko\u0144 ki\u0144ski","ko\u0144 kladrubski","ko\u0144 kuzniecki","ko\u0144 lipica\u0144sk","ko\u0144 lokajski","ko\u0144 ma\u0142opolski","ko\u0144 meklemburski","ko\u0144 mese\u0144ski","ko\u0144 norycki (Pinzgauer)","ko\u0144 nowokirgiski","ko\u0144 oldenburski","ko\u0144 peczorski","ko\u0144 pe\u0142nej krwi angielskiej","ko\u0144 plewe\u0144ski","ko\u0144 poleski","ko\u0144 pozna\u0144ski","ko\u0144 p\xF3\u0142nocnoszwedzki","ko\u0144 sok\xF3lski","ko\u0144 szlachetny p\xF3\u0142krwi","ko\u0144 szwajcarski","ko\u0144 terski","ko\u0144 toryjski","ko\u0144 trake\u0144ski","ko\u0144 turkme\u0144ski","ko\u0144 ukrai\u0144ski","ko\u0144 westfalski","ko\u0144 wielkopolski","ko\u0144 woroneski","ko\u0144 wschodniobu\u0142garski","ko\u0144 wschodniofryzyjski","ko\u0144 w\u0142odzimierski","ko\u0144 \u015Bl\u0105ski","kuc Bardigiano","kuc Belear\xF3w","kuc Connemara","kuc Dales","kuc Dartmoor","kuc Fell","kuc Fulani","kuc Galiceno","kuc Garrano","kuc Gharbaui","kuc Hackney","kuc Landais","kuc Lewitzer","kuc Merens","kuc New Forest","kuc Pindos","kuc Pottok","kuc Rocky Mountain","kuc Sable Island","kuc Skyrian","kuc Skyros","kuc Sorraia","kuc Togo","kuc Zaniskari","kuc ameryka\u0144ski-Americas","kuc australijski","kuc feli\u0144ski","kuc galla","kuc gotlandzki","kuc indyjski","kuc jakucki","kuc jawajski","kuc kaspijski","kuc kenijski","kuc korsyka\u0144ski","kuc mongolski","kuc nigeryjski","kuc sardy\u0144ski (Achetta)","kuc somalijski","kuc sycylijski","kuc szetlandzki","kuc tybeta\u0144ski","kuc \u017Cemajtuka","kurdyjski","k\u0142usak (i inochodziec) gor\u0105cokrwisty \u2013 Standardbred","k\u0142usak or\u0142owsk","k\u0142usak rosyjsk","litewski ko\u0144 zimnokrwisty","murgese","niemiecki kuc wierzchowy","normandzki cob","perski ko\u0144 arabski","polski ko\u0144 zimnokrwisty","radziecki ci\u0119\u017Cki ko\u0144 poci\u0105gowy","re\u0144ski ko\u0144 gor\u0105cokrwisty","re\u0144ski ko\u0144 zimnokrwisty","syryjski ko\u0144 arabski","szlezwicki ko\u0144 zimnokrwisty","walijski kuc g\xF3rski","westfalski ko\u0144 zimnokrwisty","w\u0119gierski ko\u0144 sportowy","w\u0142oski ko\u0144 zimnokrwisty","\u0141otewski ko\u0144 gor\u0105cokrwisty"];var sa={horse:e},r=sa;var n=["Andrzej Maleszka","Andrzej Pilipiuk","Andrzej Sapkowski","Anita G\u0142owi\u0144ska","A\u0142bena Grabowska","Czes\u0142aw Mi\u0142osz","Grzegorz Kasdepke","Henryk Sienkiewicz","Jacek Dukaj","Jacek Piekara","Jakub \u017Bulczyk","Jaros\u0142aw Grz\u0119dowicz","Jerzy Pilch","Joanna Bator","Katarzyna Bonda","Katarzyna Michalak","Krzysztof Kamil Baczy\u0144ski","Leopold Tyrmand","Mariusz Szczygie\u0142","Marta Galewska-Kustra","Olga Tokarczuk","Paulina \u015Awist","Remigiusz Mr\xF3z","Stanis\u0142aw Lem","Sylwia Chutnik","Szczepan Twardoch","Tadeusz Konwicki","Wies\u0142aw My\u015Bliwski","Wis\u0142awa Szymborska","Wojciech Chmielarz","Zbigniew Herbert","Zygmunt Mi\u0142oszewski"];var k=["Audiobook","Ebook","Mi\u0119kka ok\u0142adka","Twarda ok\u0142adka"];var s=["Beletrystyka","Biografia","Fantastyka","Fantastyka naukowa","Krymina\u0142","Ksi\u0105\u017Cka kucharska","Literatura dzieci\u0119ca","Literatura faktu","Literatura m\u0142odzie\u017Cowa","Literatura naukowa","Literatura pi\u0119kna","Literatura podr\xF3\u017Cnicza","Literatura popularnonaukowa","Poezja","Powie\u015B\u0107 historyczna","Powie\u015B\u0107 przygodowa","Reporta\u017C","Rozw\xF3j osobisty"];var t=["Czarne","Dom Wydawniczy Rebis","Muza","PWN","W.A.B.","Wydawnictwo Agora","Wydawnictwo Literackie","Wydawnictwo \u015Awiat Ksi\u0105\u017Cki","Znak","Zysk i S-ka Wydawnictwo"];var l=["Archiwum Burzowego \u015Awiat\u0142a","Cykl Inkwizytorski","Diuna","Dziedzictwo","Harry Potter","Igrzyska \u015Amierci","Jakub W\u0119drowycz","Ko\u0142o Czasu","Malaza\u0144ska Ksi\u0119ga Poleg\u0142ych","Mroczna Wie\u017Ca","Mroczne Materie","Opowie\u015Bci z Narnii","Pan Lodowego Ogrodu","Percy Jackson","Pie\u015B\u0144 lodu i ognia","Saga o Ludziach Lodu","Sherlock Holmes","Wied\u017Amin","W\u0142adca Pier\u015Bcieni","Zwiadowcy","\u015Awiat Dysku"];var m=["Aria z kurantem","Arytmia","Austeria","Bal w operze","Barbarzy\u0144ca w ogrodzie","Bitwa o Monte Cassino","Bramy raju","Cantus","Cesarz","Cudzoziemka","Czarny polonez","Czarny potok","Do piachu","Droga donik\u0105d","Dziennik","Dziennik 1954","Dziennik bez samog\u0142osek","Dziennik pisany noc\u0105","Eseje dla Kasandry","Ferdydurke","Genera\u0142 Barcz","Granica","Imperium","Inny \u015Bwiat","Jezioro Bode\u0144skie","Kamie\u0144 na kamieniu","Kariera Nikodema Dyzmy","Karmazynowy poemat","Kartoteka","Konopielka","Lato 1932","Lekcja martwego j\u0119zyka","Ludzie na mo\u015Bcie","Madame","Ma\u0142a apokalipsa","Medaliony","Miazga","My\u015Bli nieuczesane","M\xF3j wiek. Pami\u0119tnik m\xF3wiony","Na nieludzkiej ziemi","Na wysokiej po\u0142oninie","Nadberezy\u0144cy","Nap\xF3j cienisty","Nie trzeba g\u0142o\u015Bno m\xF3wi\u0107","Nienasycenie","Niepok\xF3j","Noce i dnie","Nuta cz\u0142owiecza","Obroty rzeczy","Ocalenie","Oktostychy","Pami\u0119tnik z Powstania Warszawskiego","Pan Cogito","Panny z Wilka","Pierwszy krok w chmurach","Pier\u015Bcie\u0144 z papieru","Podr\xF3\u017Ce do piekie\u0142","Po\u017Cegnanie jesieni","Po\u017Cegnanie z Mari\u0105","Po\u017Coga","Przed Nieznanym Trybuna\u0142em","Przedwio\u015Bnie","Przemija posta\u0107 \u015Bwiata","Raport o stanie wojennym","Raport z obl\u0119\u017Conego miasta","Rodzinna Europa","Rozmowy polskie latem roku 1983","Rzeczpospolita Obojga Narod\xF3w","R\xF3wnanie serca","Sanatorium pod klepsydr\u0105","Sennik wsp\xF3\u0142czesny","Sklepy cynamonowe","Sokrates ta\u0144cz\u0105cy","Solaris","Srebrne or\u0142y","Szewcy","Szkice pi\xF3rkiem","S\xF3l ziemi","Tango","Trans-Atlantyk","Trzy zimy","Utwory poetyckie","W polu","Wariacje pocztowe","Widnokr\u0105g","Widok\xF3wka z tego \u015Bwiata","Wiersze","Wieszanie","Wiosna i wino","Wo\u0142anie do Yeti","Zach\xF3d s\u0142o\u0144ca w Milan\xF3wku","Zasypie wszystko, zawieje\u2026","Zd\u0105\u017Cy\u0107 przed Panem Bogiem","Ziemia Urlo","Zimne kraje","Zniewolony umys\u0142","Zosta\u0142o z uczty bog\xF3w","\u0141\u0105ka","\u015Alub"];var ta={author:n,format:k,genre:s,publisher:t,series:l,title:m},c=ta;var z=["50#-###-###","51#-###-###","53#-###-###","57#-###-###","60#-###-###","66#-###-###","69#-###-###","72#-###-###","73#-###-###","78#-###-###","79#-###-###","88#-###-###"];var la={formats:z},y=la;var u=["be\u017Cowy","bia\u0142y","br\u0105zowy","czarny","czerwony","fioletowy","granatowy","niebieski","pomara\u0144czowy","r\xF3\u017Cowy","szary","zielony","\u017C\xF3\u0142ty"];var ma={human:u},w=ma;var d=["S.A.","SKA","Sp. j.","Sp. k.","Sp. p.","s.c.","z o.o"];var b=["{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} and {{person.last_name.generic}}","{{person.last_name.generic}}-{{person.last_name.generic}}"];var ca={legal_entity_type:d,name_pattern:b},p=ca;var S=["agro.pl","auto.pl","biz.pl","com.pl","edu.pl","gmina.pl","gov.pl","info.pl","miasta.pl","net.pl","nieruchomosci.pl","org.pl","pl","powiat.pl","priv.pl","sklep.pl","szkola.pl","targi.pl","turystyka.pl"];var K=["gmail.com","hotmail.com","yahoo.com"];var za={domain_suffix:S,free_email:K},g=za;var P=["###","##","##a","##b","##c","#/#"];var M=["Aleksandr\xF3w Kujawski","Aleksandr\xF3w \u0141\xF3dzki","Alwernia","Andrych\xF3w","Annopol","August\xF3w","Babimost","Babor\xF3w","Baran\xF3w Sandomierski","Barcin","Barczewo","Bardo","Barlinek","Bartoszyce","Barwice","Be\u0142chat\xF3w","Be\u0142\u017Cyce","B\u0119dzin","Bia\u0142a","Bia\u0142a Piska","Bia\u0142a Podlaska","Bia\u0142a Rawska","Bia\u0142obrzegi","Bia\u0142ogard","Bia\u0142y B\xF3r","Bia\u0142ystok","Biecz","Bielawa","Bielsk Podlaski","Bielsko-Bia\u0142a","Bieru\u0144","Bierut\xF3w","Bie\u017Cu\u0144","Bi\u0142goraj","Biskupiec","Bisztynek","Blachownia","B\u0142aszki","B\u0142a\u017Cowa","B\u0142onie","Bobolice","Bobowa","Bochnia","Bodzentyn","Bogatynia","Boguchwa\u0142a","Bogusz\xF3w-Gorce","Bojanowo","Boles\u0142awiec","Bolk\xF3w","Borek Wielkopolski","Borne Sulinowo","Braniewo","Bra\u0144sk","Brodnica","Brok","Brusy","Brwin\xF3w","Brzeg","Brzeg Dolny","Brzesko","Brzeszcze","Brze\u015B\u0107 Kujawski","Brzeziny","Brzostek","Brzoz\xF3w","Buk","Bukowno","Busko-Zdr\xF3j","Bychawa","Byczyna","Bydgoszcz","Bystrzyca K\u0142odzka","Bytom","Bytom Odrza\u0144ski","Byt\xF3w","Cedynia","Che\u0142m","Che\u0142mek","Che\u0142mno","Che\u0142m\u017Ca","Ch\u0119ciny","Chmielnik","Chocian\xF3w","Chociwel","Chodecz","Chodzie\u017C","Chojna","Chojnice","Chojn\xF3w","Choroszcz","Chorzele","Chorz\xF3w","Choszczno","Chrzan\xF3w","Ciechanowiec","Ciechan\xF3w","Ciechocinek","Cieszan\xF3w","Cieszyn","Ci\u0119\u017Ckowice","Cybinka","Czaplinek","Czarna Bia\u0142ostocka","Czarna Woda","Czarne","Czarnk\xF3w","Czch\xF3w","Czechowice-Dziedzice","Czelad\u017A","Czempi\u0144","Czerniejewo","Czersk","Czerwie\u0144sk","Czerwionka-Leszczyny","Cz\u0119stochowa","Cz\u0142opa","Cz\u0142uch\xF3w","Czy\u017Cew","\u0106miel\xF3w","Daleszyce","Dar\u0142owo","D\u0105bie","D\u0105browa Bia\u0142ostocka","D\u0105browa G\xF3rnicza","D\u0105browa Tarnowska","Debrzno","D\u0119bica","D\u0119blin","D\u0119bno","Dobczyce","Dobiegniew","Dobra","Dobre Miasto","Dobrodzie\u0144","Dobrzany","Dobrzy\u0144 nad Wis\u0142\u0105","Dolsk","Drawno","Drawsko Pomorskie","Drezdenko","Drobin","Drohiczyn","Drzewica","Dukla","Duszniki-Zdr\xF3j","Dyn\xF3w","Dzia\u0142dowo","Dzia\u0142oszyce","Dzia\u0142oszyn","Dzierzgo\u0144","Dzier\u017Coni\xF3w","Dziwn\xF3w","Elbl\u0105g","E\u0142k","Frampol","Frombork","Garwolin","G\u0105bin","Gda\u0144sk","Gdynia","Gi\u017Cycko","Glinojeck","Gliwice","G\u0142og\xF3w","G\u0142og\xF3w Ma\u0142opolski","G\u0142og\xF3wek","G\u0142owno","G\u0142ubczyce","G\u0142ucho\u0142azy","G\u0142uszyca","Gniew","Gniewkowo","Gniezno","Gogolin","Golczewo","Goleni\xF3w","Golina","Golub-Dobrzy\u0144","Go\u0142a\u0144cz","Go\u0142dap","Goni\u0105dz","Gorlice","Gorz\xF3w \u015Al\u0105ski","Gorz\xF3w Wielkopolski","Gostynin","Gosty\u0144","Go\u015Bcino","Gozdnica","G\xF3ra","G\xF3ra Kalwaria","G\xF3rowo I\u0142aweckie","G\xF3rzno","Grab\xF3w nad Prosn\u0105","Grajewo","Grodk\xF3w","Grodzisk Mazowiecki","Grodzisk Wielkopolski","Gr\xF3jec","Grudzi\u0105dz","Gryb\xF3w","Gryfice","Gryfino","Gryf\xF3w \u015Al\u0105ski","Gubin","Hajn\xF3wka","Halin\xF3w","Hel","Hrubiesz\xF3w","I\u0142awa","I\u0142owa","I\u0142\u017Ca","Imielin","Inowroc\u0142aw","I\u0144sko","Iwonicz-Zdr\xF3j","Izbica Kujawska","Jab\u0142onowo Pomorskie","Janikowo","Janowiec Wielkopolski","Jan\xF3w Lubelski","Jarocin","Jaros\u0142aw","Jasie\u0144","Jas\u0142o","Jastarnia","Jastrowie","Jastrz\u0119bie-Zdr\xF3j","Jawor","Jaworzno","Jaworzyna \u015Al\u0105ska","Jedlicze","Jedlina-Zdr\xF3j","Jedwabne","Jelcz-Laskowice","Jelenia G\xF3ra","Jeziorany","J\u0119drzej\xF3w","Jordan\xF3w","J\xF3zef\xF3w","Jutrosin","Kalety","Kalisz","Kalisz Pomorski","Kalwaria Zebrzydowska","Ka\u0142uszyn","Kamienna G\xF3ra","Kamie\u0144 Kraje\u0144ski","Kamie\u0144 Pomorski","Kamie\u0144sk","Ka\u0144czuga","Karczew","Kargowa","Karlino","Karpacz","Kartuzy","Katowice","Kazimierz Dolny","Kazimierza Wielka","K\u0105ty Wroc\u0142awskie","Kcynia","K\u0119dzierzyn-Ko\u017Ale","K\u0119pice","K\u0119pno","K\u0119trzyn","K\u0119ty","Kielce","Kietrz","Kisielice","Kleczew","Kleszczele","Kluczbork","K\u0142ecko","K\u0142obuck","K\u0142odawa","K\u0142odzko","Knur\xF3w","Knyszyn","Kobylin","Koby\u0142ka","Kock","Kolbuszowa","Kolno","Kolonowskie","Koluszki","Ko\u0142aczyce","Ko\u0142o","Ko\u0142obrzeg","Koniecpol","Konin","Konstancin-Jeziorna","Konstantyn\xF3w \u0141\xF3dzki","Ko\u0144skie","Koprzywnica","Korfant\xF3w","Koronowo","Korsze","Kos\xF3w Lacki","Kostrzyn","Kostrzyn nad Odr\u0105","Koszalin","Ko\u015Bcian","Ko\u015Bcierzyna","Kowal","Kowalewo Pomorskie","Kowary","Kozieg\u0142owy","Kozienice","Ko\u017Amin Wielkopolski","Ko\u017Cuch\xF3w","K\xF3rnik","Krajenka","Krak\xF3w","Krapkowice","Krasnobr\xF3d","Krasnystaw","Kra\u015Bnik","Krobia","Krosno","Krosno Odrza\u0144skie","Kro\u015Bniewice","Krotoszyn","Kruszwica","Krynica Morska","Krynica-Zdr\xF3j","Krynki","Krzanowice","Krzepice","Krzeszowice","Krzywi\u0144","Krzy\u017C Wielkopolski","Ksi\u0105\u017C Wielkopolski","Kudowa-Zdr\xF3j","Kun\xF3w","Kutno","Ku\u017Ania Raciborska","Kwidzyn","L\u0105dek-Zdr\xF3j","Legionowo","Legnica","Lesko","Leszno","Le\u015Bna","Le\u015Bnica","Lewin Brzeski","Le\u017Cajsk","L\u0119bork","L\u0119dziny","Libi\u0105\u017C","Lidzbark","Lidzbark Warmi\u0144ski","Limanowa","Lipiany","Lipno","Lipsk","Lipsko","Lubacz\xF3w","Luba\u0144","Lubart\xF3w","Lubawa","Lubawka","Lubie\u0144 Kujawski","Lubin","Lublin","Lubliniec","Lubniewice","Lubomierz","Lubo\u0144","Lubraniec","Lubsko","Lw\xF3wek","Lw\xF3wek \u015Al\u0105ski","\u0141abiszyn","\u0141a\u0144cut","\u0141apy","\u0141asin","\u0141ask","\u0141askarzew","\u0141aszcz\xF3w","\u0141aziska G\xF3rne","\u0141azy","\u0141eba","\u0141\u0119czna","\u0141\u0119czyca","\u0141\u0119knica","\u0141obez","\u0141ob\u017Cenica","\u0141och\xF3w","\u0141omianki","\u0141om\u017Ca","\u0141osice","\u0141owicz","\u0141\xF3d\u017A","\u0141uk\xF3w","Mak\xF3w Mazowiecki","Mak\xF3w Podhala\u0144ski","Malbork","Ma\u0142ogoszcz","Ma\u0142omice","Margonin","Marki","Maszewo","Miasteczko \u015Al\u0105skie","Miastko","Micha\u0142owo","Miech\xF3w","Miejska G\xF3rka","Mielec","Mierosz\xF3w","Mieszkowice","Mi\u0119dzyb\xF3rz","Mi\u0119dzych\xF3d","Mi\u0119dzylesie","Mi\u0119dzyrzec Podlaski","Mi\u0119dzyrzecz","Mi\u0119dzyzdroje","Miko\u0142ajki","Miko\u0142\xF3w","Mikstat","Milan\xF3wek","Milicz","Mi\u0142akowo","Mi\u0142om\u0142yn","Mi\u0142os\u0142aw","Mi\u0144sk Mazowiecki","Miros\u0142awiec","Mirsk","M\u0142awa","M\u0142ynary","Mogielnica","Mogilno","Mo\u0144ki","Mor\u0105g","Mordy","Mory\u0144","Mosina","Mr\u0105gowo","Mrocza","Mszana Dolna","Mszczon\xF3w","Murowana Go\u015Blina","Muszyna","Mys\u0142owice","Myszk\xF3w","Myszyniec","My\u015Blenice","My\u015Blib\xF3rz","Nak\u0142o nad Noteci\u0105","Na\u0142\u0119cz\xF3w","Namys\u0142\xF3w","Narol","Nasielsk","Nekla","Nidzica","Niemcza","Niemodlin","Niepo\u0142omice","Nieszawa","Nisko","Nowa D\u0119ba","Nowa Ruda","Nowa Sarzyna","Nowa S\xF3l","Nowe","Nowe Brzesko","Nowe Miasteczko","Nowe Miasto Lubawskie","Nowe Miasto nad Pilic\u0105","Nowe Skalmierzyce","Nowe Warpno","Nowogard","Nowogrodziec","Nowogr\xF3d","Nowogr\xF3d Bobrza\u0144ski","Nowy Dw\xF3r Gda\u0144ski","Nowy Dw\xF3r Mazowiecki","Nowy S\u0105cz","Nowy Staw","Nowy Targ","Nowy Tomy\u015Bl","Nowy Wi\u015Bnicz","Nysa","Oborniki","Oborniki \u015Al\u0105skie","Obrzycko","Odolan\xF3w","Ogrodzieniec","Okonek","Olecko","Olesno","Oleszyce","Ole\u015Bnica","Olkusz","Olsztyn","Olsztynek","Olszyna","O\u0142awa","Opalenica","Opat\xF3w","Opoczno","Opole","Opole Lubelskie","Orneta","Orzesze","Orzysz","Osieczna","Osiek","Ostro\u0142\u0119ka","Ostror\xF3g","Ostrowiec \u015Awi\u0119tokrzyski","Ostr\xF3da","Ostr\xF3w Lubelski","Ostr\xF3w Mazowiecka","Ostr\xF3w Wielkopolski","Ostrzesz\xF3w","O\u015Bno Lubuskie","O\u015Bwi\u0119cim","Otmuch\xF3w","Otwock","Ozimek","Ozork\xF3w","O\u017Car\xF3w","O\u017Car\xF3w Mazowiecki","Pabianice","Paczk\xF3w","Paj\u0119czno","Pako\u015B\u0107","Parczew","Pas\u0142\u0119k","Pasym","Pelplin","Pe\u0142czyce","Piaseczno","Piaski","Piast\xF3w","Piechowice","Piekary \u015Al\u0105skie","Pieni\u0119\u017Cno","Pie\u0144sk","Pieszyce","Pilawa","Pilica","Pilzno","Pi\u0142a","Pi\u0142awa G\xF3rna","Pi\u0144cz\xF3w","Pionki","Piotrk\xF3w Kujawski","Piotrk\xF3w Trybunalski","Pisz","Piwniczna-Zdr\xF3j","Pleszew","P\u0142ock","P\u0142o\u0144sk","P\u0142oty","Pniewy","Pobiedziska","Podd\u0119bice","Podkowa Le\u015Bna","Pogorzela","Polanica-Zdr\xF3j","Polan\xF3w","Police","Polkowice","Po\u0142aniec","Po\u0142czyn-Zdr\xF3j","Poniatowa","Poniec","Por\u0119ba","Pozna\u0144","Prabuty","Praszka","Prochowice","Proszowice","Pr\xF3szk\xF3w","Pruchnik","Prudnik","Prusice","Pruszcz Gda\u0144ski","Pruszk\xF3w","Przasnysz","Przec\u0142aw","Przedb\xF3rz","Przedecz","Przemk\xF3w","Przemy\u015Bl","Przeworsk","Przysucha","Pszczyna","Psz\xF3w","Puck","Pu\u0142awy","Pu\u0142tusk","Puszczykowo","Pyrzyce","Pyskowice","Pyzdry","Rabka-Zdr\xF3j","Raci\u0105\u017C","Racib\xF3rz","Radk\xF3w","Radlin","Rad\u0142\xF3w","Radom","Radomsko","Radomy\u015Bl Wielki","Radymno","Radziej\xF3w","Radzionk\xF3w","Radzymin","Radzy\u0144 Che\u0142mi\u0144ski","Radzy\u0144 Podlaski","Rajgr\xF3d","Rakoniewice","Raszk\xF3w","Rawa Mazowiecka","Rawicz","Recz","Reda","Rejowiec Fabryczny","Resko","Reszel","Rogo\u017Ano","Ropczyce","R\xF3\u017Can","Ruciane-Nida","Ruda \u015Al\u0105ska","Rudnik nad Sanem","Rumia","Rybnik","Rychwa\u0142","Rydu\u0142towy","Rydzyna","Ryglice","Ryki","Ryman\xF3w","Ryn","Rypin","Rzepin","Rzesz\xF3w","Rzg\xF3w","Sandomierz","Sanok","Sejny","Serock","S\u0119dzisz\xF3w","S\u0119dzisz\xF3w Ma\u0142opolski","S\u0119popol","S\u0119p\xF3lno Kraje\u0144skie","Sian\xF3w","Siechnice","Siedlce","Siemianowice \u015Al\u0105skie","Siemiatycze","Sieniawa","Sieradz","Sierak\xF3w","Sierpc","Siewierz","Skalbmierz","Ska\u0142a","Skarszewy","Skaryszew","Skar\u017Cysko-Kamienna","Skawina","Sk\u0119pe","Skierniewice","Skocz\xF3w","Skoki","Sk\xF3rcz","Skwierzyna","S\u0142awa","S\u0142awk\xF3w","S\u0142awno","S\u0142omniki","S\u0142ubice","S\u0142upca","S\u0142upsk","Sob\xF3tka","Sochaczew","Soko\u0142\xF3w Ma\u0142opolski","Soko\u0142\xF3w Podlaski","Sok\xF3\u0142ka","Solec Kujawski","Sompolno","Sopot","Sosnowiec","So\u015Bnicowice","Stalowa Wola","Starachowice","Stargard","Starogard Gda\u0144ski","Stary S\u0105cz","Stasz\xF3w","Stawiski","Stawiszyn","St\u0105pork\xF3w","St\u0119szew","Stoczek \u0141ukowski","Stronie \u015Al\u0105skie","Strumie\u0144","Stryk\xF3w","Strzegom","Strzelce Kraje\u0144skie","Strzelce Opolskie","Strzelin","Strzelno","Strzy\u017C\xF3w","Sucha Beskidzka","Sucha\u0144","Suchedni\xF3w","Suchowola","Sulech\xF3w","Sulej\xF3w","Sulej\xF3wek","Sul\u0119cin","Sulmierzyce","Su\u0142kowice","Supra\u015Bl","Sura\u017C","Susz","Suwa\u0142ki","Swarz\u0119dz","Syc\xF3w","Szadek","Szamocin","Szamotu\u0142y","Szczawnica","Szczawno-Zdr\xF3j","Szczebrzeszyn","Szczecin","Szczecinek","Szczekociny","Szczucin","Szczuczyn","Szczyrk","Szczytna","Szczytno","Szepietowo","Szklarska Por\u0119ba","Szlichtyngowa","Szprotawa","Sztum","Szubin","Szyd\u0142owiec","\u015Acinawa","\u015Alesin","\u015Amigiel","\u015Arem","\u015Aroda \u015Al\u0105ska","\u015Aroda Wielkopolska","\u015Awi\u0105tniki G\xF3rne","\u015Awidnica","\u015Awidnik","\u015Awidwin","\u015Awiebodzice","\u015Awiebodzin","\u015Awiecie","\u015Awierad\xF3w-Zdr\xF3j","\u015Awierzawa","\u015Awi\u0119toch\u0142owice","\u015Awinouj\u015Bcie","Tarczyn","Tarnobrzeg","Tarnogr\xF3d","Tarnowskie G\xF3ry","Tarn\xF3w","Tczew","Terespol","T\u0142uszcz","Tolkmicko","Tomasz\xF3w Lubelski","Tomasz\xF3w Mazowiecki","Toru\u0144","Torzym","Toszek","Trzcianka","Trzciel","Trzci\u0144sko-Zdr\xF3j","Trzebiat\xF3w","Trzebinia","Trzebnica","Trzemeszno","Tuchola","Tuch\xF3w","Tuczno","Tuliszk\xF3w","Turek","Tuszyn","Twardog\xF3ra","Tychowo","Tychy","Tyczyn","Tykocin","Tyszowce","Ujazd","Uj\u015Bcie","Ulan\xF3w","Uniej\xF3w","Ustka","Ustro\u0144","Ustrzyki Dolne","Wadowice","Wa\u0142brzych","Wa\u0142cz","Warka","Warszawa","Warta","Wasilk\xF3w","W\u0105brze\u017Ano","W\u0105chock","W\u0105growiec","W\u0105sosz","Wejherowo","W\u0119gliniec","W\u0119gorzewo","W\u0119gorzyno","W\u0119gr\xF3w","Wi\u0105z\xF3w","Wiele\u0144","Wielichowo","Wieliczka","Wielu\u0144","Wierusz\xF3w","Wi\u0119cbork","Wilamowice","Wis\u0142a","Witkowo","Witnica","Wle\u0144","W\u0142adys\u0142awowo","W\u0142oc\u0142awek","W\u0142odawa","W\u0142oszczowa","Wodzis\u0142aw \u015Al\u0105ski","Wojciesz\xF3w","Wojkowice","Wojnicz","Wolb\xF3rz","Wolbrom","Wolin","Wolsztyn","Wo\u0142czyn","Wo\u0142omin","Wo\u0142\xF3w","Wo\u017Aniki","Wroc\u0142aw","Wronki","Wrze\u015Bnia","Wschowa","Wyrzysk","Wysoka","Wysokie Mazowieckie","Wyszk\xF3w","Wyszogr\xF3d","Wy\u015Bmierzyce","Zab\u0142ud\xF3w","Zabrze","Zag\xF3r\xF3w","Zag\xF3rz","Zakliczyn","Zakopane","Zakroczym","Zalewo","Zambr\xF3w","Zamo\u015B\u0107","Zator","Zawadzkie","Zawichost","Zawid\xF3w","Zawiercie","Z\u0105bki","Z\u0105bkowice \u015Al\u0105skie","Zb\u0105szynek","Zb\u0105szy\u0144","Zduny","Zdu\u0144ska Wola","Zdzieszowice","Zel\xF3w","Zgierz","Zgorzelec","Zielona G\xF3ra","Zielonka","Zi\u0119bice","Z\u0142ocieniec","Z\u0142oczew","Z\u0142otoryja","Z\u0142ot\xF3w","Z\u0142oty Stok","Zwierzyniec","Zwole\u0144","\u017Babno","\u017Baga\u0144","\u017Barki","\u017Bar\xF3w","\u017Bary","\u017Belech\xF3w","\u017Berk\xF3w","\u017Bmigr\xF3d","\u017Bnin","\u017Bory","\u017Bukowo","\u017Buromin","\u017Bychlin","\u017Byrard\xF3w","\u017Bywiec"];var N=["{{location.city_name}}"];var j=["Afganistan","Albania","Algieria","Andora","Angola","Antigua i Barbuda","Arabia Saudyjska","Argentyna","Armenia","Australia","Austria","Azerbejd\u017Can","Bahamy","Bahrajn","Bangladesz","Barbados","Belgia","Belize","Benin","Bhutan","Bia\u0142oru\u015B","Birma","Boliwia","Bo\u015Bnia i Hercegowina","Botswana","Brazylia","Brunei","Bu\u0142garia","Burkina Faso","Burundi","Chile","Chiny","Chorwacja","Cypr","Czad","Czarnog\xF3ra","Czechy","Dania","Demokratyczna Republika Konga","Dominika","Dominikana","D\u017Cibuti","Egipt","Ekwador","Erytrea","Estonia","Eswatini","Etiopia","Fid\u017Ci","Filipiny","Finlandia","Francja","Gabon","Gambia","Ghana","Grecja","Grenada","Gruzja","Gujana","Gwatemala","Gwinea","Gwinea Bissau","Gwinea R\xF3wnikowa","Haiti","Hiszpania","Holandia","Honduras","Indie","Indonezja","Irak","Iran","Irlandia","Islandia","Izrael","Jamajka","Japonia","Jemen","Jordania","Kambod\u017Ca","Kamerun","Kanada","Katar","Kazachstan","Kenia","Kirgistan","Kiribati","Kolumbia","Komory","Kongo","Korea Po\u0142udniowa","Korea P\xF3\u0142nocna","Kostaryka","Kuba","Kuwejt","Laos","Lesotho","Liban","Liberia","Libia","Liechtenstein","Litwa","Luksemburg","\u0141otwa","Macedonia P\xF3\u0142nocna","Madagaskar","Malawi","Malediwy","Malezja","Mali","Malta","Maroko","Mauretania","Mauritius","Meksyk","Mikronezja","Mo\u0142dawia","Monako","Mongolia","Mozambik","Namibia","Nauru","Nepal","Niemcy","Niger","Nigeria","Nikaragua","Norwegia","Nowa Zelandia","Oman","Pakistan","Palau","Panama","Papua-Nowa Gwinea","Paragwaj","Peru","Polska","Portugalia","Republika Po\u0142udniowej Afryki","Republika \u015Arodkowoafryka\u0144ska","Republika Zielonego Przyl\u0105dka","Rosja","Rumunia","Rwanda","Saint Kitts i Nevis","Saint Lucia","Saint Vincent i Grenadyny","Salwador","Samoa","San Marino","Senegal","Serbia","Seszele","Sierra Leone","Singapur","S\u0142owacja","S\u0142owenia","Somalia","Sri Lanka","Stany Zjednoczone","Sudan","Sudan Po\u0142udniowy","Surinam","Syria","Szwajcaria","Szwecja","Tad\u017Cykistan","Tajlandia","Tanzania","Timor Wschodni","Togo","Tonga","Trynidad i Tobago","Tunezja","Turcja","Turkmenistan","Tuvalu","Uganda","Ukraina","Urugwaj","Uzbekistan","Vanuatu","Watykan","Wenezuela","W\u0119gry","Wielka Brytania","Wietnam","W\u0142ochy","Wybrze\u017Ce Ko\u015Bci S\u0142oniowej","Wyspy Marshalla","Wyspy Salomona","Wyspy \u015Awi\u0119tego Tomasza i Ksi\u0105\u017C\u0119ca","Zambia","Zimbabwe","Zjednoczone Emiraty Arabskie"];var h={cardinal:["p\xF3\u0142noc","wsch\xF3d","po\u0142udnie","zach\xF3d"],cardinal_abbr:["pn.","wsch.","pd.","zach."],ordinal:["p\xF3\u0142nocny wsch\xF3d","po\u0142udniowy wsch\xF3d","po\u0142udniowy zach\xF3d","p\xF3\u0142nocny zach\xF3d"],ordinal_abbr:["pn. wsch.","pd. wsch.","pd. zach.","pn. zach."]};var W=["##-###"];var f=["m. ###"];var B=["dolno\u015Bl\u0105skie","kujawsko-pomorskie","lubelskie","lubuskie","\u0142\xF3dzkie","ma\u0142opolskie","mazowieckie","opolskie","podkarpackie","podlaskie","pomorskie","\u015Bl\u0105skie","\u015Bwi\u0119tokrzyskie","warmi\u0144sko-mazurskie","wielkopolskie","zachodniopomorskie"];var C=["DS","KP","LU","LB","LD","MA","MZ","OP","PK","PD","PM","SL","SK","WN","WP","ZP"];var D={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}} {{location.secondaryAddress}}"};var G=["{{location.street_prefix}} {{person.last_name.generic}}"];var T=["al.","bulw.","droga","ogr\xF3d","os.","park","pl.","rondo","rynek","skwer","szosa","ul.","wyb.","wyspa"];var ya={building_number:P,city_name:M,city_pattern:N,country:j,direction:h,postcode:W,secondary_address:f,state:B,state_abbr:C,street_address:D,street_pattern:G,street_prefix:T},A=ya;var L=["alias","consequatur","aut","perferendis","sit","voluptatem","accusantium","doloremque","aperiam","eaque","ipsa","quae","ab","illo","inventore","veritatis","et","quasi","architecto","beatae","vitae","dicta","sunt","explicabo","aspernatur","odit","fugit","sed","quia","consequuntur","magni","dolores","eos","qui","ratione","sequi","nesciunt","neque","dolorem","ipsum","dolor","amet","consectetur","adipisci","velit","non","numquam","eius","modi","tempora","incidunt","ut","labore","dolore","magnam","aliquam","quaerat","enim","ad","minima","veniam","quis","nostrum","exercitationem","ullam","corporis","nemo","ipsam","voluptas","suscipit","laboriosam","nisi","aliquid","ex","ea","commodi","autem","vel","eum","iure","reprehenderit","in","voluptate","esse","quam","nihil","molestiae","iusto","odio","dignissimos","ducimus","blanditiis","praesentium","laudantium","totam","rem","voluptatum","deleniti","atque","corrupti","quos","quas","molestias","excepturi","sint","occaecati","cupiditate","provident","perspiciatis","unde","omnis","iste","natus","error","similique","culpa","officia","deserunt","mollitia","animi","id","est","laborum","dolorum","fuga","harum","quidem","rerum","facilis","expedita","distinctio","nam","libero","tempore","cum","soluta","nobis","eligendi","optio","cumque","impedit","quo","porro","quisquam","minus","quod","maxime","placeat","facere","possimus","assumenda","repellendus","temporibus","quibusdam","illum","fugiat","nulla","pariatur","at","vero","accusamus","officiis","debitis","necessitatibus","saepe","eveniet","voluptates","repudiandae","recusandae","itaque","earum","hic","tenetur","a","sapiente","delectus","reiciendis","voluptatibus","maiores","doloribus","asperiores","repellat"];var ua={word:L},R=ua;var wa={title:"Polish",code:"pl",language:"pl",endonym:"Polski",dir:"ltr",script:"Latn"},J=wa;var Z=["Alternatywna","Blues","Country","Disco polo","Elektroniczna","Funk","Hip Hop","Indie Pop","Jazz","Klasyczna","Latynoska","Ludowa","Metal","Pop","Rap","Reggae","Rock","Soul"];var O=["12 Groszy","24.11.94","51","A To Co Mam","Adriatyk, Ocean Gor\u0105cy","Ale Jestem","Aleja Gwiazd","Arahja","Autobiografia","Autystyczny","Bab\u0119 Zes\u0142a\u0142 B\xF3g","Ballada 07","Baranek","Beksa","Bema Pami\u0119ci \u017Ba\u0142obny - Rapsod","Berlin Zachodni","Bia\u0142a Armia","Bia\u0142a Flaga","Bia\u0142y Krzy\u017C","Biegnij Dalej Sam","Bieszczadzkie Anio\u0142y","Bo Jeste\u015B Ty","Bo Jo Cie Kochom","Bombonierka","Boskie Buenos","Bracka","Buka","Butelki Z Benzyn\u0105 I Kamienie","By\u0142am R\xF3\u017C\u0105","By\u0142a\u015B Serca Biciem","B\xF3g","C'Est La Vie - Pary\u017C Z Poczt\xF3wki","Celina","Chcemy By\u0107 Sob\u0105","Chcia\u0142bym Umrze\u0107 Z Mi\u0142o\u015Bci","Chcia\u0142em By\u0107","Chod\u017A, Pomaluj M\xF3j \u015Awiat","Ch\u0142opcy","Ciche Dni","Cichosza","Ciep\u0142y Wiatr","Cie\u0144 Wielkiej G\xF3ry","Ci\u0105gnik","Co Mi Panie Dasz","Cud Niepami\u0119ci","Cykady Na Cykladach","Czarne S\u0142o\u0144ca","Czarny Blues O Czwartej Nad Ranem","Czas Nas Uczy Pogody","Czas O\u0142owiu","Czerwony Jak Ceg\u0142a","Cztery Pokoje","Cz\u0142owiek Z Li\u015Bciem","Deszcz","Deszcz Na Betonie","Deszcz W Cisnej","Dla Ciebie","Dmuchawce, Latawce, Wiatr","Dni, Kt\xF3rych Nie Znamy","Do Ani","Do Ko\u0142yski","Do Prostego Cz\u0142owieka","Dom","Doros\u0142e Dzieci","Dwa Ognie","Dwa Serca, Dwa Smutki","Dzieci","Dziewczyna Bez Z\u0119ba Na Przedzie","Dzie\u0144 Dobry, Kocham Ci\u0119","Dziwny Jest Ten \u015Awiat","Dzi\u015B P\xF3\u017Ano P\xF3jd\u0119 Spa\u0107","D\u0142ugo\u015B\u0107 D\u017Awi\u0119ku Samotno\u015Bci","Elektryczny","Eli Lama Sabachtani","Ezoteryczny Pozna\u0144","Filandia","Film","Flota Zjednoczonych Si\u0142","Fotograf Brok","Gdy Nie Ma Dzieci W Domu","Gdybym","Go\u0142\u0119bi Puch","Granda","Grande Valse Brillante","Gra\u017Cka","Groszki I R\xF3\u017Ce","Harry","Hej Wy","Hej, Czy Nie Wiecie","Hi-Fi","Horses","Hydropiek\u0142owst\u0105pienie","I Ciebie Te\u017C, Bardzo","I Nikomu Nie Wolno Si\u0119 Z Tego \u015Amia\u0107","I Tak Warto \u017By\u0107","I Wszystko Si\u0119 Mo\u017Ce Zdarzy\u0107","IV Liceum","Ja Sowa","Ja Wysiadam","Jaka R\xF3\u017Ca, Taki Cier\u0144","Jask\xF3\u0142ka Uwi\u0119ziona","Jednego Serca","Jedwab","Jedyne Co Mam","Jej Portret","Jenny","Jest Taki Samotny Dom","Jestem Bogiem","Jestem W Niebie","Jeste\u015B Lekiem Na Ca\u0142e Z\u0142o","Jeste\u015Bmy Na Wczasach","Jeszcze B\u0119dzie Przepi\u0119knie","Jeszcze Si\u0119 Tam \u017Bagiel Bieli","Jeszcze W Zielone Gramy","Jezu Jak Si\u0119 Ciesz\u0119","Je\u015Bli Wiesz Co Chc\u0119 Powiedzie\u0107","Jolka, Jolka, Pami\u0119tasz","Jutro Mo\u017Cemy By\u0107 Szcz\u0119\u015Bliwi","Kapitan Polska","Karuzela Z Madonnami","Karwoski","Kawa\u0142ek Pod\u0142ogi","Kiedy By\u0142em Ma\u0142ym Ch\u0142opcem","Kiedy Powiem Sobie Do\u015B\u0107","King","King Bruce Lee Karate Mistrz","Klub Weso\u0142ego Szampana","Kobiety Jak Te Kwiaty","Kocham By\u0107 Z Tob\u0105","Kocham Ci\u0119","Kocham Ci\u0119 Jak Irlandi\u0119","Kocham Ci\u0119 Kochanie Moje","Kocham Wolno\u015B\u0107","Kochana","Kocha\u0107 Inaczej","Koncert Jesienny Na Dwa \u015Awierszcze I Wiatr W Kominie","Konstytucje","Korow\xF3d","Kosmiczne Energie","Ko\u0142ysanka Dla Nieznajomej","Krakowski Spleen","Krak\xF3w","Krew Boga","Kryzysowa Narzeczona","Kwiaty We W\u0142osach","Lamparty","Leszek \u017Bukowski","Lewe Lewe Loff","Lipstick On The Glass","List","List Do M.","Los, Cebula I Krokodyle \u0141zy","Lubi\u0119 M\xF3wi\u0107 Z Tob\u0105","Lubi\u0119 Wraca\u0107 Tam Gdzie By\u0142em","Lucciola","Lucy Phere","Malcziki","Marchewkowe Pole","Maszynka Do \u015Awierkania","Ma\u0142e T\u0119sknoty","Ma\u0142omiasteczkowy","Meluzyna","Mimo Wszystko","Mississippi W Ogniu","Mi\u0119dzy Cisz\u0105 A Cisz\u0105","Mi\u0142o\u015B\u0107, Mi\u0142o\u015B\u0107","Mniej Ni\u017C Zero","Modlitwa","Modlitwa III - Pozw\xF3l Mi","Modlitwa O Wschodzie S\u0142o\u0144ca","Mog\u0142o By\u0107 Nic","Moja I Twoja Nadzieja","Moja Krew","Moje Bieszczady","Mury","M\xF3j Dom","M\xF3wi\u0119 Ci \u017Be","Na Falochronie","Na Szczycie","Na Zakr\u0119cie","Nadzieja","Naprawd\u0119 Nie Dzieje Si\u0119 Nic","Nast\u0119pna Stacja","Nasza Klasa","Nic Nie Mo\u017Ce Wiecznie Trwa\u0107","Nie Ma, Nie Ma Ciebie","Nie Mam Dla Ciebie Mi\u0142o\u015Bci","Nie Przeno\u015Bcie Nam Stolicy Do Krakowa","Nie Pytaj O Polsk\u0119","Nie P\u0142acz Ewka","Nie Raj","Nie Wierz Nigdy Kobiecie","Niebo By\u0142o R\xF3\u017Cowe","Niech \u017Byje Bal","Niemi\u0142o\u015B\u0107","Niepokonani","Niewiele Ci Mog\u0119 Da\u0107","Nieznajomy","Niezwyci\u0119\u017Cony","Nikt Tak Pi\u0119knie Nie M\xF3wi\u0142, \u017Be Si\u0119 Boi Mi\u0142o\u015Bci","Nim Stanie Si\u0119 Tak, Jak Gdyby Nigdy Nic","Nim Wstanie Dzie\u0144","Noc Komety","N\xF3\u017C","O! Ela","Objazdowe Nieme Kino","Obud\u017A Si\u0119","Ob\u0142awa","Ocali\u0107 Od Zapomnienia","Och \u017Bycie, Kocham Ci\u0119 Nad \u017Bycie","Oczy Tej Ma\u0142ej","Odchodz\u0105c","Odkryjemy Mi\u0142o\u015B\u0107 Nieznan\u0105","Ok, Boomer!","Oni Zaraz Przyjd\u0105 Tu","Orkiestra","Ostatni","Pami\u0119tajcie O Ogrodach","Papierowy Ksi\u0119\u017Cyc","Partyzant","Pary\u017C - Moskwa 17.15","Paw","Peggy Brown","Piosenka Jest Dobra Na Wszystko","Piosenka Ksi\u0119\u017Cycowa","Piosenka M\u0142odych Wio\u015Blarzy","Pi\u0142a Tango","Pi\u0142em W Spale, Spa\u0142em W Pile","Plamy Na S\u0142o\u0144cu","Plus I Minus","Po Co Wolno\u015B\u0107","Po Prostu B\u0105d\u017A","Pocz\u0105tek","Pod Niebem","Pod Papugami","Pod Pr\u0105d","Podaruj Mi Troch\u0119 S\u0142o\u0144ca","Pola","Polska","Polski","Polsko","Przebudzenie","Prze\u017Cyj To Sam","Pr\xF3cz Ciebie Nic","Psalm Stoj\u0105cych W Kolejce","P\u0142on\u0105 G\xF3ry, P\u0142on\u0105 Lasy","Raz Na Milion Lat","Rosemary'S Baby","Rower","Rzu\u0107 To Wszystko Co Z\u0142e","R\xF3bmy Swoje","Samba Przed Rozstaniem","Scenariusz Dla Moich S\u0105siad\xF3w","Sen","Sen O Dolinie","Sen O Victorii","Sen O Warszawie","Serce To Jest Muzyk","Sie \u015Aciemnia","Skazany Na Bluesa","Sk\xF3ra","Sk\u0142ama\u0142am","Sobie I Wam","Son Of The Blue Sky","Sorry Polsko","Spadam","Spalam Si\u0119","Spytaj Milicjanta","Stare Drzewa","Statki Na Niebie","Strze\u017C Si\u0119 Tych Miejsc","Syreny","Szaror\xF3\u017Cowe","Sza\u0142 Niebieskich Cia\u0142","Szklana Pogoda","Sztuka Latania","S\u0142odkiego Mi\u0142ego \u017Bycia","Ta Noc Do Innych Jest Niepodobna","Tak Mi Si\u0119 Nie Chce","Tak, Tak... To Ja","Tam, Gdzie Nie Si\u0119ga Wzrok","Taniec Eleny","Te Smaki I Zapachy","Teksa\u0144ski","Telefony",'Temat Serialu "Polskie Drogi"',"The Depth Of Self-Delusion","To Co Czujesz, To Co Wiesz","To Ostatnia Niedziela","To Tylko Tango","To Wychowanie","Tolerancja / Na Mi\u0142y B\xF3g","Transmission Into Your Heart","Trudne \u017Byczenia","Trudno Nie Wierzy\u0107 W Nic","Trudno Tak (Razem By\u0107 Nam Ze Sob\u0105...)","Trzy Zapa\u0142ki","Tr\xF3jk\u0105ty I Kwadraty","Tu\u017C Przed P\xF3\u0142noc\u0105","Twoja Lorelei","Tw\xF3j B\xF3l Jest Lepszy Ni\u017C M\xF3j","Tyle S\u0142o\u0144ca W Ca\u0142ym Mie\u015Bcie","Ucieczka Z Tropiku","Uciekaj Moje Serce","Varsovie","W Deszczu Male\u0144kich \u017B\xF3\u0142tych Kwiat\xF3w","W Dobr\u0105 Stron\u0119","W Domach Z Betonu Nie Ma Wolnej Mi\u0142o\u015Bci","W Moim Magicznym Domu","W Moim Ogrodzie","W Wielkim Mie\u015Bcie","W \u017B\xF3\u0142tych P\u0142omieniach Li\u015Bci","Warszawa","Wataha","Wci\u0105\u017C Bardziej Obcy","Wehiku\u0142 Czasu","Whisky","Wie\u017Ca Rado\u015Bci, Wie\u017Ca Samotno\u015Bci","Wind\u0105 Do Nieba","Wiosna, Ach To Ty","Wi\u015Bnia","Wodymidaj","Wojenka","Wspomnienie","Wszystko Czego Dzi\u015B Chc\u0119","Wyj\u0105tkowo Zimny Maj","Wymy\u015Bli\u0142em Ciebie","Wyspa, Drzewo, Zamek","W\u0142adza","Z Imbirem","Z Nim B\u0119dziesz Szcz\u0119\u015Bliwsza","Z Tob\u0105 / Do Domu","Z Tob\u0105 Chc\u0119 Ogl\u0105da\u0107 \u015Awiat","Za Ostatni Grosz","Zacznij Od Bacha","Zanim P\xF3jd\u0119","Zanim Zrozumiesz","Zaopiekuj Si\u0119 Mn\u0105","Zaprzepaszczone Si\u0142y Wielkiej Armii \u015Awi\u0119tych Znak\xF3w","Zapytaj Mnie Czy Ci\u0119 Kocham","Zawsze Tam Gdzie Ty","Zazdro\u015B\u0107","Za\u0142oga G","Zegarmistrz \u015Awiat\u0142a","Zn\xF3w W\u0119drujemy","Zosta\u0144","[Sic!]","\u0141za Dla Cieni\xF3w Minionych","\u015Alady","\u015Amier\u0107 W Bikini","\u015Apiewa\u0107 Ka\u017Cdy Mo\u017Ce","\u015Apij Kochanie, \u015Apij","\u015Awiecie Nasz","\u017Byj\u0119 W Kraju"];var da={genre:Z,song_name:O},I=da;var E={generic:["Aaron","Abraham","Ada","Adam","Adelajda","Adrian","Agata","Agaton","Agnieszka","Agrypina","Aida","Alan","Albert","Aleksander","Aleksandra","Aleksy","Alfred","Alicja","Alina","Alwar","Amabela","Amanda","Ambro\u017Cy","Anastazja","Anatol","Andrzej","And\u017Celika","Angela","Angelina","Anna","Ansgary","Antoni","Antonina","Apollinary","Apollo","Archibald","Ariadna","Arkadiusz","Arkady","Arnold","Arseniusz","Artur","Arystarch","Atanazy","August","Aurora","Baldwin","Barbara","Bazyli","Beatrycze","Benedykt","Beniamin","Bernard","Berta","Bertram","Bertrand","Bibiana","Bibianna","Borys","Brajan","Bruno","Brygida","B\u0142a\u017Cej","Cecylia","Cecyliusz","Cezary","Chloe","Cyra","Cyrus","Cyryl","Damian","Daniel","Daria","Dariusz","Dawid","Demetriusz","Diana","Dina","Dionizy","Dominik","Donald","Dorian","Dorota","Edgar","Edmund","Edward","Edwin","Edyta","Efraim","Efrem","Eleazar","Eleonora","Eliasz","Eliza","Elwira","El\u017Cbieta","Emanuel","Emil","Emilia","Erast","Ernest","Erwin","Estera","Eudokia","Eudoksja","Eugenia","Eugeniusz","Eustracjusz","Ewa","Ewelina","Fabian","Feliks","Ferdynanda","Filemon","Filip","Filipa","Florencja","Florian","Franciszek","Franciszka","Fryderyk","Gabriel","Gabriela","Galfryd","Gedeon","Gerald","Gerazym","Gertruda","Gilbert","Ginewra","Gloria","Gonsalwy","Gracja","Greta","Grzegorz","Gwido","Hanna","Harald","Helena","Henryk","Henryka","Herbert","Herman","Hieronim","Hilarion","Hilary","Hipolit","Horacy","Hubert","Hugo","Ida","Ignacy","Igor","Ilona","Innocenty","Irena","Ireneusz","Irma","Izaak","Izabela","Izajasz","Izolda","Izydor","Jadwiga","Jakub","Jakubina","Jan","Janina","Janusz","Jeremi","Jeremiasz","Jerzy","Joachim","Joanna","Jonatan","Jozue","Judyta","Julia","Julian","Julita","Juliusz","Justyn","Justyna","J\xF3zef","J\xF3zefina","Kalistrat","Kamila","Karol","Karolina","Katarzyna","Kazimierz","Kira","Klara","Klarencjusz","Klaudia","Klaudiusz","Klemens","Klementyna","Kleopatra","Konrad","Konstancja","Konstanty","Konstantyn","Koralia","Kornel","Korneli","Kornelia","Korneliusz","Krystian","Krystyna","Krzysztof","Ksawera","Ksawery","Ksenia","Ksenofont","Kwintyn","Larysa","Laura","Laurencja","Laurencjusz","Laurenty","Laurentyna","Lea","Leila","Leon","Leonard","Leonid","Lidia","Lilia","Liliana","Lilianna","Lilla","Liza","Lucja","Lucjan","Ludwik","Ludwika","Luiza","Magdalena","Magnus","Maja","Makary","Maksym","Maksymilian","Malwina","Marceli","Marcin","Marek","Maria","Marianna","Marta","Martyna","Maryna","Mateusz","Matylda","Maurycy","Ma\u0142gorzata","Melania","Michalina","Micha\u0142","Miko\u0142aj","Miron","Modest","Moj\u017Cesz","Monika","Nadzieja","Natalia","Natan","Natanael","Nazariusz","Nazary","Nestor","Nikodem","Nikola","Nina","Noemi","Ofelia","Olaf","Oleg","Olga","Olimpia","Oliwia","Oliwier","Onufry","Orestes","Oskar","Osmund","Pankracy","Pantaleon","Patrycja","Patrycjusz","Patrycy","Patryk","Paula","Paulina","Pawe\u0142","Pelagia","Penelopa","Piotr","Platon","Polikarp","Porfiriusz","Porfiry","Prokles","Prokop","Prokul","Rachela","Rafa\u0142","Rajmund","Rajnold","Randolf","Rebeka","Regina","Reginald","Renata","Robert","Roderyk","Roger","Roksana","Roland","Roman","Romeo","Rozalia","Rudolf","Rufina","Ruta","Ryszard","R\xF3\u017Ca","Sabina","Salomon","Salwator","Samson","Samuel","Sara","Sebastian","Serafin","Serafina","Sergiusz","Seweryn","Spirydion","Stanis\u0142aw","Stefan","Stefania","Stella","Sybilla","Sylwester","Sylwia","Szczepan","Szymon","Tacjana","Tamara","Tekla","Teodor","Teodora","Terencjusz","Teresa","Tobiasz","Tomasz","Tymoteusz","Wac\u0142aw","Walenty","Walentyn","Walentyna","Waleria","Walerian","Walery","Walgierz","Walter","Wanda","Wanesa","Wawrzyniec","Weronika","Wiara","Wiktor","Wiktoria","Wilfryd","Wilhelm","Wilhelmina","Wincenty","Wirginia","Witalis","W\u0142adys\u0142aw","W\u0142odzimierz","Zachariasz","Zachary","Zoe","Zofia","Zuzanna","Zygmunt","\u0141azarz","\u0141ucja","\u0141ukasz","\u017Baneta"],female:["Ada","Adelajda","Agata","Agnieszka","Agrypina","Aida","Aleksandra","Alicja","Alina","Amabela","Amanda","Anastazja","And\u017Celika","Angela","Angelina","Anna","Antonina","Ariadna","Aurora","Barbara","Beatrycze","Berta","Bibiana","Bibianna","Brygida","Cecylia","Chloe","Cyra","Daria","Diana","Dina","Dorota","Edyta","Eleonora","Eliza","Elwira","El\u017Cbieta","Emilia","Estera","Eudokia","Eudoksja","Eugenia","Ewa","Ewelina","Ferdynanda","Filipa","Florencja","Franciszka","Gabriela","Gertruda","Ginewra","Gloria","Gracja","Greta","Hanna","Helena","Henryka","Ida","Ilona","Irena","Irma","Izabela","Izolda","Jadwiga","Jakubina","Janina","Joanna","Judyta","Julia","Julita","Justyna","J\xF3zefina","Kamila","Karolina","Katarzyna","Kira","Klara","Klaudia","Klementyna","Kleopatra","Konstancja","Koralia","Kornelia","Krystyna","Ksawera","Ksenia","Larysa","Laura","Laurencja","Laurentyna","Lea","Leila","Lidia","Lilia","Liliana","Lilianna","Lilla","Liza","Lucja","Ludwika","Luiza","Magdalena","Maja","Malwina","Maria","Marianna","Marta","Martyna","Maryna","Matylda","Ma\u0142gorzata","Melania","Michalina","Monika","Nadzieja","Natalia","Nikola","Nina","Noemi","Ofelia","Olga","Olimpia","Oliwia","Patrycja","Paula","Paulina","Pelagia","Penelopa","Rachela","Rebeka","Regina","Renata","Roksana","Rozalia","Rufina","Ruta","R\xF3\u017Ca","Sabina","Sara","Serafina","Stefania","Stella","Sybilla","Sylwia","Tacjana","Tamara","Tekla","Teodora","Teresa","Walentyna","Waleria","Wanda","Wanesa","Weronika","Wiara","Wiktoria","Wilhelmina","Wirginia","Zoe","Zofia","Zuzanna","\u0141ucja","\u017Baneta"],male:["Aaron","Abraham","Adam","Adrian","Agaton","Alan","Albert","Aleksander","Aleksy","Alfred","Alwar","Ambro\u017Cy","Anatol","Andrzej","Ansgary","Antoni","Apollinary","Apollo","Archibald","Arkadiusz","Arkady","Arnold","Arseniusz","Artur","Arystarch","Atanazy","August","Baldwin","Bazyli","Benedykt","Beniamin","Bernard","Bertram","Bertrand","Borys","Brajan","Bruno","B\u0142a\u017Cej","Cecyliusz","Cezary","Cyrus","Cyryl","Damian","Daniel","Dariusz","Dawid","Demetriusz","Dionizy","Dominik","Donald","Dorian","Edgar","Edmund","Edward","Edwin","Efraim","Efrem","Eleazar","Eliasz","Emanuel","Emil","Erast","Ernest","Erwin","Eugeniusz","Eustracjusz","Fabian","Feliks","Filemon","Filip","Florian","Franciszek","Fryderyk","Gabriel","Galfryd","Gedeon","Gerald","Gerazym","Gilbert","Gonsalwy","Grzegorz","Gwido","Harald","Henryk","Herbert","Herman","Hieronim","Hilarion","Hilary","Hipolit","Horacy","Hubert","Hugo","Ignacy","Igor","Innocenty","Ireneusz","Izaak","Izajasz","Izydor","Jakub","Jan","Janusz","Jeremi","Jeremiasz","Jerzy","Joachim","Jonatan","Jozue","Julian","Juliusz","Justyn","J\xF3zef","Kalistrat","Karol","Kazimierz","Klarencjusz","Klaudiusz","Klemens","Konrad","Konstanty","Konstantyn","Kornel","Korneli","Korneliusz","Krystian","Krzysztof","Ksawery","Ksenofont","Kwintyn","Laurencjusz","Laurenty","Leon","Leonard","Leonid","Lucjan","Ludwik","Magnus","Makary","Maksym","Maksymilian","Marceli","Marcin","Marek","Mateusz","Maurycy","Micha\u0142","Miko\u0142aj","Miron","Modest","Moj\u017Cesz","Natan","Natanael","Nazariusz","Nazary","Nestor","Nikodem","Olaf","Oleg","Oliwier","Onufry","Orestes","Oskar","Osmund","Pankracy","Pantaleon","Patrycjusz","Patrycy","Patryk","Pawe\u0142","Piotr","Platon","Polikarp","Porfiriusz","Porfiry","Prokles","Prokop","Prokul","Rafa\u0142","Rajmund","Rajnold","Randolf","Reginald","Robert","Roderyk","Roger","Roland","Roman","Romeo","Rudolf","Ryszard","Salomon","Salwator","Samson","Samuel","Sebastian","Serafin","Sergiusz","Seweryn","Spirydion","Stanis\u0142aw","Stefan","Sylwester","Szczepan","Szymon","Teodor","Terencjusz","Tobiasz","Tomasz","Tymoteusz","Wac\u0142aw","Walenty","Walentyn","Walerian","Walery","Walgierz","Walter","Wawrzyniec","Wiktor","Wilfryd","Wilhelm","Wincenty","Witalis","W\u0142adys\u0142aw","W\u0142odzimierz","Zachariasz","Zachary","Zygmunt","\u0141azarz","\u0141ukasz"]};var x=["Genderqueer","Hermafrodyta","Inna","Kobieta cis","Kobieta cisp\u0142ciowa","Kobieta interp\u0142ciowa","Kobieta trans","Kobieta","Me\u017Cczyzna trans","M\u0119\u017Cczyzna cis","M\u0119\u017Cczyzna cisp\u0142ciowy","M\u0119\u017Cczyzna interp\u0142ciowy","M\u0119\u017Cczyzna","Osoba agender","Osoba bigender","Osoba gender fluid","Osoba interp\u0142ciowa","Osoba niebinarna","Osoba pangender","Osoba polygender","Osoba trans","Transkobieta","Transsekualista","Transm\u0119\u017Cczyzna"];var F={generic:["Adamczak","Adamczyk","Adamek","Adamiak","Adamiec","Adamowicz","Adamski","Adamus","Aleksandrowicz","Andrzejczak","Andrzejewski","Antczak","Augustyn","Augustyniak","Bagi\u0144ski","Balcerzak","Banach","Banasiak","Banasik","Bana\u015B","Baran","Baranowski","Bara\u0144ski","Bartczak","Bartkowiak","Bartnik","Bartosik","Bednarczyk","Bednarek","Bednarski","Bednarz","Bia\u0142as","Bia\u0142ek","Bia\u0142kowski","Biedrzy\u0144ski","Bielak","Bielawski","Bielecki","Bielski","Bieniek","Biernacki","Biernat","Bie\u0144kowski","Bilski","Bober","Bochenek","Bogucki","Bogusz","Borek","Borkowski","Borowiec","Borowski","Bo\u017Cek","Broda","Brzezi\u0144ski","Brzozowski","Buczek","Buczkowski","Buczy\u0144ski","Budzi\u0144ski","Budzy\u0144ski","Bujak","Bukowski","Burzy\u0144ski","B\u0105k","B\u0105kowski","B\u0142aszczak","B\u0142aszczyk","Cebula","Chmiel","Chmielewski","Chmura","Chojnacki","Chojnowski","Cholewa","Chrzanowski","Chudzik","Cichocki","Cicho\u0144","Cichy","Ciesielski","Cie\u015Bla","Cie\u015Blak","Cie\u015Blik","Ciszewski","Cybulski","Cygan","Czaja","Czajka","Czajkowski","Czapla","Czarnecki","Czech","Czechowski","Czekaj","Czerniak","Czerwi\u0144ski","Czy\u017C","Czy\u017Cewski","Dec","Dobosz","Dobrowolski","Dobrzy\u0144ski","Domaga\u0142a","Doma\u0144ski","Dominiak","Drabik","Drozd","Drozdowski","Drzewiecki","Dr\xF3\u017Cd\u017C","Dubiel","Duda","Dudek","Dudziak","Dudzik","Dudzi\u0144ski","Duszy\u0144ski","Dziedzic","Dziuba","D\u0105bek","D\u0105bkowski","D\u0105browski","D\u0119bowski","D\u0119bski","D\u0142ugosz","Falkowski","Fija\u0142kowski","Filipek","Filipiak","Filipowicz","Flak","Flis","Florczak","Florek","Frankowski","Fr\u0105ckowiak","Fr\u0105czek","Fr\u0105tczak","Furman","Gadomski","Gajda","Gajewski","Gawe\u0142","Gawlik","Gawron","Gawro\u0144ski","Ga\u0142ka","Ga\u0142\u0105zka","Gil","Godlewski","Golec","Go\u0142\u0105b","Go\u0142\u0119biewski","Go\u0142\u0119biowski","Grabowski","Graczyk","Grochowski","Grudzie\u0144","Gruszczy\u0144ski","Gruszka","Grzegorczyk","Grzelak","Grzesiak","Grzesik","Grze\u015Bkowiak","Grzyb","Grzybowski","Grzywacz","Gutowski","Guzik","Gwo\u017Adzik","Gw\xF3\u017Ad\u017A","G\xF3ra","G\xF3ral","G\xF3recki","G\xF3rka","G\xF3rniak","G\xF3rny","G\xF3rski","G\u0105sior","G\u0105siorowski","G\u0142ogowski","G\u0142owacki","G\u0142\u0105b","Hajduk","Herman","Iwanowski","Iwa\u0144ski","Izdebski","Jab\u0142o\u0144ski","Jackowski","Jagielski","Jagie\u0142\u0142o","Jagodzi\u0144ski","Jakubiak","Jakubowski","Janas","Janiak","Janicki","Janik","Janiszewski","Jankowiak","Jankowski","Janowski","Janus","Janusz","Januszewski","Jaros","Jarosz","Jarz\u0105bek","Jasi\u0144ski","Jastrz\u0119bski","Jaworski","Ja\u015Bkiewicz","Jezierski","Jurek","Jurkiewicz","Jurkowski","Juszczak","J\xF3\u017Awiak","J\xF3\u017Awik","J\u0119drzejczak","J\u0119drzejczyk","J\u0119drzejewski","Kacprzak","Kaczmarczyk","Kaczmarek","Kaczmarski","Kaczor","Kaczorowski","Kaczy\u0144ski","Kaleta","Kalinowski","Kalisz","Kami\u0144ski","Kania","Kaniewski","Kapusta","Kara\u015B","Karczewski","Karpi\u0144ski","Karwowski","Kasperek","Kasprzak","Kasprzyk","Kaszuba","Kawa","Kawecki","Ka\u0142u\u017Ca","Ka\u017Amierczak","Kie\u0142basa","Kisiel","Kita","Klimczak","Klimek","Kmiecik","Kmie\u0107","Knapik","Kobus","Kogut","Kolasa","Komorowski","Konieczna","Konieczny","Konopka","Kopczy\u0144ski","Koper","Kope\u0107","Korzeniowski","Kos","Kosi\u0144ski","Kosowski","Kostecki","Kostrzewa","Kot","Kotowski","Kowal","Kowalczuk","Kowalczyk","Kowalewski","Kowalik","Kowalski","Koza","Kozak","Kozie\u0142","Kozio\u0142","Koz\u0142owski","Ko\u0142akowski","Ko\u0142odziej","Ko\u0142odziejczyk","Ko\u0142odziejski","Krajewski","Krakowiak","Krawczyk","Krawiec","Kruk","Krukowski","Krupa","Krupi\u0144ski","Kruszewski","Krysiak","Krzemi\u0144ski","Krzy\u017Canowski","Kr\xF3l","Kr\xF3likowski","Ksi\u0105\u017Cek","Kubacki","Kubiak","Kubica","Kubicki","Kubik","Kuc","Kucharczyk","Kucharski","Kuchta","Kuci\u0144ski","Kuczy\u0144ski","Kujawa","Kujawski","Kula","Kulesza","Kulig","Kulik","Kuli\u0144ski","Kurek","Kurowski","Ku\u015B","Kwa\u015Bniewski","Kwiatkowski","Kwiecie\u0144","Kwieci\u0144ski","K\u0119dzierski","K\u0119dziora","K\u0119pa","K\u0142os","K\u0142osowski","Lach","Laskowski","Lasota","Lech","Lenart","Lesiak","Leszczy\u0144ski","Lewandowski","Lewicki","Le\u015Bniak","Le\u015Bniewski","Lipi\u0144ski","Lipka","Lipski","Lis","Lisiecki","Lisowski","Maciejewski","Maci\u0105g","Mackiewicz","Madej","Maj","Majcher","Majchrzak","Majewski","Majka","Makowski","Malec","Malicki","Malinowski","Maliszewski","Marchewka","Marciniak","Marcinkowski","Marczak","Marek","Markiewicz","Markowski","Marsza\u0142ek","Marzec","Mas\u0142owski","Matusiak","Matuszak","Matuszewski","Matysiak","Mazur","Mazurek","Mazurkiewicz","Ma\u0107kowiak","Ma\u0142ecki","Ma\u0142ek","Ma\u015Blanka","Michalak","Michalczyk","Michalik","Michalski","Micha\u0142ek","Micha\u0142owski","Mielczarek","Mierzejewski","Mika","Mikla\u015B","Miko\u0142ajczak","Miko\u0142ajczyk","Mikulski","Milczarek","Milewski","Miller","Misiak","Misztal","Mi\u015Bkiewicz","Modzelewski","Molenda","Morawski","Motyka","Mroczek","Mroczkowski","Mrozek","Mr\xF3z","Mucha","Murawski","Musia\u0142","Muszy\u0144ski","M\u0142ynarczyk","Napiera\u0142a","Nawrocki","Nawrot","Niedziela","Niedzielski","Nied\u017Awiecki","Niemczyk","Niemiec","Niewiadomski","Noga","Nowacki","Nowaczyk","Nowak","Nowakowski","Nowicki","Nowi\u0144ski","Olczak","Olejniczak","Olejnik","Olszewski","Orzechowski","Or\u0142owski","Osi\u0144ski","Ossowski","Ostrowski","Owczarek","Paczkowski","Paj\u0105k","Paku\u0142a","Palacz","Paluch","Panek","Partyka","Pasternak","Paszkowski","Pawelec","Pawlak","Pawlicki","Pawlik","Pawlikowski","Paw\u0142owski","Pa\u0142ka","Piasecki","Piechota","Pieczek","Piekarski","Pietras","Pietruszka","Pietrzak","Pietrzyk","Pilarski","Pilch","Piotrowicz","Piotrowski","Piwowarczyk","Pi\xF3rkowski","Pi\u0105tek","Pi\u0105tkowski","Pi\u0142at","Pluta","Podg\xF3rski","Polak","Pop\u0142awski","Por\u0119bski","Prokop","Prus","Przybylski","Przybysz","Przyby\u0142","Przyby\u0142a","Ptak","Puchalski","Pytel","P\u0142onka","Raczy\u0144ski","Radecki","Radomski","Rak","Rakowski","Ratajczak","Robak","Rogala","Rogalski","Rogowski","Rojek","Romanowski","Rosa","Rosiak","Rosi\u0144ski","Ruci\u0144ski","Rudnicki","Rudzi\u0144ski","Rudzki","Rusin","Rutkowski","Rybak","Rybarczyk","Rybicki","Rzepka","R\xF3\u017Ca\u0144ski","R\xF3\u017Cycki","Sadowski","Sawicki","Serafin","Siedlecki","Sienkiewicz","Sieradzki","Sikora","Sikorski","Sitek","Siwek","Skalski","Skiba","Skibi\u0144ski","Skoczylas","Skowron","Skowronek","Skowro\u0144ski","Skrzypczak","Skrzypek","Sk\xF3ra","Smoli\u0144ski","Sobczak","Sobczyk","Sobieraj","Sobolewski","Socha","Sochacki","Soko\u0142owski","Sok\xF3\u0142","Sosnowski","Sowa","Sowi\u0144ski","So\u0142tys","So\u0142tysiak","Sroka","Stachowiak","Stachowicz","Stachura","Stachurski","Stanek","Staniszewski","Stanis\u0142awski","Stankiewicz","Stasiak","Staszewski","Stawicki","Stec","Stefaniak","Stefa\u0144ski","Stelmach","Stolarczyk","Stolarski","Strzelczyk","Strzelecki","St\u0119pie\u0144","St\u0119pniak","Surma","Suski","Szafra\u0144ski","Szatkowski","Szczepaniak","Szczepanik","Szczepa\u0144ski","Szczerba","Szcze\u015Bniak","Szczygie\u0142","Szcz\u0119sna","Szcz\u0119sny","Szel\u0105g","Szewczyk","Szostak","Szulc","Szwarc","Szwed","Szyd\u0142owski","Szyma\u0144ski","Szymczak","Szymczyk","Szymkowiak","Szyszka","S\u0142awi\u0144ski","S\u0142owik","S\u0142owi\u0144ski","Tarnowski","Tkaczyk","Tokarski","Tomala","Tomaszewski","Tomczak","Tomczyk","Tomys","Tracz","Trojanowski","Trzci\u0144ski","Trzeciak","Turek","Twardowski","Urban","Urbanek","Urbaniak","Urbanowicz","Urba\u0144czyk","Urba\u0144ski","Walczak","Walkowiak","Warcho\u0142","Wasiak","Wasilewski","Wawrzyniak","Weso\u0142owski","Wieczorek","Wierzbicki","Wilczek","Wilczy\u0144ski","Wilk","Winiarski","Witczak","Witek","Witkowski","Wi\u0105cek","Wi\u0119cek","Wi\u0119ckowski","Wi\u015Bniewski","Wnuk","Wojciechowski","Wojtas","Wojtasik","Wojtczak","Wojtkowiak","Wolak","Woli\u0144ski","Wolny","Wolski","Wo\u015B","Wo\u017Aniak","Wrona","Wro\u0144ski","Wr\xF3bel","Wr\xF3blewski","Wypych","Wysocki","Wyszy\u0144ski","W\xF3jcicki","W\xF3jcik","W\xF3jtowicz","W\u0105sik","W\u0105sowski","W\u0119grzyn","W\u0142odarczyk","W\u0142odarski","Zaborowski","Zab\u0142ocki","Zag\xF3rski","Zaj\u0105c","Zaj\u0105czkowski","Zakrzewski","Zalewski","Zaremba","Zarzycki","Zar\u0119ba","Zawada","Zawadzki","Zdunek","Zieli\u0144ski","Zielonka","Zi\xF3\u0142kowski","Zi\u0119ba","Zi\u0119tek","Zwoli\u0144ski","Zych","Zygmunt","\u0141api\u0144ski","\u0141uczak","\u0141ukasiewicz","\u0141ukasik","\u0141ukaszewski","\u015Aliwa","\u015Aliwi\u0144ski","\u015Alusarczyk","\u015Awiderski","\u015Awierczy\u0144ski","\u015Awi\u0105tek","\u017Bak","\u017Bebrowski","\u017Bmuda","\u017Buk","\u017Bukowski","\u017Burawski","\u017Burek","\u017By\u0142a"]};var H={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var _=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:9}];var q={generic:["Pan","Pani"],female:["Pani"],male:["Pan"]};var v=["kobieta","m\u0119\u017Cczyzna"];var ba={first_name:E,gender:x,last_name:F,last_name_pattern:H,name:_,prefix:q,sex:v},U=ba;var V=["12-###-##-##","13-###-##-##","14-###-##-##","15-###-##-##","16-###-##-##","17-###-##-##","18-###-##-##","22-###-##-##","23-###-##-##","24-###-##-##","25-###-##-##","29-###-##-##","32-###-##-##","33-###-##-##","34-###-##-##","41-###-##-##","42-###-##-##","43-###-##-##","44-###-##-##","46-###-##-##","48-###-##-##","52-###-##-##","54-###-##-##","55-###-##-##","56-###-##-##","58-###-##-##","59-###-##-##","61-###-##-##","62-###-##-##","63-###-##-##","65-###-##-##","67-###-##-##","68-###-##-##","71-###-##-##","74-###-##-##","75-###-##-##","76-###-##-##","77-###-##-##","81-###-##-##","82-###-##-##","83-###-##-##","84-###-##-##","85-###-##-##","86-###-##-##","87-###-##-##","89-###-##-##","91-###-##-##","94-###-##-##","95-###-##-##"];var Y=["+4812#######","+4813#######","+4814#######","+4815#######","+4816#######","+4817#######","+4818#######","+4822#######","+4823#######","+4824#######","+4825#######","+4829#######","+4832#######","+4833#######","+4834#######","+4841#######","+4842#######","+4843#######","+4844#######","+4846#######","+4848#######","+4852#######","+4854#######","+4855#######","+4856#######","+4858#######","+4859#######","+4861#######","+4862#######","+4863#######","+4865#######","+4867#######","+4868#######","+4871#######","+4874#######","+4875#######","+4876#######","+4877#######","+4881#######","+4882#######","+4883#######","+4884#######","+4885#######","+4886#######","+4887#######","+4889#######","+4891#######","+4894#######","+4895#######"];var Q=["12 ### ## ##","13 ### ## ##","14 ### ## ##","15 ### ## ##","16 ### ## ##","17 ### ## ##","18 ### ## ##","22 ### ## ##","23 ### ## ##","24 ### ## ##","25 ### ## ##","29 ### ## ##","32 ### ## ##","33 ### ## ##","34 ### ## ##","41 ### ## ##","42 ### ## ##","43 ### ## ##","44 ### ## ##","46 ### ## ##","48 ### ## ##","52 ### ## ##","54 ### ## ##","55 ### ## ##","56 ### ## ##","58 ### ## ##","59 ### ## ##","61 ### ## ##","62 ### ## ##","63 ### ## ##","65 ### ## ##","67 ### ## ##","68 ### ## ##","71 ### ## ##","74 ### ## ##","75 ### ## ##","76 ### ## ##","77 ### ## ##","81 ### ## ##","82 ### ## ##","83 ### ## ##","84 ### ## ##","85 ### ## ##","86 ### ## ##","87 ### ## ##","89 ### ## ##","91 ### ## ##","94 ### ## ##","95 ### ## ##"];var pa={human:V,international:Y,national:Q},X=pa;var Sa={format:X},$=Sa;var aa=[{symbol:"H",name:"Wod\xF3r",atomicNumber:1},{symbol:"He",name:"Hel",atomicNumber:2},{symbol:"Li",name:"Lit",atomicNumber:3},{symbol:"Be",name:"Beryl",atomicNumber:4},{symbol:"B",name:"Bor",atomicNumber:5},{symbol:"C",name:"W\u0119giel",atomicNumber:6},{symbol:"N",name:"Azot",atomicNumber:7},{symbol:"O",name:"Tlen",atomicNumber:8},{symbol:"F",name:"Fluor",atomicNumber:9},{symbol:"Ne",name:"Neon",atomicNumber:10},{symbol:"Na",name:"S\xF3d",atomicNumber:11},{symbol:"Mg",name:"Magnez",atomicNumber:12},{symbol:"Al",name:"Glin",atomicNumber:13},{symbol:"Si",name:"Krzem",atomicNumber:14},{symbol:"P",name:"Fosfor",atomicNumber:15},{symbol:"S",name:"Siarka",atomicNumber:16},{symbol:"Cl",name:"Chlor",atomicNumber:17},{symbol:"Ar",name:"Argon",atomicNumber:18},{symbol:"K",name:"Potas",atomicNumber:19},{symbol:"Ca",name:"Wap\u0144",atomicNumber:20},{symbol:"Sc",name:"Skand",atomicNumber:21},{symbol:"Ti",name:"Tytan",atomicNumber:22},{symbol:"V",name:"Wanad",atomicNumber:23},{symbol:"Cr",name:"Chrom",atomicNumber:24},{symbol:"Mn",name:"Mangan",atomicNumber:25},{symbol:"Fe",name:"\u017Belazo",atomicNumber:26},{symbol:"Co",name:"Kobalt",atomicNumber:27},{symbol:"Ni",name:"Nikiel",atomicNumber:28},{symbol:"Cu",name:"Mied\u017A",atomicNumber:29},{symbol:"Zn",name:"Cynk",atomicNumber:30},{symbol:"Ga",name:"Gal",atomicNumber:31},{symbol:"Ge",name:"German",atomicNumber:32},{symbol:"As",name:"Arsen",atomicNumber:33},{symbol:"Se",name:"Selen",atomicNumber:34},{symbol:"Br",name:"Brom",atomicNumber:35},{symbol:"Kr",name:"Krypton",atomicNumber:36},{symbol:"Rb",name:"Rubid",atomicNumber:37},{symbol:"Sr",name:"Stront",atomicNumber:38},{symbol:"Y",name:"Itr",atomicNumber:39},{symbol:"Zr",name:"Cyrkon",atomicNumber:40},{symbol:"Nb",name:"Niob",atomicNumber:41},{symbol:"Mo",name:"Molibden",atomicNumber:42},{symbol:"Tc",name:"Technet",atomicNumber:43},{symbol:"Ru",name:"Ruten",atomicNumber:44},{symbol:"Rh",name:"Rod",atomicNumber:45},{symbol:"Pd",name:"Pallad",atomicNumber:46},{symbol:"Ag",name:"Srebro",atomicNumber:47},{symbol:"Cd",name:"Kadm",atomicNumber:48},{symbol:"In",name:"Ind",atomicNumber:49},{symbol:"Sn",name:"Cyna",atomicNumber:50},{symbol:"Sb",name:"Antymon",atomicNumber:51},{symbol:"Te",name:"Tellur",atomicNumber:52},{symbol:"I",name:"Jod",atomicNumber:53},{symbol:"Xe",name:"Ksenon",atomicNumber:54},{symbol:"Cs",name:"Cez",atomicNumber:55},{symbol:"Ba",name:"Bar",atomicNumber:56},{symbol:"La",name:"Lantan",atomicNumber:57},{symbol:"Ce",name:"Cer",atomicNumber:58},{symbol:"Pr",name:"Prazeodym",atomicNumber:59},{symbol:"Nd",name:"Neodym",atomicNumber:60},{symbol:"Pm",name:"Promet",atomicNumber:61},{symbol:"Sm",name:"Samar",atomicNumber:62},{symbol:"Eu",name:"Europ",atomicNumber:63},{symbol:"Gd",name:"Gadolin",atomicNumber:64},{symbol:"Tb",name:"Terb",atomicNumber:65},{symbol:"Dy",name:"Dysproz",atomicNumber:66},{symbol:"Ho",name:"Holm",atomicNumber:67},{symbol:"Er",name:"Erb",atomicNumber:68},{symbol:"Tm",name:"Tul",atomicNumber:69},{symbol:"Yb",name:"Iterb",atomicNumber:70},{symbol:"Lu",name:"Lutet",atomicNumber:71},{symbol:"Hf",name:"Hafn",atomicNumber:72},{symbol:"Ta",name:"Tantal",atomicNumber:73},{symbol:"W",name:"Wolfram",atomicNumber:74},{symbol:"Re",name:"Ren",atomicNumber:75},{symbol:"Os",name:"Osm",atomicNumber:76},{symbol:"Ir",name:"Iryd",atomicNumber:77},{symbol:"Pt",name:"Platyna",atomicNumber:78},{symbol:"Au",name:"Z\u0142oto",atomicNumber:79},{symbol:"Hg",name:"Rt\u0119\u0107",atomicNumber:80},{symbol:"Tl",name:"Tal",atomicNumber:81},{symbol:"Pb",name:"O\u0142\xF3w",atomicNumber:82},{symbol:"Bi",name:"Bizmut",atomicNumber:83},{symbol:"Po",name:"Polon",atomicNumber:84},{symbol:"At",name:"Astat",atomicNumber:85},{symbol:"Rn",name:"Radon",atomicNumber:86},{symbol:"Fr",name:"Frans",atomicNumber:87},{symbol:"Ra",name:"Rad",atomicNumber:88},{symbol:"Ac",name:"Aktyn",atomicNumber:89},{symbol:"Th",name:"Tor",atomicNumber:90},{symbol:"Pa",name:"Protaktyn",atomicNumber:91},{symbol:"U",name:"Uran",atomicNumber:92},{symbol:"Np",name:"Neptun",atomicNumber:93},{symbol:"Pu",name:"Pluton",atomicNumber:94},{symbol:"Am",name:"Ameryk",atomicNumber:95},{symbol:"Cm",name:"Kiur",atomicNumber:96},{symbol:"Bk",name:"Berkel",atomicNumber:97},{symbol:"Cf",name:"Kaliforn",atomicNumber:98},{symbol:"Es",name:"Einstein",atomicNumber:99},{symbol:"Fm",name:"Ferm",atomicNumber:100},{symbol:"Md",name:"Mendelew",atomicNumber:101},{symbol:"No",name:"Nobel",atomicNumber:102},{symbol:"Lr",name:"Lorens",atomicNumber:103},{symbol:"Rf",name:"Rutherford",atomicNumber:104},{symbol:"Db",name:"Dubn",atomicNumber:105},{symbol:"Sg",name:"Seaborg",atomicNumber:106},{symbol:"Bh",name:"Bohr",atomicNumber:107},{symbol:"Hs",name:"Has",atomicNumber:108},{symbol:"Mt",name:"Meitner",atomicNumber:109},{symbol:"Ds",name:"Darmsztadt",atomicNumber:110},{symbol:"Rg",name:"Roentgen",atomicNumber:111},{symbol:"Cn",name:"Kopernik",atomicNumber:112},{symbol:"Nh",name:"Nihon",atomicNumber:113},{symbol:"Fl",name:"Flerow",atomicNumber:114},{symbol:"Mc",name:"Moskow",atomicNumber:115},{symbol:"Lv",name:"Liwermor",atomicNumber:116},{symbol:"Ts",name:"Tenes",atomicNumber:117},{symbol:"Og",name:"Oganeson",atomicNumber:118}];var ia=[{name:"metr",symbol:"m"},{name:"sekunda",symbol:"s"},{name:"mol",symbol:"mol"},{name:"amper",symbol:"A"},{name:"kelwin",symbol:"K"},{name:"kandela",symbol:"cd"},{name:"kilogram",symbol:"kg"},{name:"radian",symbol:"rad"},{name:"herc",symbol:"Hz"},{name:"niuton",symbol:"N"},{name:"paskal",symbol:"Pa"},{name:"d\u017Cul",symbol:"J"},{name:"wat",symbol:"W"},{name:"kulomb",symbol:"C"},{name:"wolt",symbol:"V"},{name:"om",symbol:"\u03A9"},{name:"tesla",symbol:"T"},{name:"stopie\u0144 Celsjusza",symbol:"\xB0C"},{name:"lumen",symbol:"lm"},{name:"bekerel",symbol:"Bq"},{name:"grej",symbol:"Gy"},{name:"siwert",symbol:"Sv"}];var Ka={chemical_element:aa,unit:ia},oa=Ka;var ea=["{{team.prefix}} {{location.city}}"];var ra=["GKS","G\xF3rnik","KS","LZS","Piast","Pogo\u0144","Polonia","Ruch","Stal","Unia","Zjednoczenie"];var ga={name:ea,prefix:ra},na=ga;var Pa={animal:r,book:c,cell_phone:y,color:w,company:p,internet:g,location:A,lorem:R,metadata:J,music:I,person:U,phone_number:$,science:oa,team:na},ka= exports.a =Pa;var Lo=new (0, _chunkZKNYQOPPcjs.n)({locale:[ka,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = ka; exports.b = Lo;
