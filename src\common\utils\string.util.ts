/**
 * String utility functions for common string operations
 */
export class StringUtil {
  /**
   * Convert string to camelCase
   */
  static toCamelCase(str: string): string {
    return str
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
        return index === 0 ? word.toLowerCase() : word.toUpperCase();
      })
      .replace(/\s+/g, '');
  }

  /**
   * Convert string to PascalCase
   */
  static toPascalCase(str: string): string {
    return str
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => {
        return word.toUpperCase();
      })
      .replace(/\s+/g, '');
  }

  /**
   * Convert string to snake_case
   */
  static toSnakeCase(str: string): string {
    return str
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('_');
  }

  /**
   * Convert string to kebab-case
   */
  static toKebabCase(str: string): string {
    return str
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('-');
  }

  /**
   * Convert string to Title Case
   */
  static toTitleCase(str: string): string {
    return str.replace(/\w\S*/g, (txt) => {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

  /**
   * Capitalize first letter
   */
  static capitalize(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Uncapitalize first letter
   */
  static uncapitalize(str: string): string {
    if (!str) return str;
    return str.charAt(0).toLowerCase() + str.slice(1);
  }

  /**
   * Truncate string with ellipsis
   */
  static truncate(str: string, length: number, suffix: string = '...'): string {
    if (str.length <= length) return str;
    return str.substring(0, length - suffix.length) + suffix;
  }

  /**
   * Truncate string at word boundary
   */
  static truncateWords(str: string, wordCount: number, suffix: string = '...'): string {
    const words = str.split(' ');
    if (words.length <= wordCount) return str;
    return words.slice(0, wordCount).join(' ') + suffix;
  }

  /**
   * Remove extra whitespace
   */
  static normalizeWhitespace(str: string): string {
    return str.replace(/\s+/g, ' ').trim();
  }

  /**
   * Remove all whitespace
   */
  static removeWhitespace(str: string): string {
    return str.replace(/\s/g, '');
  }

  /**
   * Check if string is empty or only whitespace
   */
  static isEmpty(str: string | null | undefined): boolean {
    return !str || str.trim().length === 0;
  }

  /**
   * Check if string is not empty
   */
  static isNotEmpty(str: string | null | undefined): boolean {
    return !this.isEmpty(str);
  }

  /**
   * Generate random string
   */
  static generateRandom(
    length: number,
    charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
  ): string {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
  }

  /**
   * Generate random alphanumeric string
   */
  static generateAlphanumeric(length: number): string {
    return this.generateRandom(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');
  }

  /**
   * Generate random numeric string
   */
  static generateNumeric(length: number): string {
    return this.generateRandom(length, '0123456789');
  }

  /**
   * Generate random alphabetic string
   */
  static generateAlphabetic(length: number): string {
    return this.generateRandom(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz');
  }

  /**
   * Mask string (for sensitive data)
   */
  static mask(
    str: string,
    maskChar: string = '*',
    visibleStart: number = 2,
    visibleEnd: number = 2,
  ): string {
    if (str.length <= visibleStart + visibleEnd) {
      return maskChar.repeat(str.length);
    }

    const start = str.substring(0, visibleStart);
    const end = str.substring(str.length - visibleEnd);
    const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);

    return start + middle + end;
  }

  /**
   * Mask email address
   */
  static maskEmail(email: string): string {
    const [username, domain] = email.split('@');
    if (!domain) return this.mask(email);

    const maskedUsername = this.mask(username, '*', 1, 1);
    return `${maskedUsername}@${domain}`;
  }

  /**
   * Mask phone number
   */
  static maskPhone(phone: string): string {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length < 4) return this.mask(phone);

    return this.mask(cleaned, '*', 2, 2);
  }

  /**
   * Extract initials from name
   */
  static getInitials(name: string, maxInitials: number = 2): string {
    return name
      .split(' ')
      .filter(word => word.length > 0)
      .slice(0, maxInitials)
      .map(word => word.charAt(0).toUpperCase())
      .join('');
  }

  /**
   * Slugify string for URLs
   */
  static slugify(str: string): string {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  }

  /**
   * Escape HTML characters
   */
  static escapeHtml(str: string): string {
    const htmlEscapes = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;',
    };

    return str.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
  }

  /**
   * Unescape HTML characters
   */
  static unescapeHtml(str: string): string {
    const htmlUnescapes = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#x27;': "'",
      '&#x2F;': '/',
    };

    return str.replace(/&(?:amp|lt|gt|quot|#x27|#x2F);/g, (match) => htmlUnescapes[match]);
  }

  /**
   * Count words in string
   */
  static countWords(str: string): number {
    return str.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Count characters (excluding whitespace)
   */
  static countCharacters(str: string, includeSpaces: boolean = true): number {
    return includeSpaces ? str.length : str.replace(/\s/g, '').length;
  }

  /**
   * Reverse string
   */
  static reverse(str: string): string {
    return str.split('').reverse().join('');
  }

  /**
   * Check if string is palindrome
   */
  static isPalindrome(str: string): boolean {
    const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
    return cleaned === this.reverse(cleaned);
  }

  /**
   * Repeat string n times
   */
  static repeat(str: string, count: number): string {
    return str.repeat(Math.max(0, count));
  }

  /**
   * Pad string to specified length
   */
  static pad(str: string, length: number, padChar: string = ' ', padLeft: boolean = true): string {
    if (str.length >= length) return str;
    
    const padLength = length - str.length;
    const padding = padChar.repeat(Math.ceil(padLength / padChar.length)).substring(0, padLength);
    
    return padLeft ? padding + str : str + padding;
  }

  /**
   * Left pad string
   */
  static padLeft(str: string, length: number, padChar: string = ' '): string {
    return this.pad(str, length, padChar, true);
  }

  /**
   * Right pad string
   */
  static padRight(str: string, length: number, padChar: string = ' '): string {
    return this.pad(str, length, padChar, false);
  }

  /**
   * Remove prefix from string
   */
  static removePrefix(str: string, prefix: string): string {
    return str.startsWith(prefix) ? str.substring(prefix.length) : str;
  }

  /**
   * Remove suffix from string
   */
  static removeSuffix(str: string, suffix: string): string {
    return str.endsWith(suffix) ? str.substring(0, str.length - suffix.length) : str;
  }

  /**
   * Ensure string starts with prefix
   */
  static ensurePrefix(str: string, prefix: string): string {
    return str.startsWith(prefix) ? str : prefix + str;
  }

  /**
   * Ensure string ends with suffix
   */
  static ensureSuffix(str: string, suffix: string): string {
    return str.endsWith(suffix) ? str : str + suffix;
  }

  /**
   * Split string into chunks of specified size
   */
  static chunk(str: string, size: number): string[] {
    const chunks: string[] = [];
    for (let i = 0; i < str.length; i += size) {
      chunks.push(str.substring(i, i + size));
    }
    return chunks;
  }

  /**
   * Convert string to boolean
   */
  static toBoolean(str: string): boolean {
    const truthyValues = ['true', '1', 'yes', 'on', 'enabled'];
    return truthyValues.includes(str.toLowerCase().trim());
  }

  /**
   * Format string template with variables
   */
  static template(template: string, variables: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables.hasOwnProperty(key) ? String(variables[key]) : match;
    });
  }

  /**
   * Extract numbers from string
   */
  static extractNumbers(str: string): number[] {
    const matches = str.match(/\d+/g);
    return matches ? matches.map(Number) : [];
  }

  /**
   * Remove numbers from string
   */
  static removeNumbers(str: string): string {
    return str.replace(/\d/g, '');
  }

  /**
   * Check if string contains only letters
   */
  static isAlpha(str: string): boolean {
    return /^[a-zA-Z]+$/.test(str);
  }

  /**
   * Check if string contains only numbers
   */
  static isNumeric(str: string): boolean {
    return /^\d+$/.test(str);
  }

  /**
   * Check if string contains only letters and numbers
   */
  static isAlphanumeric(str: string): boolean {
    return /^[a-zA-Z0-9]+$/.test(str);
  }
}
