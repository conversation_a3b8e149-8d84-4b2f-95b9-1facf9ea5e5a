import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import * as request from 'supertest';
import { DataSource } from 'typeorm';
import { testDatabaseConfig } from './setup';

// Global e2e test application
let app: INestApplication;
let testModule: TestingModule;
let dataSource: DataSource;

// Setup before all e2e tests
beforeAll(async () => {
  // Create test module
  testModule = await Test.createTestingModule({
    imports: [AppModule],
  })
    .overrideProvider('DATABASE_CONFIG')
    .useValue(testDatabaseConfig)
    .compile();

  // Create application
  app = testModule.createNestApplication();
  
  // Apply global configurations (same as main.ts)
  app.setGlobalPrefix('api/v1');
  
  // Get database connection
  dataSource = app.get(DataSource);
  
  await app.init();
});

// Cleanup after all e2e tests
afterAll(async () => {
  if (dataSource && dataSource.isInitialized) {
    await dataSource.destroy();
  }
  
  if (app) {
    await app.close();
  }
  
  if (testModule) {
    await testModule.close();
  }
});

// Clean database before each e2e test
beforeEach(async () => {
  if (dataSource && dataSource.isInitialized) {
    const entities = dataSource.entityMetadatas;
    
    for (const entity of entities) {
      const repository = dataSource.getRepository(entity.name);
      await repository.clear();
    }
  }
});

// E2E test utilities
export class E2ETestUtils {
  static getApp(): INestApplication {
    return app;
  }
  
  static getDataSource(): DataSource {
    return dataSource;
  }
  
  static request() {
    return request(app.getHttpServer());
  }
  
  // Authentication helpers
  static async registerUser(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    tenantId?: string;
  }) {
    const response = await this.request()
      .post('/api/v1/auth/register')
      .send(userData)
      .expect(201);
    
    return response.body;
  }
  
  static async loginUser(credentials: { email: string; password: string }) {
    const response = await this.request()
      .post('/api/v1/auth/login')
      .send(credentials)
      .expect(200);
    
    return response.body;
  }
  
  static async getAuthHeaders(email: string, password: string) {
    const loginResponse = await this.loginUser({ email, password });
    return {
      Authorization: `Bearer ${loginResponse.accessToken}`,
    };
  }
  
  // Tenant helpers
  static async createTenant(tenantData: {
    name: string;
    domain: string;
    adminEmail: string;
    adminPassword: string;
  }) {
    const response = await this.request()
      .post('/api/v1/tenants')
      .send(tenantData)
      .expect(201);
    
    return response.body;
  }
  
  static async getTenant(tenantId: string, authHeaders: any) {
    const response = await this.request()
      .get(`/api/v1/tenants/${tenantId}`)
      .set(authHeaders)
      .expect(200);
    
    return response.body;
  }
  
  // User management helpers
  static async createUser(userData: any, authHeaders: any) {
    const response = await this.request()
      .post('/api/v1/users')
      .set(authHeaders)
      .send(userData)
      .expect(201);
    
    return response.body;
  }
  
  static async getUser(userId: string, authHeaders: any) {
    const response = await this.request()
      .get(`/api/v1/users/${userId}`)
      .set(authHeaders)
      .expect(200);
    
    return response.body;
  }
  
  static async updateUser(userId: string, updateData: any, authHeaders: any) {
    const response = await this.request()
      .patch(`/api/v1/users/${userId}`)
      .set(authHeaders)
      .send(updateData)
      .expect(200);
    
    return response.body;
  }
  
  static async deleteUser(userId: string, authHeaders: any) {
    await this.request()
      .delete(`/api/v1/users/${userId}`)
      .set(authHeaders)
      .expect(204);
  }
  
  // Role management helpers
  static async createRole(roleData: any, authHeaders: any) {
    const response = await this.request()
      .post('/api/v1/roles')
      .set(authHeaders)
      .send(roleData)
      .expect(201);
    
    return response.body;
  }
  
  static async assignRoleToUser(userId: string, roleId: string, authHeaders: any) {
    const response = await this.request()
      .post(`/api/v1/users/${userId}/roles`)
      .set(authHeaders)
      .send({ roleId })
      .expect(200);
    
    return response.body;
  }
  
  // File upload helpers
  static async uploadFile(filePath: string, fieldName: string = 'file', authHeaders: any) {
    const response = await this.request()
      .post('/api/v1/files/upload')
      .set(authHeaders)
      .attach(fieldName, filePath)
      .expect(201);
    
    return response.body;
  }
  
  // Health check helpers
  static async checkHealth() {
    const response = await this.request()
      .get('/api/v1/health')
      .expect(200);
    
    return response.body;
  }
  
  // Database helpers
  static async seedTestData() {
    // Create test tenant
    const tenant = await this.createTenant({
      name: 'E2E Test Company',
      domain: 'e2e-test.com',
      adminEmail: '<EMAIL>',
      adminPassword: 'AdminPassword123!',
    });
    
    // Login as admin to get auth headers
    const authHeaders = await this.getAuthHeaders(
      '<EMAIL>',
      'AdminPassword123!'
    );
    
    // Create test roles
    const adminRole = await this.createRole({
      name: 'Admin',
      description: 'Administrator role',
      permissions: ['*'],
    }, authHeaders);
    
    const userRole = await this.createRole({
      name: 'User',
      description: 'Regular user role',
      permissions: ['read:profile', 'update:profile'],
    }, authHeaders);
    
    // Create test users
    const testUser = await this.createUser({
      email: '<EMAIL>',
      password: 'UserPassword123!',
      firstName: 'Test',
      lastName: 'User',
    }, authHeaders);
    
    // Assign role to test user
    await this.assignRoleToUser(testUser.id, userRole.id, authHeaders);
    
    return {
      tenant,
      roles: { admin: adminRole, user: userRole },
      users: { admin: tenant.admin, test: testUser },
      authHeaders,
    };
  }
  
  // Error testing helpers
  static async expectError(
    requestPromise: Promise<any>,
    expectedStatus: number,
    expectedMessage?: string
  ) {
    try {
      await requestPromise;
      throw new Error('Expected request to fail but it succeeded');
    } catch (error) {
      expect(error.status).toBe(expectedStatus);
      if (expectedMessage) {
        expect(error.response.body.message).toContain(expectedMessage);
      }
    }
  }
  
  // Performance testing helpers
  static async measureResponseTime(requestFn: () => Promise<any>) {
    const startTime = Date.now();
    const response = await requestFn();
    const responseTime = Date.now() - startTime;
    
    return { response, responseTime };
  }
  
  static async runConcurrentRequests(
    requestFn: () => Promise<any>,
    concurrency: number
  ) {
    const promises = Array(concurrency).fill(null).map(() => requestFn());
    return await Promise.all(promises);
  }
}

// Test data factories
export class E2ETestDataFactory {
  static createUserData(overrides: Partial<any> = {}) {
    return {
      email: `user-${Date.now()}@test.com`,
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      ...overrides,
    };
  }
  
  static createTenantData(overrides: Partial<any> = {}) {
    return {
      name: `Test Company ${Date.now()}`,
      domain: `test-${Date.now()}.com`,
      adminEmail: `admin-${Date.now()}@test.com`,
      adminPassword: 'AdminPassword123!',
      ...overrides,
    };
  }
  
  static createRoleData(overrides: Partial<any> = {}) {
    return {
      name: `Test Role ${Date.now()}`,
      description: 'Test role description',
      permissions: ['read:profile'],
      ...overrides,
    };
  }
}

// API response validators
export class E2EResponseValidators {
  static validateUserResponse(user: any) {
    expect(user).toHaveProperty('id');
    expect(user).toHaveProperty('email');
    expect(user).toHaveProperty('firstName');
    expect(user).toHaveProperty('lastName');
    expect(user).toHaveProperty('isActive');
    expect(user).toHaveProperty('createdAt');
    expect(user).toHaveProperty('updatedAt');
    expect(user).not.toHaveProperty('password');
  }
  
  static validateTenantResponse(tenant: any) {
    expect(tenant).toHaveProperty('id');
    expect(tenant).toHaveProperty('name');
    expect(tenant).toHaveProperty('domain');
    expect(tenant).toHaveProperty('isActive');
    expect(tenant).toHaveProperty('createdAt');
    expect(tenant).toHaveProperty('updatedAt');
  }
  
  static validateRoleResponse(role: any) {
    expect(role).toHaveProperty('id');
    expect(role).toHaveProperty('name');
    expect(role).toHaveProperty('description');
    expect(role).toHaveProperty('permissions');
    expect(role).toHaveProperty('createdAt');
    expect(role).toHaveProperty('updatedAt');
  }
  
  static validateAuthResponse(authResponse: any) {
    expect(authResponse).toHaveProperty('accessToken');
    expect(authResponse).toHaveProperty('refreshToken');
    expect(authResponse).toHaveProperty('user');
    expect(authResponse.accessToken).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
  }
  
  static validatePaginatedResponse(response: any) {
    expect(response).toHaveProperty('data');
    expect(response).toHaveProperty('meta');
    expect(response.meta).toHaveProperty('page');
    expect(response.meta).toHaveProperty('limit');
    expect(response.meta).toHaveProperty('total');
    expect(response.meta).toHaveProperty('totalPages');
    expect(Array.isArray(response.data)).toBe(true);
  }
  
  static validateErrorResponse(response: any, expectedStatus: number) {
    expect(response).toHaveProperty('statusCode', expectedStatus);
    expect(response).toHaveProperty('message');
    expect(response).toHaveProperty('timestamp');
    expect(response).toHaveProperty('path');
  }
}
