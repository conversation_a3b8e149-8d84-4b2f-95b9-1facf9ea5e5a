import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { ValidationUtil } from '../utils/validation.util';

/**
 * Custom validation decorators for common validation scenarios
 */

// Strong Password Validator
@ValidatorConstraint({ name: 'isStrongPassword', async: false })
export class IsStrongPasswordConstraint implements ValidatorConstraintInterface {
  validate(password: string, args: ValidationArguments) {
    const result = ValidationUtil.validatePasswordStrength(password);
    return result.isValid;
  }

  defaultMessage(args: ValidationArguments) {
    const password = args.value;
    const result = ValidationUtil.validatePasswordStrength(password);
    return result.errors.join(', ');
  }
}

export function IsStrongPassword(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsStrongPasswordConstraint,
    });
  };
}

// Valid Tenant ID Validator
@ValidatorConstraint({ name: 'isValidTenantId', async: false })
export class IsValidTenantIdConstraint implements ValidatorConstraintInterface {
  validate(tenantId: string, args: ValidationArguments) {
    return ValidationUtil.isValidTenantId(tenantId);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Tenant ID must be alphanumeric with hyphens and underscores, 3-50 characters long';
  }
}

export function IsValidTenantId(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidTenantIdConstraint,
    });
  };
}

// Valid Employee ID Validator
@ValidatorConstraint({ name: 'isValidEmployeeId', async: false })
export class IsValidEmployeeIdConstraint implements ValidatorConstraintInterface {
  validate(employeeId: string, args: ValidationArguments) {
    return ValidationUtil.isValidEmployeeId(employeeId);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Employee ID must be alphanumeric, 3-20 characters long';
  }
}

export function IsValidEmployeeId(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidEmployeeIdConstraint,
    });
  };
}

// Date Range Validator
@ValidatorConstraint({ name: 'isValidDateRange', async: false })
export class IsValidDateRangeConstraint implements ValidatorConstraintInterface {
  validate(endDate: string, args: ValidationArguments) {
    const [startDateProperty] = args.constraints;
    const startDate = (args.object as any)[startDateProperty];
    
    if (!startDate || !endDate) return true; // Let other validators handle required validation
    
    return ValidationUtil.isValidDateRange(startDate, endDate);
  }

  defaultMessage(args: ValidationArguments) {
    return 'End date must be after start date';
  }
}

export function IsValidDateRange(startDateProperty: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [startDateProperty],
      validator: IsValidDateRangeConstraint,
    });
  };
}

// Currency Code Validator
@ValidatorConstraint({ name: 'isValidCurrencyCode', async: false })
export class IsValidCurrencyCodeConstraint implements ValidatorConstraintInterface {
  validate(currency: string, args: ValidationArguments) {
    return ValidationUtil.isValidCurrencyCode(currency);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Currency code must be a valid ISO 4217 currency code';
  }
}

export function IsValidCurrencyCode(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidCurrencyCodeConstraint,
    });
  };
}

// Salary Amount Validator
@ValidatorConstraint({ name: 'isValidSalaryAmount', async: false })
export class IsValidSalaryAmountConstraint implements ValidatorConstraintInterface {
  validate(amount: number, args: ValidationArguments) {
    const [currencyProperty] = args.constraints;
    const currency = currencyProperty ? (args.object as any)[currencyProperty] : 'USD';
    
    return ValidationUtil.isValidSalaryAmount(amount, currency);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Salary amount is not within acceptable range for the specified currency';
  }
}

export function IsValidSalaryAmount(currencyProperty?: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: currencyProperty ? [currencyProperty] : [],
      validator: IsValidSalaryAmountConstraint,
    });
  };
}

// Phone Number Validator
@ValidatorConstraint({ name: 'isValidPhoneNumber', async: false })
export class IsValidPhoneNumberConstraint implements ValidatorConstraintInterface {
  validate(phone: string, args: ValidationArguments) {
    return ValidationUtil.isValidPhoneNumber(phone);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Phone number must be a valid international phone number';
  }
}

export function IsValidPhoneNumber(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidPhoneNumberConstraint,
    });
  };
}

// Time Format Validator
@ValidatorConstraint({ name: 'isValidTimeFormat', async: false })
export class IsValidTimeFormatConstraint implements ValidatorConstraintInterface {
  validate(time: string, args: ValidationArguments) {
    return ValidationUtil.isValidTimeFormat(time);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Time must be in HH:MM or HH:MM:SS format';
  }
}

export function IsValidTimeFormat(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidTimeFormatConstraint,
    });
  };
}

// Timezone Validator
@ValidatorConstraint({ name: 'isValidTimezone', async: false })
export class IsValidTimezoneConstraint implements ValidatorConstraintInterface {
  validate(timezone: string, args: ValidationArguments) {
    return ValidationUtil.isValidTimezone(timezone);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Timezone must be a valid IANA timezone identifier';
  }
}

export function IsValidTimezone(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidTimezoneConstraint,
    });
  };
}

// Not Empty String Validator (trims whitespace)
@ValidatorConstraint({ name: 'isNotEmptyString', async: false })
export class IsNotEmptyStringConstraint implements ValidatorConstraintInterface {
  validate(value: string, args: ValidationArguments) {
    return typeof value === 'string' && value.trim().length > 0;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Field cannot be empty or contain only whitespace';
  }
}

export function IsNotEmptyString(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsNotEmptyStringConstraint,
    });
  };
}

// Alphanumeric Validator
@ValidatorConstraint({ name: 'isAlphanumeric', async: false })
export class IsAlphanumericConstraint implements ValidatorConstraintInterface {
  validate(value: string, args: ValidationArguments) {
    return /^[a-zA-Z0-9]+$/.test(value);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Field must contain only letters and numbers';
  }
}

export function IsAlphanumeric(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsAlphanumericConstraint,
    });
  };
}

// Future Date Validator
@ValidatorConstraint({ name: 'isFutureDate', async: false })
export class IsFutureDateConstraint implements ValidatorConstraintInterface {
  validate(date: string, args: ValidationArguments) {
    const d = new Date(date);
    return d > new Date();
  }

  defaultMessage(args: ValidationArguments) {
    return 'Date must be in the future';
  }
}

export function IsFutureDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsFutureDateConstraint,
    });
  };
}

// Past Date Validator
@ValidatorConstraint({ name: 'isPastDate', async: false })
export class IsPastDateConstraint implements ValidatorConstraintInterface {
  validate(date: string, args: ValidationArguments) {
    const d = new Date(date);
    return d < new Date();
  }

  defaultMessage(args: ValidationArguments) {
    return 'Date must be in the past';
  }
}

export function IsPastDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsPastDateConstraint,
    });
  };
}
