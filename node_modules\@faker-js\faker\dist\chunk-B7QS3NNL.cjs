"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunk63UJVCQ4cjs = require('./chunk-63UJVCQ4.cjs');var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var t=["com","net","org","sn"];var r=["gmail.com","hotmail.com","yahoo.com"];var K={domain_suffix:t,free_email:r},n=K;var u=["####","###","##","#"];var m=["Bakel","<PERSON><PERSON><PERSON>","Bar<PERSON>","Bignona","Dagana","Da<PERSON>","Dakar","Diourbel","Fatick","Gandiaye","<PERSON><PERSON><PERSON>","<PERSON>udo<PERSON>","<PERSON><PERSON><PERSON>\xE9o","<PERSON><PERSON>\xE9diawaye","Joal-<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","K<PERSON><PERSON>e","<PERSON><PERSON>","<PERSON>unghe<PERSON>","K\xE9b\xE9mer","K\xE9dougou","Lingu\xE8re","Louga","Matam","Mback\xE9","Mboro","Mbour","NDofan","Ndioum","Ngaa\xFF Meckhe","Ngu\xE9khokh","Nioro du Rip","Ourossogui","Pikine","Pout","Richard-Toll","Rufisque","Saint-Louis","Sokone","S\xE9bikhotane","S\xE9dhiou","Tambacounda","Thiadiaye","Thi\xE8s","Tivaouane","Touba","V\xE9lingara","Ziguinchor"];var d=["{{location.city_name}}"];var l=["#####"];var s=["Apt. ###","# \xE9tage"];var h=["Dakar","Diourbel","Fatick","Kaffrine","Kaolack","Kolda","K\xE9dougou","Louga","Matam","Saint-Louis","S\xE9dhiou","Tambacounda","Thi\xE8s","Ziguinchor"];var b={normal:"{{location.buildingNumber}} {{location.street}}",full:"{{location.buildingNumber}} {{location.street}} {{location.secondaryAddress}}"};var y=["{{location.street_prefix}} {{location.street_suffix}}"];var f=["All\xE9e","Voie","Rue","Avenue","Boulevard","Quai","Place","\xC9changeur","R\xE9sidence","Immeuble"];var p=["de l'alternance","de l'\xE9mergence","Abdou Aziz Sy Dabakh","Amadou Assane Ndoye","Birago Diop","Blaise Diagne","Cheikh Amadou Bamba","Cheikh Anta Diop","Cheikh Seydi El Hadji Malick Sy","Dial Diop","Faidherbe","Galandou Diouf","Houphou\xEBt Boigny","Lamine Gueye","Lat Dior","L\xE9opold Sedar Senghor","Neslon Mandela","Saint Michel","St Joseph","S\xE9rigne Fallou Mback\xE9","Victor Hugo","Wagane Diouf","William Ponty","Yacine Boubou","de Bordeaux","de Contournement Nord","de Nguinth","des Diambars","des Jasmins","du Baol","du Tirailleur"];var N={building_number:u,city_name:m,city_pattern:d,postcode:l,secondary_address:s,state:h,street_address:b,street_pattern:y,street_prefix:f,street_suffix:p},M=N;var F={title:"French (Senegal)",code:"fr_SN",country:"SN",language:"fr",endonym:"Fran\xE7ais (S\xE9n\xE9gal)",dir:"ltr",script:"Latn"},A=F;var S={generic:["Abdou","Abdoul","Abdoulaye","Abibatou","Abou","Aboubacar","Aby","Adama","Ahmed","Alassane","Alima","Aliou","Alioune","Alpha","Aly","Amadou","Amady","Amath","Amdy","Ameth","Ami","Amie","Amina","Aminata","Amy","Anna","Antoine","Arame","Arona","Asta","Astou","Atta","Awa","A\xEFcha","A\xEFda","A\xEFssa","A\xEFssata","A\xEFssatou","Baba","Babacar","Bakary","Banna","Bineta","Binta","Bintou","Birane","Bouba","Boubacar","Boubou","Cheikh","Cheikhou","Chekhouna","Cherif","Coumba","Dado","Dame","Daouda","Demba","Diarra","Dieynaba","Dior","Djibril","Elisabeth","El\xE8ne","Fanta","Fatim","Fatima","Fatimata","Fatma","Fatou","Fatoumata","Fily","Haby","Hamidou","Hawa","Ibou","Ibra","Ibrahima","Idrissa","Idy","Insa","Isma\xEFla","Issa","Jean","Jeanne","Joseph","Josephine","Kadiatou","Kalidou","Khadidiatou","Khadim","Khady","Khalifa","Khoudia","Kin\xE9","K\xE9ba","Lala","Lamine","Lassana","Mactar","Madeleine","Mady","Maguette","Makhtar","Malick","Mama","Mamadou","Mamady","Mamour","Mansour","Mariama","Marie","Mari\xE8me","Mary","Mar\xE8me","Massamba","Matar","Ma\xEF","Ma\xEFmouna","Mbaye","Me\xEFssa","Michel","Moctar","Modou","Mohamadou","Mohamed","Mohammed","Mouhamadou","Mouhamed","Mouhameth","Mouhammadou","Moussa","Moustapha","M\xE9doune","N\xE9n\xE9","Omar","Oumar","Oumou","Ousmane","Path\xE9","Paul","Paulette","Penda","Pierre","Rama","Ramata","Ramatoulaye","Rokhaya","Rose","Sada","Sadio","Safiatou","Safi\xE9tou","Sagar","Salif","Salimata","Salimatou","Saliou","Saly","Samba","Sa\xE8r","Seckou","Seydou","Seynabou","Sidy","Sir\xE9","Soda","Sokhna","Sophie","Souleymane","S\xE9kou","Thierno","Th\xE9r\xE8se","Tidiane","Waly","Yacine","Yaya","Yoro","Youssou","Youssouph","Youssoupha"],female:["Abibatou","Aby","Adama","Alima","Ami","Amie","Amina","Aminata","Amy","Anna","Arame","Asta","Astou","Atta","Awa","A\xEFcha","A\xEFda","A\xEFssa","A\xEFssata","A\xEFssatou","Banna","Bineta","Binta","Bintou","Coumba","Dado","Diarra","Dieynaba","Dior","Elisabeth","El\xE8ne","Fanta","Fatim","Fatima","Fatimata","Fatma","Fatou","Fatoumata","Fily","Haby","Hawa","Jeanne","Josephine","Kadiatou","Khadidiatou","Khady","Khoudia","Kin\xE9","Lala","Madeleine","Maguette","Mariama","Marie","Mari\xE8me","Mary","Mar\xE8me","Ma\xEF","Ma\xEFmouna","N\xE9n\xE9","Oumou","Paulette","Penda","Rama","Ramata","Ramatoulaye","Rokhaya","Rose","Safiatou","Safi\xE9tou","Salimata","Salimatou","Saly","Seynabou","Soda","Sokhna","Sophie","Th\xE9r\xE8se","Yacine","Yaya"],male:["Abdou","Abdoul","Abdoulaye","Abou","Aboubacar","Adama","Ahmed","Alassane","Aliou","Alioune","Alpha","Aly","Amadou","Amady","Amath","Amdy","Ameth","Antoine","Arona","Baba","Babacar","Bakary","Birane","Bouba","Boubacar","Boubou","Cheikh","Cheikhou","Chekhouna","Cherif","Dame","Daouda","Demba","Djibril","Hamidou","Ibou","Ibra","Ibrahima","Idrissa","Idy","Insa","Isma\xEFla","Issa","Jean","Joseph","Kalidou","Khadim","Khalifa","K\xE9ba","Lamine","Lassana","Mactar","Mady","Makhtar","Malick","Mama","Mamadou","Mamady","Mamour","Mansour","Massamba","Matar","Mbaye","Me\xEFssa","Michel","Moctar","Modou","Mohamadou","Mohamed","Mohammed","Mouhamadou","Mouhamed","Mouhameth","Mouhammadou","Moussa","Moustapha","M\xE9doune","Omar","Oumar","Ousmane","Path\xE9","Paul","Pierre","Sada","Sadio","Sagar","Salif","Saliou","Samba","Sa\xE8r","Seckou","Seydou","Sidy","Sir\xE9","Souleymane","S\xE9kou","Thierno","Tidiane","Waly","Yoro","Youssou","Youssouph","Youssoupha"]};var c={generic:["Amar","Anne","Aw","A\xEFdara","Ba","Babou","Badiane","Badji","Bakhoum","Bald\xE9","Barry","Beye","Biteye","Bodian","Boye","Camara","Ciss","Cisse","Cissokho","Coly","Coulibaly","Dabo","Dembel\xE9","Dia","Diaby","Diack","Diagne","Diakhat\xE9","Diallo","Diamanka","Diao","Diarra","Diatta","Diattara","Diaw","Diawara","Dieng","Dieye","Diome","Dione","Diongue","Diop","Diouf","Dioum","Di\xE8ne","Di\xE9dhiou","Di\xE9m\xE9","Djitt\xE9","Dram\xE9","D\xE8me","Fall","Faty","Faye","Fofana","Gadiaga","Gassama","Gaye","Gning","Gningue","Gomis","Goudiaby","Gueye","Guiss\xE9","Hane","Ka","Kamara","Kandji","Kand\xE9","Kane","Kant\xE9","Kass\xE9","Ke\xEFta","Khouma","Konat\xE9","Kont\xE9","K\xE9b\xE9","Lam","Leye","Lo","Loum","Ly","Manga","Mangane","Man\xE9","Mar","Mback\xE9","Mballo","Mbaye","Mbodj","Mboup","Mbow","Mb\xE8ngue","Mendy","Ndao","Ndaw","Ndiaye","Ndione","Ndir","Ndong","Ndour","Ndoye","Ngom","Ngu\xE8r","Niane","Niang","Niass","Niasse","Pouye","Sabaly","Sadio","Sagna","Sakho","Sall","Samb","Samba","Sambe","Sambou","San\xE9","Sarr","Seck","Senghor","Seydi","Seye","Sidib\xE9","Sonko","Souare","Soumar\xE9","Sow","Sy","Sylla","S\xE8ne","S\xE9gnane","Tall","Tamba","Thiam","Thiao","Thiaw","Thiongane","Thioub","Thioune","Tine","Top","Tour\xE9","Traor\xE9","Wade","Wane","Willane","Yade"]};var D={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var g=[{value:"{{person.firstName}} {{person.lastName}}",weight:1}];var x={first_name:S,last_name:c,last_name_pattern:D,name:g},k=x;var T={internet:n,location:M,metadata:A,person:k},B= exports.a =T;var ga=new (0, _chunkZKNYQOPPcjs.n)({locale:[B,_chunk63UJVCQ4cjs.a,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = B; exports.b = ga;
