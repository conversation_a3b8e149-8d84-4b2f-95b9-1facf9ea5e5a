import { faker } from '@faker-js/faker';
import { ValidationPipe } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

/**
 * Test data generators using Faker.js for realistic test data
 */
export class TestDataGenerator {
  static generateUser(overrides: Partial<any> = {}) {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      password: 'TestPassword123!',
      isActive: true,
      tenantId: faker.string.uuid(),
      roles: [],
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static generateTenant(overrides: Partial<any> = {}) {
    return {
      id: faker.string.uuid(),
      name: faker.company.name(),
      domain: faker.internet.domainName(),
      isActive: true,
      settings: {},
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static generateRole(overrides: Partial<any> = {}) {
    return {
      id: faker.string.uuid(),
      name: faker.person.jobTitle(),
      description: faker.lorem.sentence(),
      permissions: [
        'read:profile',
        'update:profile',
      ],
      tenantId: faker.string.uuid(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static generateEmployee(overrides: Partial<any> = {}) {
    return {
      id: faker.string.uuid(),
      employeeId: faker.string.alphanumeric(8).toUpperCase(),
      userId: faker.string.uuid(),
      tenantId: faker.string.uuid(),
      department: faker.commerce.department(),
      position: faker.person.jobTitle(),
      salary: faker.number.int({ min: 30000, max: 150000 }),
      hireDate: faker.date.past({ years: 5 }),
      status: 'active',
      manager: null,
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static generateAddress(overrides: Partial<any> = {}) {
    return {
      street: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      zipCode: faker.location.zipCode(),
      country: faker.location.country(),
      ...overrides,
    };
  }

  static generateContact(overrides: Partial<any> = {}) {
    return {
      email: faker.internet.email(),
      phone: faker.phone.number('+1##########'),
      mobile: faker.phone.number('+1##########'),
      ...overrides,
    };
  }

  static generateBulkUsers(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.generateUser(overrides));
  }

  static generateBulkEmployees(count: number, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, () => this.generateEmployee(overrides));
  }
}

/**
 * Mock factory for creating test doubles
 */
export class MockFactory {
  static createMockRepository<T = any>() {
    return {
      find: jest.fn(),
      findOne: jest.fn(),
      findOneBy: jest.fn(),
      findAndCount: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn(() => ({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoinAndSelect: jest.fn().mockReturnThis(),
        getOne: jest.fn(),
        getMany: jest.fn(),
        getManyAndCount: jest.fn(),
        execute: jest.fn(),
      })),
    };
  }

  static createMockService<T = any>() {
    return {
      findAll: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      findByEmail: jest.fn(),
      findByTenant: jest.fn(),
    };
  }

  static createMockLogger() {
    return {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      info: jest.fn(),
      business: jest.fn(),
      security: jest.fn(),
      audit: jest.fn(),
      performance: jest.fn(),
      database: jest.fn(),
      external: jest.fn(),
      userAction: jest.fn(),
      timer: jest.fn(() => ({ end: jest.fn() })),
      child: jest.fn(() => MockFactory.createMockLogger()),
    };
  }

  static createMockRedisClient() {
    return {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      flushall: jest.fn(),
      connect: jest.fn(),
      disconnect: jest.fn(),
      on: jest.fn(),
    };
  }

  static createMockEmailService() {
    return {
      sendEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
      sendWelcomeEmail: jest.fn().mockResolvedValue(true),
      sendPasswordResetEmail: jest.fn().mockResolvedValue(true),
      sendVerificationEmail: jest.fn().mockResolvedValue(true),
      sendNotificationEmail: jest.fn().mockResolvedValue(true),
    };
  }

  static createMockFileService() {
    return {
      uploadFile: jest.fn().mockResolvedValue({
        url: 'https://test.com/file.jpg',
        key: 'test-file-key',
        size: 1024,
      }),
      deleteFile: jest.fn().mockResolvedValue(true),
      getFileUrl: jest.fn().mockReturnValue('https://test.com/file.jpg'),
      validateFile: jest.fn().mockReturnValue(true),
    };
  }

  static createMockAuditService() {
    return {
      logEvent: jest.fn().mockResolvedValue(true),
      logUserAction: jest.fn().mockResolvedValue(true),
      logSystemEvent: jest.fn().mockResolvedValue(true),
      logSecurityEvent: jest.fn().mockResolvedValue(true),
    };
  }

  static createMockCacheService() {
    return {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      clear: jest.fn(),
      keys: jest.fn(),
      ttl: jest.fn(),
    };
  }
}

/**
 * Test module builder for easier test setup
 */
export class TestModuleBuilder {
  private moduleBuilder: any;

  constructor() {
    this.moduleBuilder = Test.createTestingModule({});
  }

  static create() {
    return new TestModuleBuilder();
  }

  addController(controller: any) {
    this.moduleBuilder = this.moduleBuilder.overrideProvider(controller);
    return this;
  }

  addService(service: any, mock?: any) {
    if (mock) {
      this.moduleBuilder = this.moduleBuilder
        .overrideProvider(service)
        .useValue(mock);
    } else {
      this.moduleBuilder = this.moduleBuilder.overrideProvider(service);
    }
    return this;
  }

  addRepository(repository: any, mock?: any) {
    const mockRepo = mock || MockFactory.createMockRepository();
    this.moduleBuilder = this.moduleBuilder
      .overrideProvider(repository)
      .useValue(mockRepo);
    return this;
  }

  addMockLogger() {
    this.moduleBuilder = this.moduleBuilder
      .overrideProvider('LOGGER_SERVICE')
      .useValue(MockFactory.createMockLogger());
    return this;
  }

  addMockRedis() {
    this.moduleBuilder = this.moduleBuilder
      .overrideProvider('REDIS_CLIENT')
      .useValue(MockFactory.createMockRedisClient());
    return this;
  }

  addValidationPipe() {
    this.moduleBuilder = this.moduleBuilder
      .overrideProvider(ValidationPipe)
      .useValue(new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }));
    return this;
  }

  async build(): Promise<TestingModule> {
    return await this.moduleBuilder.compile();
  }
}

/**
 * Assertion helpers for common test patterns
 */
export class TestAssertions {
  static expectValidUUID(value: string) {
    expect(value).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
  }

  static expectValidEmail(value: string) {
    expect(value).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  }

  static expectValidDate(value: string | Date) {
    const date = new Date(value);
    expect(date.getTime()).not.toBeNaN();
  }

  static expectValidPhoneNumber(value: string) {
    expect(value).toMatch(/^\+[1-9]\d{6,14}$/);
  }

  static expectPaginatedResponse(response: any) {
    expect(response).toHaveProperty('data');
    expect(response).toHaveProperty('meta');
    expect(response.meta).toHaveProperty('page');
    expect(response.meta).toHaveProperty('limit');
    expect(response.meta).toHaveProperty('total');
    expect(response.meta).toHaveProperty('totalPages');
    expect(Array.isArray(response.data)).toBe(true);
  }

  static expectErrorResponse(response: any, statusCode: number) {
    expect(response).toHaveProperty('statusCode', statusCode);
    expect(response).toHaveProperty('message');
    expect(response).toHaveProperty('timestamp');
    expect(response).toHaveProperty('path');
  }

  static expectUserResponse(user: any) {
    expect(user).toHaveProperty('id');
    expect(user).toHaveProperty('email');
    expect(user).toHaveProperty('firstName');
    expect(user).toHaveProperty('lastName');
    expect(user).toHaveProperty('isActive');
    expect(user).toHaveProperty('createdAt');
    expect(user).toHaveProperty('updatedAt');
    expect(user).not.toHaveProperty('password');
  }

  static expectTenantResponse(tenant: any) {
    expect(tenant).toHaveProperty('id');
    expect(tenant).toHaveProperty('name');
    expect(tenant).toHaveProperty('domain');
    expect(tenant).toHaveProperty('isActive');
    expect(tenant).toHaveProperty('createdAt');
    expect(tenant).toHaveProperty('updatedAt');
  }

  static expectRoleResponse(role: any) {
    expect(role).toHaveProperty('id');
    expect(role).toHaveProperty('name');
    expect(role).toHaveProperty('description');
    expect(role).toHaveProperty('permissions');
    expect(role).toHaveProperty('createdAt');
    expect(role).toHaveProperty('updatedAt');
  }
}

/**
 * Performance testing utilities
 */
export class PerformanceTestUtils {
  static async measureExecutionTime<T>(
    operation: () => Promise<T>
  ): Promise<{ result: T; executionTime: number }> {
    const startTime = process.hrtime.bigint();
    const result = await operation();
    const endTime = process.hrtime.bigint();
    const executionTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds

    return { result, executionTime };
  }

  static async runLoadTest<T>(
    operation: () => Promise<T>,
    options: {
      iterations: number;
      concurrency?: number;
      warmupIterations?: number;
    }
  ): Promise<{
    results: T[];
    averageTime: number;
    minTime: number;
    maxTime: number;
    totalTime: number;
  }> {
    const { iterations, concurrency = 1, warmupIterations = 0 } = options;

    // Warmup
    for (let i = 0; i < warmupIterations; i++) {
      await operation();
    }

    const results: T[] = [];
    const times: number[] = [];
    const startTime = Date.now();

    // Run test iterations
    for (let i = 0; i < iterations; i += concurrency) {
      const batch = Math.min(concurrency, iterations - i);
      const promises = Array(batch).fill(null).map(async () => {
        const { result, executionTime } = await this.measureExecutionTime(operation);
        times.push(executionTime);
        return result;
      });

      const batchResults = await Promise.all(promises);
      results.push(...batchResults);
    }

    const totalTime = Date.now() - startTime;
    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    return {
      results,
      averageTime,
      minTime,
      maxTime,
      totalTime,
    };
  }

  static expectPerformance(executionTime: number, maxTime: number) {
    expect(executionTime).toBeLessThan(maxTime);
  }

  static expectThroughput(operationsPerSecond: number, minThroughput: number) {
    expect(operationsPerSecond).toBeGreaterThanOrEqual(minThroughput);
  }
}

/**
 * Database testing utilities
 */
export class DatabaseTestUtils {
  static async cleanDatabase(dataSource: any) {
    const entities = dataSource.entityMetadatas;
    
    // Disable foreign key checks
    await dataSource.query('SET FOREIGN_KEY_CHECKS = 0');
    
    // Clear all tables
    for (const entity of entities) {
      const repository = dataSource.getRepository(entity.name);
      await repository.clear();
    }
    
    // Re-enable foreign key checks
    await dataSource.query('SET FOREIGN_KEY_CHECKS = 1');
  }

  static async seedDatabase(dataSource: any, seedData: any) {
    for (const [entityName, data] of Object.entries(seedData)) {
      const repository = dataSource.getRepository(entityName);
      if (Array.isArray(data)) {
        await repository.save(data);
      } else {
        await repository.save([data]);
      }
    }
  }

  static async countRecords(dataSource: any, entityName: string): Promise<number> {
    const repository = dataSource.getRepository(entityName);
    return await repository.count();
  }
}
