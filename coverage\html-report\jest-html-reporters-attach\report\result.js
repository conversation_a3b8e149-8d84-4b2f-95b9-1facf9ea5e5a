window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":0,"numPassedTests":0,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":0,"startTime":1751361655144,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"runtime":0,"slow":false,"start":0},"testFilePath":"C:\\PeopleNest\\src\\common\\utils\\__tests__\\error-handler.util.unit.spec.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31mJ<PERSON> encountered an unexpected token\u001b[39m\u001b[22m\n\n    Jest failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when Jest is not configured to support such syntax.\n\n    Out of the box Jest supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    SyntaxError: C:\\PeopleNest\\src\\common\\utils\\__tests__\\error-handler.util.unit.spec.ts: Missing semicolon. (45:50)\n\n    \u001b[0m \u001b[90m 43 |\u001b[39m       expect(httpException\u001b[33m.\u001b[39mgetStatus())\u001b[33m.\u001b[39mtoBe(\u001b[35m400\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 44 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m       \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m httpException\u001b[33m.\u001b[39mgetResponse() \u001b[36mas\u001b[39m any\u001b[33m;\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 46 |\u001b[39m       expect(response\u001b[33m.\u001b[39mcode)\u001b[33m.\u001b[39mtoBe(\u001b[33mErrorCode\u001b[39m\u001b[33m.\u001b[39m\u001b[33mVALIDATION_ERROR\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 47 |\u001b[39m       expect(response\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoBe(\u001b[32m'Invalid input'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 48 |\u001b[39m       expect(response\u001b[33m.\u001b[39mdetails)\u001b[33m.\u001b[39mtoEqual({ field\u001b[33m:\u001b[39m \u001b[32m'email'\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat constructor (\u001b[22mnode_modules/@babel/parser/src/parse-error.ts\u001b[2m:95:45)\u001b[22m\n      \u001b[2mat Parser.toParseError [as raise] (\u001b[22mnode_modules/@babel/parser/src/tokenizer/index.ts\u001b[2m:1503:19)\u001b[22m\n      \u001b[2mat Parser.raise [as semicolon] (\u001b[22mnode_modules/@babel/parser/src/parser/util.ts\u001b[2m:149:10)\u001b[22m\n      \u001b[2mat Parser.semicolon [as parseVarStatement] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1243:10)\u001b[22m\n      \u001b[2mat Parser.parseVarStatement [as parseStatementContent] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:603:21)\u001b[22m\n      \u001b[2mat Parser.parseStatementContent [as parseStatementLike] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:472:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementLike [as parseStatementListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:421:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementListItem [as parseBlockOrModuleBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1435:16)\u001b[22m\n      \u001b[2mat Parser.parseBlockOrModuleBlockBody [as parseBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1408:10)\u001b[22m\n      \u001b[2mat Parser.parseBlockBody [as parseBlock] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1376:10)\u001b[22m\n      \u001b[2mat Parser.parseBlock [as parseFunctionBody] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2576:24)\u001b[22m\n      \u001b[2mat Parser.parseFunctionBody [as parseArrowExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2517:10)\u001b[22m\n      \u001b[2mat Parser.parseArrowExpression [as parseParenAndDistinguishExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1808:12)\u001b[22m\n      \u001b[2mat Parser.parseParenAndDistinguishExpression [as parseExprAtom] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1153:21)\u001b[22m\n      \u001b[2mat Parser.parseExprAtom [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:728:23)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:707:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:669:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:403:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:415:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:370:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:301:21)\u001b[22m\n      \u001b[2mat parseMaybeAssign (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:257:12)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3142:12)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseMaybeAssignAllowIn] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:256:17)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssignAllowIn [as parseExprListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2739:18)\u001b[22m\n      \u001b[2mat Parser.parseExprListItem [as parseCallExpressionArguments] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1030:14)\u001b[22m\n      \u001b[2mat Parser.parseCallExpressionArguments [as parseCoverCallAndAsyncArrowHead] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:908:29)\u001b[22m\n      \u001b[2mat Parser.parseCoverCallAndAsyncArrowHead [as parseSubscript] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:790:19)\u001b[22m\n      \u001b[2mat Parser.parseSubscript [as parseSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:749:19)\u001b[22m\n      \u001b[2mat Parser.parseSubscripts [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:734:17)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:707:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:669:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:403:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:415:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:370:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:301:21)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssign [as parseExpressionBase] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:226:23)\u001b[22m\n      \u001b[2mat parseExpressionBase (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:217:39)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3137:16)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:217:17)\u001b[22m\n      \u001b[2mat Parser.parseExpression [as parseStatementContent] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:679:23)\u001b[22m\n      \u001b[2mat Parser.parseStatementContent [as parseStatementLike] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:472:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementLike [as parseStatementListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:421:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementListItem [as parseBlockOrModuleBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1435:16)\u001b[22m\n      \u001b[2mat Parser.parseBlockOrModuleBlockBody [as parseBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1408:10)\u001b[22m\n      \u001b[2mat Parser.parseBlockBody [as parseBlock] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1376:10)\u001b[22m\n      \u001b[2mat Parser.parseBlock [as parseFunctionBody] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2576:24)\u001b[22m\n      \u001b[2mat Parser.parseFunctionBody [as parseArrowExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2517:10)\u001b[22m\n      \u001b[2mat Parser.parseArrowExpression [as parseParenAndDistinguishExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1808:12)\u001b[22m\n      \u001b[2mat Parser.parseParenAndDistinguishExpression [as parseExprAtom] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1153:21)\u001b[22m\n      \u001b[2mat Parser.parseExprAtom [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:728:23)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:707:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:669:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:403:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:415:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:370:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:301:21)\u001b[22m\n      \u001b[2mat parseMaybeAssign (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:257:12)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3142:12)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseMaybeAssignAllowIn] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:256:17)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssignAllowIn [as parseExprListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2739:18)\u001b[22m\n      \u001b[2mat Parser.parseExprListItem [as parseCallExpressionArguments] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1030:14)\u001b[22m\n      \u001b[2mat Parser.parseCallExpressionArguments [as parseCoverCallAndAsyncArrowHead] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:908:29)\u001b[22m\n      \u001b[2mat Parser.parseCoverCallAndAsyncArrowHead [as parseSubscript] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:790:19)\u001b[22m\n      \u001b[2mat Parser.parseSubscript [as parseSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:749:19)\u001b[22m\n      \u001b[2mat Parser.parseSubscripts [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:734:17)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:707:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:669:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:403:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:415:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:370:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:301:21)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssign [as parseExpressionBase] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:226:23)\u001b[22m\n      \u001b[2mat parseExpressionBase (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:217:39)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3137:16)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:217:17)\u001b[22m\n      \u001b[2mat Parser.parseExpression [as parseStatementContent] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:679:23)\u001b[22m\n      \u001b[2mat Parser.parseStatementContent [as parseStatementLike] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:472:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementLike [as parseStatementListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:421:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementListItem [as parseBlockOrModuleBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1435:16)\u001b[22m\n      \u001b[2mat Parser.parseBlockOrModuleBlockBody [as parseBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1408:10)\u001b[22m\n      \u001b[2mat Parser.parseBlockBody [as parseBlock] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1376:10)\u001b[22m\n      \u001b[2mat Parser.parseBlock [as parseFunctionBody] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2576:24)\u001b[22m\n      \u001b[2mat Parser.parseFunctionBody [as parseArrowExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2517:10)\u001b[22m\n      \u001b[2mat Parser.parseArrowExpression [as parseParenAndDistinguishExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1808:12)\u001b[22m\n      \u001b[2mat Parser.parseParenAndDistinguishExpression [as parseExprAtom] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1153:21)\u001b[22m\n      \u001b[2mat Parser.parseExprAtom [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:728:23)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:707:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:669:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:403:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:415:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:370:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:301:21)\u001b[22m\n      \u001b[2mat parseMaybeAssign (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:257:12)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3142:12)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseMaybeAssignAllowIn] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:256:17)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssignAllowIn [as parseExprListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2739:18)\u001b[22m\n      \u001b[2mat Parser.parseExprListItem [as parseCallExpressionArguments] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1030:14)\u001b[22m\n      \u001b[2mat Parser.parseCallExpressionArguments [as parseCoverCallAndAsyncArrowHead] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:908:29)\u001b[22m\n      \u001b[2mat Parser.parseCoverCallAndAsyncArrowHead [as parseSubscript] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:790:19)\u001b[22m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["src/**/*.{ts,js}","!src/**/*.d.ts","!src/**/__tests__/**","!src/**/*.spec.ts","!src/**/*.test.ts","!src/main.ts","!src/**/*.interface.ts","!src/**/*.enum.ts","!src/**/*.dto.ts","!src/**/*.entity.ts","!src/**/index.ts"],"coverageDirectory":"C:\\PeopleNest\\coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json","clover"],"coverageThreshold":{"global":{"branches":85,"functions":85,"lines":85,"statements":85},"./src/common/":{"branches":90,"functions":90,"lines":90,"statements":90},"./src/modules/auth/":{"branches":90,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":true,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":true,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":6,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[{"displayName":"unit","testMatch":["<rootDir>/src/**/__tests__/**/*.unit.{ts,js}","<rootDir>/src/**/*.unit.(test|spec).{ts,js}"],"testEnvironment":"node","rootDir":"C:\\PeopleNest"},{"displayName":"integration","testMatch":["<rootDir>/src/**/__tests__/**/*.integration.{ts,js}","<rootDir>/src/**/*.integration.(test|spec).{ts,js}","<rootDir>/test/integration/**/*.{ts,js}"],"testEnvironment":"node","setupFilesAfterEnv":["<rootDir>/test/setup.ts","<rootDir>/test/integration-setup.ts"],"rootDir":"C:\\PeopleNest"},{"displayName":"e2e","testMatch":["<rootDir>/test/e2e/**/*.{ts,js}","<rootDir>/src/**/*.e2e.(test|spec).{ts,js}"],"testEnvironment":"node","setupFilesAfterEnv":["<rootDir>/test/setup.ts","<rootDir>/test/e2e-setup.ts"],"rootDir":"C:\\PeopleNest"}],"reporters":[["default",{}],["C:\\PeopleNest\\node_modules\\jest-junit\\index.js",{"outputDirectory":"<rootDir>/coverage","outputName":"junit.xml","classNameTemplate":"{classname}","titleTemplate":"{title}","ancestorSeparator":" › ","usePathForSuiteName":true}],["C:\\PeopleNest\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"<rootDir>/coverage/html-report","filename":"report.html","expand":true}]],"rootDir":"C:\\PeopleNest","runTestsByPath":false,"seed":-809145166,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"","testResultsProcessor":"C:\\PeopleNest\\node_modules\\jest-sonar-reporter\\index.js","testSequencer":"C:\\PeopleNest\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchPlugins":[{"config":{},"path":"C:\\PeopleNest\\node_modules\\jest-watch-typeahead\\build\\file_name_plugin\\plugin.js"},{"config":{},"path":"C:\\PeopleNest\\node_modules\\jest-watch-typeahead\\build\\test_name_plugin\\plugin.js"}],"watchman":true,"workerThreads":false,"coverageLinkPath":"..\\lcov-report\\index.html"},"endTime":1751361655550,"_reporterOptions":{"publicPath":"C:\\PeopleNest/coverage/html-report","filename":"report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})