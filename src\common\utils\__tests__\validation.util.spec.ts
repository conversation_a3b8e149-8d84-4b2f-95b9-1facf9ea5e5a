import { ValidationUtil } from '../validation.util';

describe('ValidationUtil', () => {
  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(ValidationUtil.isValidEmail('<EMAIL>')).toBe(true);
      expect(ValidationUtil.isValidEmail('<EMAIL>')).toBe(true);
      expect(ValidationUtil.isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(ValidationUtil.isValidEmail('invalid-email')).toBe(false);
      expect(ValidationUtil.isValidEmail('test@')).toBe(false);
      expect(ValidationUtil.isValidEmail('@example.com')).toBe(false);
      expect(ValidationUtil.isValidEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('isValidPhoneNumber', () => {
    it('should validate correct phone numbers', () => {
      expect(ValidationUtil.isValidPhoneNumber('+1234567890')).toBe(true);
      expect(ValidationUtil.isValidPhoneNumber('+44 20 7946 0958')).toBe(true);
      expect(ValidationUtil.isValidPhoneNumber('+91-98765-43210')).toBe(true);
    });

    it('should reject invalid phone numbers', () => {
      expect(ValidationUtil.isValidPhoneNumber('123')).toBe(false);
      expect(ValidationUtil.isValidPhoneNumber('abc123')).toBe(false);
      expect(ValidationUtil.isValidPhoneNumber('+123456789012345678')).toBe(false);
    });
  });

  describe('validatePasswordStrength', () => {
    it('should validate strong passwords', () => {
      const result = ValidationUtil.validatePasswordStrength('StrongP@ssw0rd');
      expect(result.isValid).toBe(true);
      expect(result.score).toBe(5);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject weak passwords', () => {
      const result = ValidationUtil.validatePasswordStrength('weak');
      expect(result.isValid).toBe(false);
      expect(result.score).toBeLessThan(5);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should detect common patterns', () => {
      const result = ValidationUtil.validatePasswordStrength('Password123');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('common patterns'))).toBe(true);
    });
  });

  describe('isValidUUID', () => {
    it('should validate correct UUIDs', () => {
      expect(ValidationUtil.isValidUUID('123e4567-e89b-12d3-a456-************')).toBe(true);
      expect(ValidationUtil.isValidUUID('550e8400-e29b-41d4-a716-************')).toBe(true);
    });

    it('should reject invalid UUIDs', () => {
      expect(ValidationUtil.isValidUUID('invalid-uuid')).toBe(false);
      expect(ValidationUtil.isValidUUID('123e4567-e89b-12d3-a456')).toBe(false);
      expect(ValidationUtil.isValidUUID('123e4567-e89b-12d3-a456-42661417400g')).toBe(false);
    });
  });

  describe('isValidTenantId', () => {
    it('should validate correct tenant IDs', () => {
      expect(ValidationUtil.isValidTenantId('company-123')).toBe(true);
      expect(ValidationUtil.isValidTenantId('tenant_abc')).toBe(true);
      expect(ValidationUtil.isValidTenantId('org123')).toBe(true);
    });

    it('should reject invalid tenant IDs', () => {
      expect(ValidationUtil.isValidTenantId('ab')).toBe(false); // Too short
      expect(ValidationUtil.isValidTenantId('a'.repeat(51))).toBe(false); // Too long
      expect(ValidationUtil.isValidTenantId('tenant@123')).toBe(false); // Invalid characters
    });
  });

  describe('isValidEmployeeId', () => {
    it('should validate correct employee IDs', () => {
      expect(ValidationUtil.isValidEmployeeId('EMP123')).toBe(true);
      expect(ValidationUtil.isValidEmployeeId('12345')).toBe(true);
      expect(ValidationUtil.isValidEmployeeId('ABC123XYZ')).toBe(true);
    });

    it('should reject invalid employee IDs', () => {
      expect(ValidationUtil.isValidEmployeeId('AB')).toBe(false); // Too short
      expect(ValidationUtil.isValidEmployeeId('A'.repeat(21))).toBe(false); // Too long
      expect(ValidationUtil.isValidEmployeeId('EMP-123')).toBe(false); // Invalid characters
    });
  });

  describe('isValidDateString', () => {
    it('should validate correct date strings', () => {
      expect(ValidationUtil.isValidDateString('2023-12-25')).toBe(true);
      expect(ValidationUtil.isValidDateString('2024-02-29')).toBe(true); // Leap year
    });

    it('should reject invalid date strings', () => {
      expect(ValidationUtil.isValidDateString('2023-13-01')).toBe(false); // Invalid month
      expect(ValidationUtil.isValidDateString('2023-02-30')).toBe(false); // Invalid day
      expect(ValidationUtil.isValidDateString('invalid-date')).toBe(false);
    });
  });

  describe('isValidDateRange', () => {
    it('should validate correct date ranges', () => {
      expect(ValidationUtil.isValidDateRange('2023-01-01', '2023-12-31')).toBe(true);
      expect(ValidationUtil.isValidDateRange('2023-06-15', '2023-06-15')).toBe(true); // Same date
    });

    it('should reject invalid date ranges', () => {
      expect(ValidationUtil.isValidDateRange('2023-12-31', '2023-01-01')).toBe(false);
      expect(ValidationUtil.isValidDateRange('invalid', '2023-12-31')).toBe(false);
    });
  });

  describe('isValidCurrencyCode', () => {
    it('should validate correct currency codes', () => {
      expect(ValidationUtil.isValidCurrencyCode('USD')).toBe(true);
      expect(ValidationUtil.isValidCurrencyCode('EUR')).toBe(true);
      expect(ValidationUtil.isValidCurrencyCode('GBP')).toBe(true);
      expect(ValidationUtil.isValidCurrencyCode('usd')).toBe(true); // Case insensitive
    });

    it('should reject invalid currency codes', () => {
      expect(ValidationUtil.isValidCurrencyCode('INVALID')).toBe(false);
      expect(ValidationUtil.isValidCurrencyCode('US')).toBe(false);
      expect(ValidationUtil.isValidCurrencyCode('USDD')).toBe(false);
    });
  });

  describe('isValidSalaryAmount', () => {
    it('should validate reasonable salary amounts', () => {
      expect(ValidationUtil.isValidSalaryAmount(50000, 'USD')).toBe(true);
      expect(ValidationUtil.isValidSalaryAmount(100000, 'EUR')).toBe(true);
      expect(ValidationUtil.isValidSalaryAmount(5000000, 'JPY')).toBe(true);
    });

    it('should reject unreasonable salary amounts', () => {
      expect(ValidationUtil.isValidSalaryAmount(-1000, 'USD')).toBe(false); // Negative
      expect(ValidationUtil.isValidSalaryAmount(20000000, 'USD')).toBe(false); // Too high
    });
  });

  describe('sanitizeString', () => {
    it('should sanitize strings correctly', () => {
      expect(ValidationUtil.sanitizeString('  hello world  ')).toBe('hello world');
      expect(ValidationUtil.sanitizeString('test<script>alert("xss")</script>')).toBe('testscriptalert("xss")/script');
      expect(ValidationUtil.sanitizeString('text\x00with\x1Fcontrol\x7Fchars')).toBe('textwithcontrolchars');
    });

    it('should handle edge cases', () => {
      expect(ValidationUtil.sanitizeString('')).toBe('');
      expect(ValidationUtil.sanitizeString(null as any)).toBe('');
      expect(ValidationUtil.sanitizeString(undefined as any)).toBe('');
    });
  });

  describe('validateFileUpload', () => {
    const createMockFile = (name: string, size: number, mimetype: string) => ({
      originalname: name,
      size,
      mimetype,
    });

    it('should validate correct file uploads', () => {
      const file = createMockFile('test.jpg', 1024 * 1024, 'image/jpeg'); // 1MB JPEG
      const result = ValidationUtil.validateFileUpload(file);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject files that are too large', () => {
      const file = createMockFile('large.jpg', 20 * 1024 * 1024, 'image/jpeg'); // 20MB
      const result = ValidationUtil.validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('size exceeds'))).toBe(true);
    });

    it('should reject files with invalid MIME types', () => {
      const file = createMockFile('test.exe', 1024, 'application/x-executable');
      const result = ValidationUtil.validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('type'))).toBe(true);
    });

    it('should reject files with invalid extensions', () => {
      const file = createMockFile('test.exe', 1024, 'image/jpeg'); // Wrong extension
      const result = ValidationUtil.validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('extension'))).toBe(true);
    });
  });

  describe('isValidJSON', () => {
    it('should validate correct JSON strings', () => {
      expect(ValidationUtil.isValidJSON('{"key": "value"}')).toBe(true);
      expect(ValidationUtil.isValidJSON('[]')).toBe(true);
      expect(ValidationUtil.isValidJSON('null')).toBe(true);
      expect(ValidationUtil.isValidJSON('123')).toBe(true);
    });

    it('should reject invalid JSON strings', () => {
      expect(ValidationUtil.isValidJSON('{"key": value}')).toBe(false); // Unquoted value
      expect(ValidationUtil.isValidJSON('{"key"}')).toBe(false); // Missing value
      expect(ValidationUtil.isValidJSON('invalid json')).toBe(false);
    });
  });

  describe('isValidURL', () => {
    it('should validate correct URLs', () => {
      expect(ValidationUtil.isValidURL('https://example.com')).toBe(true);
      expect(ValidationUtil.isValidURL('http://localhost:3000')).toBe(true);
      expect(ValidationUtil.isValidURL('ftp://files.example.com')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(ValidationUtil.isValidURL('not-a-url')).toBe(false);
      expect(ValidationUtil.isValidURL('http://')).toBe(false);
      expect(ValidationUtil.isValidURL('://example.com')).toBe(false);
    });
  });

  describe('isValidIPAddress', () => {
    it('should validate correct IPv4 addresses', () => {
      expect(ValidationUtil.isValidIPAddress('***********')).toBe(true);
      expect(ValidationUtil.isValidIPAddress('********')).toBe(true);
      expect(ValidationUtil.isValidIPAddress('***************')).toBe(true);
    });

    it('should validate correct IPv6 addresses', () => {
      expect(ValidationUtil.isValidIPAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);
    });

    it('should reject invalid IP addresses', () => {
      expect(ValidationUtil.isValidIPAddress('256.1.1.1')).toBe(false); // Invalid IPv4
      expect(ValidationUtil.isValidIPAddress('192.168.1')).toBe(false); // Incomplete IPv4
      expect(ValidationUtil.isValidIPAddress('not-an-ip')).toBe(false);
    });
  });

  describe('isValidTimeFormat', () => {
    it('should validate correct time formats', () => {
      expect(ValidationUtil.isValidTimeFormat('09:30')).toBe(true);
      expect(ValidationUtil.isValidTimeFormat('23:59')).toBe(true);
      expect(ValidationUtil.isValidTimeFormat('00:00:00')).toBe(true);
      expect(ValidationUtil.isValidTimeFormat('12:30:45')).toBe(true);
    });

    it('should reject invalid time formats', () => {
      expect(ValidationUtil.isValidTimeFormat('25:00')).toBe(false); // Invalid hour
      expect(ValidationUtil.isValidTimeFormat('12:60')).toBe(false); // Invalid minute
      expect(ValidationUtil.isValidTimeFormat('12:30:60')).toBe(false); // Invalid second
      expect(ValidationUtil.isValidTimeFormat('not-a-time')).toBe(false);
    });
  });

  describe('isValidTimezone', () => {
    it('should validate correct timezones', () => {
      expect(ValidationUtil.isValidTimezone('America/New_York')).toBe(true);
      expect(ValidationUtil.isValidTimezone('Europe/London')).toBe(true);
      expect(ValidationUtil.isValidTimezone('UTC')).toBe(true);
    });

    it('should reject invalid timezones', () => {
      expect(ValidationUtil.isValidTimezone('Invalid/Timezone')).toBe(false);
      expect(ValidationUtil.isValidTimezone('not-a-timezone')).toBe(false);
    });
  });
});
