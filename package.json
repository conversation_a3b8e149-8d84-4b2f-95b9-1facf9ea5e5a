{"name": "peoplenest-hrms", "version": "1.0.0", "description": "AI-Enabled Enterprise HRMS Platform - PeopleNest", "author": "PeopleNest Development Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:e2e": "jest --selectProjects e2e", "test:all": "jest --selectProjects unit integration e2e", "test:ci": "jest --ci --coverage --watchAll=false", "test:coverage": "jest --coverage --coverageReporters=text-lcov", "test:coverage-html": "jest --coverage --coverageReporters=html", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert", "seed": "ts-node src/database/seeds/index.ts"}, "dependencies": {"@apollo/server": "^4.7.4", "@nestjs/apollo": "^12.0.0", "@nestjs/cache-manager": "^2.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^4.2.1", "@nestjs/typeorm": "^10.0.0", "@types/compression": "^1.8.1", "@types/pg": "^8.15.4", "@types/redis": "^4.0.10", "apollo-server-express": "^3.12.0", "bcrypt": "^5.1.0", "cache-manager": "^5.2.3", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "graphql": "^16.7.1", "helmet": "^7.0.0", "kafkajs": "^2.2.4", "nest-winston": "^1.9.4", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-oauth2": "^1.7.0", "pg": "^8.16.3", "redis": "^4.7.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.17", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "jest-watch-typeahead": "^2.2.2", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "webpack": "^5.99.9"}}