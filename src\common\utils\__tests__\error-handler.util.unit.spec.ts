import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AppError, ErrorCode } from '../error-handler.util';
import { HttpException, BadRequestException, UnauthorizedException, NotFoundException, ConflictException, InternalServerErrorException, HttpStatus } from '@nestjs/common';
import { QueryFailedError } from 'typeorm';

describe('ErrorHandler (Unit)', () => {
  describe('AppError', () => {
    it('should create an AppError with correct properties', () => {
      const error = new AppError(
        'Test validation error',
        ErrorCode.VALIDATION_FAILED,
        HttpStatus.BAD_REQUEST,
        true,
        { field: 'email' },
        'test-correlation-id'
      );

      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Test validation error');
      expect(error.code).toBe(ErrorCode.VALIDATION_FAILED);
      expect(error.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(error.isOperational).toBe(true);
      expect(error.context).toEqual({ field: 'email' });
      expect(error.correlationId).toBe('test-correlation-id');
    });

    it('should create an AppError with default values', () => {
      const error = new AppError('Test error', ErrorCode.INTERNAL_ERROR);

      expect(error.message).toBe('Test error');
      expect(error.code).toBe(ErrorCode.INTERNAL_ERROR);
      expect(error.statusCode).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(error.isOperational).toBe(true);
      expect(error.context).toBeUndefined();
      expect(error.correlationId).toBeUndefined();
    });
  });

  describe('createBusinessError', () => {
    it('should create a business error with correct properties', () => {
      const error = ErrorHandler.createBusinessError(
        'Business rule violated',
        ErrorCode.BUSINESS_RULE_VIOLATION,
        { rule: 'max_employees' },
        'test-correlation-id'
      );

      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Business rule violated');
      expect(error.code).toBe(ErrorCode.BUSINESS_RULE_VIOLATION);
      expect(error.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(error.context).toEqual({ rule: 'max_employees' });
      expect(error.correlationId).toBe('test-correlation-id');
    });

    it('should use default error code when not provided', () => {
      const error = ErrorHandler.createBusinessError('Business rule violated');
      expect(error.code).toBe(ErrorCode.BUSINESS_RULE_VIOLATION);
    });
  });

  describe('createNotFoundError', () => {
    it('should create a not found error with identifier', () => {
      const error = ErrorHandler.createNotFoundError('User', '123', 'test-correlation-id');

      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe("User with identifier '123' not found");
      expect(error.code).toBe(ErrorCode.RESOURCE_NOT_FOUND);
      expect(error.statusCode).toBe(HttpStatus.NOT_FOUND);
      expect(error.context).toEqual({ resource: 'User', identifier: '123' });
      expect(error.correlationId).toBe('test-correlation-id');
    });

    it('should create a not found error without identifier', () => {
      const error = ErrorHandler.createNotFoundError('User');
      expect(error.message).toBe('User not found');
      expect(error.context).toEqual({ resource: 'User', identifier: undefined });
    });
  });

  describe('createUnauthorizedError', () => {
    it('should create an unauthorized error', () => {
      const error = ErrorHandler.createUnauthorizedError('Access denied', ErrorCode.INSUFFICIENT_PERMISSIONS, 'test-correlation-id');

      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Access denied');
      expect(error.code).toBe(ErrorCode.INSUFFICIENT_PERMISSIONS);
      expect(error.statusCode).toBe(HttpStatus.UNAUTHORIZED);
      expect(error.correlationId).toBe('test-correlation-id');
    });

    it('should use default values when not provided', () => {
      const error = ErrorHandler.createUnauthorizedError();
      expect(error.message).toBe('Unauthorized access');
      expect(error.code).toBe(ErrorCode.INSUFFICIENT_PERMISSIONS);
    });
  });

  describe('createForbiddenError', () => {
    it('should create a forbidden error', () => {
      const error = ErrorHandler.createForbiddenError('Access forbidden', ErrorCode.INSUFFICIENT_PERMISSIONS, 'test-correlation-id');

      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Access forbidden');
      expect(error.code).toBe(ErrorCode.INSUFFICIENT_PERMISSIONS);
      expect(error.statusCode).toBe(HttpStatus.FORBIDDEN);
      expect(error.correlationId).toBe('test-correlation-id');
    });
  });

  describe('createValidationError', () => {
    it('should create a validation error', () => {
      const errors = ['Field is required', 'Invalid format'];
      const error = ErrorHandler.createValidationError('Validation failed', errors, 'test-correlation-id');

      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Validation failed');
      expect(error.code).toBe(ErrorCode.VALIDATION_FAILED);
      expect(error.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(error.context).toEqual({ errors });
      expect(error.correlationId).toBe('test-correlation-id');
    });
  });

  describe('handleError', () => {
    it('should pass through HttpExceptions unchanged', () => {
      const httpException = new BadRequestException('Test error');
      const result = ErrorHandler.handleError(httpException);
      expect(result).toBe(httpException);
    });

    it('should convert AppError to HttpException', () => {
      const appError = new AppError(
        'Test error',
        ErrorCode.VALIDATION_FAILED,
        HttpStatus.BAD_REQUEST,
        true,
        { field: 'email' },
        'correlation-123'
      );

      const result = ErrorHandler.handleError(appError, 'correlation-123');
      expect(result).toBeInstanceOf(BadRequestException);
      expect(result.getStatus()).toBe(HttpStatus.BAD_REQUEST);

      const response = result.getResponse() as any;
      expect(response.message).toBe('Test error');
      expect(response.code).toBe(ErrorCode.VALIDATION_FAILED);
      expect(response.context).toEqual({ field: 'email' });
      expect(response.correlationId).toBe('correlation-123');
    });

    it('should handle QueryFailedError', () => {
      const dbError = new QueryFailedError('SELECT * FROM users', [], new Error('Connection failed'));
      (dbError as any).driverError = {
        code: '23505',
        detail: 'Key (email)=(<EMAIL>) already exists.',
      };

      const result = ErrorHandler.handleError(dbError);
      expect(result).toBeInstanceOf(ConflictException);
      expect(result.getStatus()).toBe(HttpStatus.CONFLICT);

      const response = result.getResponse() as any;
      expect(response.code).toBe(ErrorCode.DUPLICATE_ENTRY);
    });

    it('should handle validation errors', () => {
      const validationError = {
        name: 'ValidationError',
        message: 'Validation failed',
        errors: ['Field is required', 'Invalid format'],
      };

      const result = ErrorHandler.handleError(validationError);
      expect(result).toBeInstanceOf(BadRequestException);
      expect(result.getStatus()).toBe(HttpStatus.BAD_REQUEST);

      const response = result.getResponse() as any;
      expect(response.message).toBe('Validation failed');
      expect(response.code).toBe(ErrorCode.VALIDATION_FAILED);
      expect(response.errors).toEqual(['Field is required', 'Invalid format']);
    });

    it('should handle JWT errors', () => {
      const jwtError = {
        name: 'JsonWebTokenError',
        message: 'Invalid token',
      };

      const result = ErrorHandler.handleError(jwtError);
      expect(result).toBeInstanceOf(UnauthorizedException);
      expect(result.getStatus()).toBe(HttpStatus.UNAUTHORIZED);

      const response = result.getResponse() as any;
      expect(response.message).toBe('Invalid token');
      expect(response.code).toBe(ErrorCode.TOKEN_INVALID);
    });

    it('should handle token expiration errors', () => {
      const expiredError = {
        name: 'TokenExpiredError',
        message: 'Token expired',
      };

      const result = ErrorHandler.handleError(expiredError);
      expect(result).toBeInstanceOf(UnauthorizedException);
      expect(result.getStatus()).toBe(HttpStatus.UNAUTHORIZED);

      const response = result.getResponse() as any;
      expect(response.message).toBe('Token expired');
      expect(response.code).toBe(ErrorCode.TOKEN_EXPIRED);
    });

    it('should handle timeout errors', () => {
      const timeoutError = {
        name: 'TimeoutError',
        message: 'Operation timed out',
      };

      const result = ErrorHandler.handleError(timeoutError);
      expect(result).toBeInstanceOf(InternalServerErrorException);
      expect(result.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);

      const response = result.getResponse() as any;
      expect(response.message).toBe('Request timeout');
      expect(response.code).toBe(ErrorCode.TIMEOUT_ERROR);
    });

    it('should handle unknown errors', () => {
      const unknownError = new Error('Unknown error');

      const result = ErrorHandler.handleError(unknownError);
      expect(result).toBeInstanceOf(InternalServerErrorException);
      expect(result.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);

      const response = result.getResponse() as any;
      expect(response.message).toBe('Unknown error'); // In non-production, shows actual error message
      expect(response.code).toBe(ErrorCode.INTERNAL_ERROR);
    });
  });

  describe('isOperationalError', () => {
    it('should return true for AppError instances', () => {
      const appError = new AppError('Test error', ErrorCode.VALIDATION_FAILED);
      expect(ErrorHandler.isOperationalError(appError)).toBe(true);
    });

    it('should return true for HttpException instances', () => {
      const httpException = new BadRequestException('Test error');
      expect(ErrorHandler.isOperationalError(httpException)).toBe(true);
    });

    it('should return false for regular Error instances', () => {
      const error = new Error('Test error');
      expect(ErrorHandler.isOperationalError(error)).toBe(false);
    });

    it('should return false for programming errors', () => {
      const error = new TypeError('Cannot read property of undefined');
      expect(ErrorHandler.isOperationalError(error)).toBe(false);
    });
  });
});
