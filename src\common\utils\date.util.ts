/**
 * Date utility functions for common date operations
 */
export class DateUtil {
  /**
   * Get current date in ISO format (YYYY-MM-DD)
   */
  static getCurrentDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Get current timestamp in ISO format
   */
  static getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * Format date to ISO date string (YYYY-MM-DD)
   */
  static formatToISODate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toISOString().split('T')[0];
  }

  /**
   * Format date to ISO datetime string
   */
  static formatToISODateTime(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toISOString();
  }

  /**
   * Parse date string to Date object
   */
  static parseDate(dateString: string): Date {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date string: ${dateString}`);
    }
    return date;
  }

  /**
   * Check if date is valid
   */
  static isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Check if date string is valid
   */
  static isValidDateString(dateString: string): boolean {
    try {
      const date = new Date(dateString);
      return this.isValidDate(date);
    } catch {
      return false;
    }
  }

  /**
   * Add days to a date
   */
  static addDays(date: Date | string, days: number): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setDate(d.getDate() + days);
    return d;
  }

  /**
   * Add months to a date
   */
  static addMonths(date: Date | string, months: number): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setMonth(d.getMonth() + months);
    return d;
  }

  /**
   * Add years to a date
   */
  static addYears(date: Date | string, years: number): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setFullYear(d.getFullYear() + years);
    return d;
  }

  /**
   * Subtract days from a date
   */
  static subtractDays(date: Date | string, days: number): Date {
    return this.addDays(date, -days);
  }

  /**
   * Subtract months from a date
   */
  static subtractMonths(date: Date | string, months: number): Date {
    return this.addMonths(date, -months);
  }

  /**
   * Subtract years from a date
   */
  static subtractYears(date: Date | string, years: number): Date {
    return this.addYears(date, -years);
  }

  /**
   * Get difference between two dates in days
   */
  static getDifferenceInDays(date1: Date | string, date2: Date | string): number {
    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Get difference between two dates in hours
   */
  static getDifferenceInHours(date1: Date | string, date2: Date | string): number {
    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60));
  }

  /**
   * Get difference between two dates in minutes
   */
  static getDifferenceInMinutes(date1: Date | string, date2: Date | string): number {
    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60));
  }

  /**
   * Check if date is in the past
   */
  static isInPast(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d < new Date();
  }

  /**
   * Check if date is in the future
   */
  static isInFuture(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d > new Date();
  }

  /**
   * Check if date is today
   */
  static isToday(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return d.toDateString() === today.toDateString();
  }

  /**
   * Check if date is weekend
   */
  static isWeekend(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    const day = d.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  /**
   * Check if date is weekday
   */
  static isWeekday(date: Date | string): boolean {
    return !this.isWeekend(date);
  }

  /**
   * Get start of day
   */
  static getStartOfDay(date: Date | string): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Get end of day
   */
  static getEndOfDay(date: Date | string): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setHours(23, 59, 59, 999);
    return d;
  }

  /**
   * Get start of month
   */
  static getStartOfMonth(date: Date | string): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    return new Date(d.getFullYear(), d.getMonth(), 1);
  }

  /**
   * Get end of month
   */
  static getEndOfMonth(date: Date | string): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    return new Date(d.getFullYear(), d.getMonth() + 1, 0, 23, 59, 59, 999);
  }

  /**
   * Get start of year
   */
  static getStartOfYear(date: Date | string): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    return new Date(d.getFullYear(), 0, 1);
  }

  /**
   * Get end of year
   */
  static getEndOfYear(date: Date | string): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    return new Date(d.getFullYear(), 11, 31, 23, 59, 59, 999);
  }

  /**
   * Get age from birth date
   */
  static getAge(birthDate: Date | string): number {
    const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Format date for display
   */
  static formatForDisplay(
    date: Date | string,
    locale: string = 'en-US',
    options: Intl.DateTimeFormatOptions = {},
  ): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    
    return d.toLocaleDateString(locale, { ...defaultOptions, ...options });
  }

  /**
   * Format time for display
   */
  static formatTimeForDisplay(
    date: Date | string,
    locale: string = 'en-US',
    options: Intl.DateTimeFormatOptions = {},
  ): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    const defaultOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
    };
    
    return d.toLocaleTimeString(locale, { ...defaultOptions, ...options });
  }

  /**
   * Get business days between two dates (excluding weekends)
   */
  static getBusinessDaysBetween(startDate: Date | string, endDate: Date | string): number {
    const start = typeof startDate === 'string' ? new Date(startDate) : new Date(startDate);
    const end = typeof endDate === 'string' ? new Date(endDate) : new Date(endDate);
    
    let businessDays = 0;
    const currentDate = new Date(start);
    
    while (currentDate <= end) {
      if (this.isWeekday(currentDate)) {
        businessDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return businessDays;
  }

  /**
   * Get next business day
   */
  static getNextBusinessDay(date: Date | string): Date {
    let nextDay = this.addDays(date, 1);
    
    while (this.isWeekend(nextDay)) {
      nextDay = this.addDays(nextDay, 1);
    }
    
    return nextDay;
  }

  /**
   * Get previous business day
   */
  static getPreviousBusinessDay(date: Date | string): Date {
    let prevDay = this.subtractDays(date, 1);
    
    while (this.isWeekend(prevDay)) {
      prevDay = this.subtractDays(prevDay, 1);
    }
    
    return prevDay;
  }

  /**
   * Convert date to timezone
   */
  static convertToTimezone(date: Date | string, timezone: string): Date {
    const d = typeof date === 'string' ? new Date(date) : date;
    return new Date(d.toLocaleString('en-US', { timeZone: timezone }));
  }

  /**
   * Get timezone offset in minutes
   */
  static getTimezoneOffset(timezone: string): number {
    const date = new Date();
    const utc = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
    const target = new Date(utc.toLocaleString('en-US', { timeZone: timezone }));
    return (target.getTime() - utc.getTime()) / 60000;
  }

  /**
   * Check if year is leap year
   */
  static isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
  }

  /**
   * Get days in month
   */
  static getDaysInMonth(year: number, month: number): number {
    return new Date(year, month, 0).getDate();
  }

  /**
   * Get quarter from date
   */
  static getQuarter(date: Date | string): number {
    const d = typeof date === 'string' ? new Date(date) : date;
    return Math.floor((d.getMonth() + 3) / 3);
  }

  /**
   * Get week number of year
   */
  static getWeekNumber(date: Date | string): number {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    const firstDayOfYear = new Date(d.getFullYear(), 0, 1);
    const pastDaysOfYear = (d.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }
}
