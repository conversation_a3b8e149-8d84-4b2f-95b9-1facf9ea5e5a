import { Logger } from '@nestjs/common';

/**
 * Performance monitoring decorator options
 */
export interface PerformanceOptions {
  logLevel?: 'debug' | 'log' | 'warn';
  threshold?: number; // Log warning if execution time exceeds threshold (ms)
  includeArgs?: boolean;
  includeResult?: boolean;
  context?: string;
}

/**
 * Decorator to monitor method performance
 */
export function Performance(options: PerformanceOptions = {}) {
  const {
    logLevel = 'debug',
    threshold = 1000,
    includeArgs = false,
    includeResult = false,
    context = 'Performance',
  } = options;

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const logger = new Logger(context);

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const methodName = `${target.constructor.name}.${propertyName}`;

      try {
        // Log method start
        const logData: any = {
          method: methodName,
          timestamp: new Date().toISOString(),
        };

        if (includeArgs) {
          logData.arguments = args;
        }

        logger[logLevel](`Starting ${methodName}`, logData);

        // Execute the method
        const result = await method.apply(this, args);
        const executionTime = Date.now() - startTime;

        // Log method completion
        const completionLogData: any = {
          method: methodName,
          executionTime: `${executionTime}ms`,
          timestamp: new Date().toISOString(),
        };

        if (includeResult) {
          completionLogData.result = result;
        }

        // Use appropriate log level based on execution time
        const finalLogLevel = executionTime > threshold ? 'warn' : logLevel;
        const message = executionTime > threshold 
          ? `${methodName} completed (SLOW - ${executionTime}ms)`
          : `${methodName} completed (${executionTime}ms)`;

        logger[finalLogLevel](message, completionLogData);

        return result;
      } catch (error) {
        const executionTime = Date.now() - startTime;
        
        logger.error(`${methodName} failed after ${executionTime}ms`, {
          method: methodName,
          executionTime: `${executionTime}ms`,
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
          },
          timestamp: new Date().toISOString(),
        });

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to cache method results
 */
export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  key?: string | ((args: any[]) => string); // Cache key generator
  context?: string;
}

const cache = new Map<string, { value: any; expiry: number }>();

export function Cache(options: CacheOptions = {}) {
  const {
    ttl = 300000, // 5 minutes default
    key,
    context = 'Cache',
  } = options;

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const logger = new Logger(context);

    descriptor.value = async function (...args: any[]) {
      const methodName = `${target.constructor.name}.${propertyName}`;
      
      // Generate cache key
      let cacheKey: string;
      if (typeof key === 'function') {
        cacheKey = key(args);
      } else if (typeof key === 'string') {
        cacheKey = key;
      } else {
        cacheKey = `${methodName}:${JSON.stringify(args)}`;
      }

      // Check cache
      const cached = cache.get(cacheKey);
      if (cached && cached.expiry > Date.now()) {
        logger.debug(`Cache hit for ${methodName}`, { cacheKey });
        return cached.value;
      }

      // Execute method
      const result = await method.apply(this, args);

      // Store in cache
      cache.set(cacheKey, {
        value: result,
        expiry: Date.now() + ttl,
      });

      logger.debug(`Cache miss for ${methodName}, result cached`, { cacheKey, ttl });

      return result;
    };

    return descriptor;
  };
}

/**
 * Decorator to retry failed method calls
 */
export interface RetryOptions {
  attempts?: number;
  delay?: number; // Delay between retries in milliseconds
  backoff?: 'fixed' | 'exponential';
  retryCondition?: (error: any) => boolean;
  context?: string;
}

export function Retry(options: RetryOptions = {}) {
  const {
    attempts = 3,
    delay = 1000,
    backoff = 'fixed',
    retryCondition = () => true,
    context = 'Retry',
  } = options;

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const logger = new Logger(context);

    descriptor.value = async function (...args: any[]) {
      const methodName = `${target.constructor.name}.${propertyName}`;
      let lastError: any;

      for (let attempt = 1; attempt <= attempts; attempt++) {
        try {
          const result = await method.apply(this, args);
          
          if (attempt > 1) {
            logger.log(`${methodName} succeeded on attempt ${attempt}/${attempts}`);
          }
          
          return result;
        } catch (error) {
          lastError = error;

          if (attempt === attempts || !retryCondition(error)) {
            logger.error(`${methodName} failed after ${attempt} attempts`, {
              method: methodName,
              attempts: attempt,
              error: {
                name: error.name,
                message: error.message,
              },
            });
            throw error;
          }

          const currentDelay = backoff === 'exponential' 
            ? delay * Math.pow(2, attempt - 1)
            : delay;

          logger.warn(`${methodName} failed on attempt ${attempt}/${attempts}, retrying in ${currentDelay}ms`, {
            method: methodName,
            attempt,
            totalAttempts: attempts,
            delay: currentDelay,
            error: {
              name: error.name,
              message: error.message,
            },
          });

          await new Promise(resolve => setTimeout(resolve, currentDelay));
        }
      }

      throw lastError;
    };

    return descriptor;
  };
}

/**
 * Decorator to add timeout to method execution
 */
export interface TimeoutOptions {
  timeout: number; // Timeout in milliseconds
  context?: string;
}

export function Timeout(options: TimeoutOptions) {
  const { timeout, context = 'Timeout' } = options;

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const logger = new Logger(context);

    descriptor.value = async function (...args: any[]) {
      const methodName = `${target.constructor.name}.${propertyName}`;

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`${methodName} timed out after ${timeout}ms`));
        }, timeout);
      });

      try {
        const result = await Promise.race([
          method.apply(this, args),
          timeoutPromise,
        ]);

        return result;
      } catch (error) {
        if (error.message.includes('timed out')) {
          logger.error(`${methodName} timed out`, {
            method: methodName,
            timeout,
            timestamp: new Date().toISOString(),
          });
        }
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to rate limit method calls
 */
export interface RateLimitOptions {
  maxCalls: number;
  windowMs: number; // Time window in milliseconds
  context?: string;
}

const rateLimitCounters = new Map<string, { count: number; resetTime: number }>();

export function RateLimit(options: RateLimitOptions) {
  const { maxCalls, windowMs, context = 'RateLimit' } = options;

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const logger = new Logger(context);

    descriptor.value = async function (...args: any[]) {
      const methodName = `${target.constructor.name}.${propertyName}`;
      const now = Date.now();
      
      let counter = rateLimitCounters.get(methodName);
      
      if (!counter || now > counter.resetTime) {
        counter = { count: 0, resetTime: now + windowMs };
        rateLimitCounters.set(methodName, counter);
      }

      if (counter.count >= maxCalls) {
        const resetIn = counter.resetTime - now;
        logger.warn(`Rate limit exceeded for ${methodName}`, {
          method: methodName,
          maxCalls,
          windowMs,
          resetIn,
        });
        
        throw new Error(`Rate limit exceeded for ${methodName}. Try again in ${resetIn}ms`);
      }

      counter.count++;
      
      return await method.apply(this, args);
    };

    return descriptor;
  };
}
