"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var e=["2#######","(371) 2#######","+371 2#######"];var X={formats:e},i=X;var t=["balta","debess-zila","dzeltena","gai\u0161zila","kr\u0113ma","lill\u0101","melna","ol\u012Bv","oran\u017Ea","pel\u0113ka","purpura","roz\u0101","ruda","sarkan-br\u016Bna","sarkana","sudrabaina","violeta","za\u013Ca","za\u013Cgan-zila","zelta","zeltaina","zila","\u043A\u0440\u0430\u0441\u043D\u043E-\u043F\u0443\u0440\u043F\u0443\u0440\u043D\u044B\u0439"];var Y={human:t},o=Y;var n=["Apavu","Ap\u0123erba","Auto","Bakaleja","B\u0113rnu","Datoru","D\u0101rglietu","D\u0101rzkop\u012Bbas","Elektronikas","Filmu","Gr\u0101matu","M\u0101jas","M\u016Bzikas","Rota\u013Clietu","R\u016Bpniec\u012Bbas","Skaistuma","Sporta","Sp\u0113\u013Cu","T\u016Brisma","Vesel\u012Bbas","Z\u012Bdai\u0146u"];var u={adjective:["Ciets","Ergonomisks","Fantastisks","Lielisks","Mazs","M\u012Bksts","Neticams","Praktisks","Raupj\u0161","R\u016Bgts","Spalvains","Vieds"],material:["betona","dzelzs","gran\u012Bta","gumijas","koka","kokvilnas","plastmasas"],product:["auto","ce\u013Carullis","dators","d\u017Eemperis","galds","gredzens","ka\u017Eoks","kr\u0113sls","monitors","nazis","plak\u0101ts","pl\u0101ksteris","pon\u010Diks","radio","ritenis","siksna","skapis","ska\u013Crunis","telefons","televizors","z\u0101baks"]};var $={department:n,product_name:u},l=$;var m=["a\u0123ent\u016Bra","grupa","holdings","un partneri"];var d=["{{company.prefix}} {{person.last_name.female}} {{company.legal_entity_type}}","{{company.prefix}} {{person.last_name.male}}","{{company.prefix}} {{person.last_name.male}} {{company.legal_entity_type}}","{{person.last_name.male}} un {{person.last_name.male}}","{{person.last_name.male}}, {{person.last_name.male}} un {{person.last_name.male}}"];var c=["AS","Bezp.Org.","Biedr\u012Bba","Firma","IU","SIA"];var aa={legal_entity_type:m,name_pattern:d,prefix:c},p=aa;var v={wide:["apr\u012Blis","augusts","decembris","febru\u0101ris","janv\u0101ris","j\u016Blijs","j\u016Bnijs","maijs","marts","novembris","oktobris","septembris"],wide_context:["apr\u012Bl\u012B","august\u0101","decembr\u012B","febru\u0101r\u012B","janv\u0101r\u012B","j\u016Blij\u0101","j\u016Bnij\u0101","maij\u0101","mart\u0101","novembr\u012B","oktobr\u012B","septembr\u012B"],abbr:["apr.","aug.","dec.","feb.","jan.","j\u016Bl.","j\u016Bn.","mai.","mar.","nov.","okt.","sep."],abbr_context:["apr.","aug.","dec.","febr.","janv.","j\u016Bl.","j\u016Bn.","mai.","mar.","nov.","okt.","sept."]};var k={wide:["Ceturtdiena","Otrdiena","Piektdiena","Pirmdiena","Sestdiena","Sv\u0113tdiena","Tre\u0161diena"],wide_context:["ceturtdien","otrdien","piektdien","pirmdien","sestdien","sv\u0113tdien","tre\u0161dien"],abbr:["Ct","Ot","Pk","Pr","Se","Sv","Tr"],abbr_context:["cet.","otr.","pk.","pr.","se.","sv.","tr."]};var ra={month:v,weekday:k},b=ra;var g=["com","info","lv","net","org"];var j=["apollo.lv","gmail.com","hotmail.com","inbox.lv","mail.lv","one.lv","outlook.lv","yahoo.com"];var sa={domain_suffix:g,free_email:j},f=sa;var z=["###","##"];var K=["Aina\u017Ei","Aizkraukle","Aizpute","Akn\u012Bste","Aloja","Al\u016Bksne","Ape","Auce","Baldone","Balo\u017Ei","Balvi","Bauska","Broc\u0113ni","C\u0113sis","Cesvaine","Dagda","Daugavpils","Dobele","Durbe","Grobi\u0146a","Gulbene","Ik\u0161\u0137ile","Il\u016Bkste","Jaunjelgava","J\u0113kabpils","Jelgava","J\u016Brmala","Kandava","K\u0101rsava","Kr\u0101slava","Kuld\u012Bga","\u0136egums","Lielv\u0101rde","Liep\u0101ja","L\u012Bgatne","Limba\u017Ei","L\u012Bv\u0101ni","Lub\u0101na","Ludza","Madona","Mazsalaca","Ogre","Olaine","P\u0101vilosta","Piltene","P\u013Cavi\u0146as","Prei\u013Ci","Priekule","R\u0113zekne","R\u012Bga","R\u016Bjiena","Sabile","Salacgr\u012Bva","Salaspils","Saldus","Saulkrasti","Seda","Sigulda","Skrunda","Smiltene","Staicele","Stende","Stren\u010Di","Subate","Talsi","Tukums","Valdem\u0101rpils","Valka","Valmiera","Vanga\u017Ei","Varak\u013C\u0101ni","Ventspils","Vies\u012Bte","Vi\u013Caka","Vi\u013C\u0101ni","Zilupe"];var L=["{{location.city_name}}"];var S=["Andora","Apvienotie Ar\u0101bu Emir\u0101ti","Afganist\u0101na","Alb\u0101nija","Arm\u0113nija","Anti\u013Cas","Angola","Antarktika","Argent\u012Bna","Amerik\u0101\u0146u Samoa","Austrija","Austr\u0101lija","Azerbaid\u017E\u0101na","Bosnija un Hercegovina","Barbadosa","Banglade\u0161a","Be\u013C\u0123ija","Burkinafaso","Bulg\u0101rija","Bahreina","Burundija","Benina","Bruneja","Bol\u012Bvija","Braz\u012Blija","But\u0101na","Buv\u0113 sala","Botsv\u0101na","Baltkrievija","Beliza","Kan\u0101da","Kongo","\u0160veice","Kotdivu\u0101ra","Kuka salas","\u010C\u012Ble","Kamer\u016Bna","\u0136\u012Bna","Kolumbija","Kostarika","Kuba","Kaboverde","Kipra","\u010Cehija","V\u0101cija","D\u0101nija","Dominika","Al\u017E\u012Brija","Ekvadora","Igaunija","\u0112\u0123ipte","Sp\u0101nija","Etiopija","Somija","Fid\u017Ei","Francija","Gabona","Lielbrit\u0101nija","Gren\u0101da","Gruzija","Gana","Grenlande","Gambija","Gvineja","Gvadelupa","Grie\u0137ija","Gvatemala","Guama","Gaj\u0101na","Honkonga","Hondurasa","Horv\u0101tija","Haiti","Ung\u0101rija","Indon\u0113zija","\u012Arija","Izra\u0113la","Indija","Ir\u0101ka","Ir\u0101na","Islande","It\u0101lija","Jamaika","Jord\u0101nija","Jap\u0101na","Kenija","Kirgizst\u0101na","Kambod\u017Ea","Korejas Tautas Demokr\u0101tisk\u0101 Republika","Korejas Republika","Kuveita","Kaimanu salas","Kazahst\u0101na","Laosa","Lib\u0101na","Lihten\u0161teina","\u0160rilanka","Lib\u0113rija","Lesoto","Lietuva","Luksemburga","Latvija","L\u012Bbija","Maroka","Monako","Moldova","Madagaskara","Ma\u0137edonija","Mali","Mongolija","Makao","Martinika","Maurit\u0101nija","Montserrata","Malta","Maur\u012Bcija","Mald\u012Bvija","Mal\u0101vija","Meksika","Malaizija","Mozambika","Nam\u012Bbija","Nig\u0113ra","Nig\u0113rija","Nikaragva","N\u012Bderlande","Norv\u0113\u0123ija","Nep\u0101la","Jaunz\u0113lande","Om\u0101na","Panama","Peru","Filip\u012Bnas","Pakist\u0101na","Polija","Puertoriko","Portug\u0101le","Paragvaja","Katara","Rum\u0101nija","Krievija","Ruanda","Sa\u016Bda Ar\u0101bija","Sud\u0101na","Zviedrija","Singap\u016Bra","Slov\u0113nija","Slov\u0101kija","Sjerraleone","Sanmar\u012Bno","Seneg\u0101la","Som\u0101lija","Surinama","Salvadora","S\u012Brija","Svazilenda","\u010Cada","Togo","Taizeme","Tad\u017Eikist\u0101na","Turkmenist\u0101na","Tunisija","Tonga","Turcija","Taiv\u0101na","Tanz\u0101nija","Ukraina","Uganda","Amerikas Savienot\u0101s Valstis","Urugvaja","Uzbekist\u0101na","Venecu\u0113la","Vjetnama","Jemena","Zambija","Zimbabve"];var B=["LV####","LV-####"];var M=["dz. ###","- ###"];var P=["Aizkraukles raj.","Al\u016Bksnes raj.","Baltijas j\u016Bra","Balvu raj.","Bauskas raj.","C\u0113su raj.","Daugavpils raj.","Dobeles raj.","Gulbenes raj.","J\u0113kabpils raj.","Jelgavas raj.","J\u016Brmala","Kr\u0101slavas raj.","Kuld\u012Bgas raj.","Liep\u0101jas raj.","Limba\u017Eu raj.","Ludzas raj.","Madonas raj.","Ogres raj.","Prei\u013Cu raj.","R\u0113zeknes raj.","R\u012Bga un R\u012Bgas raj.","Saldus raj.","Talsu raj.","Tukuma raj.","Valkas raj.","Valmieras raj.","Ventspils raj."];var V={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}} {{location.secondaryAddress}}"};var A=["13. janv\u0101ra","Admir\u0101\u013Cu","Air\u012Btes","Aka\u010Du","Akad\u0113mijas","Akad\u0113mi\u0137a Mstislava Keldi\u0161a","Ak\u0101ciju","Akl\u0101","Akme\u0146u","Aleksandra Biezi\u0146a","Aleksandra \u010Caka","Alfr\u0113da Kalni\u0146a","Al\u012Bses","Alksn\u0101ja","Am\u0101lijas","Anglik\u0101\u0146u","Anni\u0146mui\u017Eas","Apak\u0161gr\u0101vja","Apmet\u0146u","Apri\u0137u","Arsen\u0101la","Artil\u0113rijas","As\u012Btes","Atg\u0101zenes","Atgrie\u017Eu","Atp\u016Btas","Aud\u0113ju","Aug\u013Cu","Aug\u0161iela","Aug\u0161zemes","\u0100bolu","\u0100p\u0161u","\u0100rlavas","Bab\u012Btes","Balo\u017Eu","Bauma\u0146a","B\u0101rbeles","B\u0101rddzi\u0146u","B\u0101ri\u0146u","B\u0101tas","Bever\u012Bnas","B\u0113rzlapu","B\u0113rzupes","Bie\u0137ensalas","Bie\u0161u","Bi\u0161u","B\u012Bskapa","Blauma\u0146a","Bl\u012Bdenes","Bramber\u0123es","Brig\u0101des","Bri\u0146\u0123u","Br\u012Bv\u012Bbas","Bru\u0146inieku","Br\u016Bkle\u0146u","Bukai\u0161u","Centr\u0101ltirgus","Cep\u013Ca","C\u0113res","Cigori\u0146u","C\u012Bru\u013Cu","\u010Cuguna","Dand\u0101les","Daugu\u013Cu","D\u0101liju","D\u0101rzaug\u013Cu","D\u0101rzciema","D\u0101rzi\u0146u","Dign\u0101jas","Dik\u013Cu","D\u012B\u0137a","Dreili\u0146u","Dric\u0101nu","Dzelzce\u013Ca","Dze\u0146u","Dz\u0113rvju","D\u017E\u016Bkstes","Ernesta Birznieka-Up\u012B\u0161a","Ernest\u012Bnes","\u0112rg\u013Cu","Festiv\u0101la","Firsa Sadov\u0146ikova","Fri\u010Da Br\u012Bvzemnieka","Fridri\u0137a","Gaizi\u0146a","Gleznot\u0101ju","Gl\u016Bdas","Gogo\u013Ca","Gran\u012Bta","Greben\u0161\u010Dikova","Gren\u010Du","Gr\u0113cinieku","Gr\u0113du","Gr\u012B\u0161\u013Cu","Grobi\u0146as","\u0122ertr\u016Bdes","\u0122imnastikas","Herma\u0146a","Ik\u0161\u0137iles","Ilm\u0101jas","Indri\u0137a","In\u017Eenieru","\u012As\u0101","\u012Avandes","\u012Aves","Jasmui\u017Eas","J\u0101\u0146a","J\u0101\u0146a Asara","J\u0101\u0146a \u010Cakstes","J\u0101\u0146a Endzel\u012Bna","J\u0101\u0146a Grestes","J\u0101\u0146av\u0101rtu","J\u0101\u0146ogu","J\u0113kaba","J\u0113kabpils","J\u0113zusbazn\u012Bcas","Jukuma V\u0101cie\u0161a","J\u016Brkalnes","Kal\u0113ju","Kal\u0113tu","Ka\u013C\u0137u","Kame\u0146u","Ka\u0146iera","Kapse\u013Cu","Kartupe\u013Cu","Kato\u013Cu","K\u0101\u013Cu","K\u0101r\u013Ca Ulma\u0146a","K\u0101rsavas","Kl\u0101\u0146u","Kom\u0113tas","Konr\u0101da","Krauk\u013Cu","Kr\u0101mu","Kr\u0101slavas","Kr\u0101sot\u0101ju","Kri\u0161j\u0101\u0146a Barona","Kri\u0161j\u0101\u0146a Valdem\u0101ra","Kr\u012Bdenera","Kr\u016Bzes","Kuk\u0161u","Kurs\u012B\u0161u","Kvie\u0161u","\u0136ekavas","\u0136emeru","\u0136engaraga","\u0136\u0113ni\u0146u","\u0136ivu\u013Cu","Latvie\u0161u str\u0113lnieku","Lav\u012Bzes","L\u0101\u010Dpl\u0113\u0161a","Lie\u0123u","Liep\u0101jas","L\u012Bbagu","L\u012Bdaku","L\u012Bdera","L\u012Bksnas","L\u012Bv\u0101nu","L\u012Bvciema","L\u012Bves","Lokomot\u012Bves","Lub\u0101nas","Ludvi\u0137a","Lutri\u0146u","\u013Baudonas","\u013Bermontova","Mago\u0146u","Mak\u0161a","Mal\u0113ju","Mat\u012Bsa","Maz\u0101 Bauskas","Maz\u0101 Bi\u0161u","Maz\u0101 Cep\u013Ca","Maz\u0101 Jaunavu","Maz\u0101 Kalna","Maz\u0101 Krasta","Maz\u0101 Lub\u0101nas","Maz\u0101 Mat\u012Bsa","Maz\u0101 Miesnieku","Maz\u0101 Mon\u0113tu","Maz\u0101 Muzeja","Maz\u0101 Nomet\u0146u","Maz\u0101 Pils","Maz\u0101 Smil\u0161u","Maz\u0101 St\u0113rstu","Maz\u0101 Trok\u0161\u0146u","M\u0101lu","M\u0101ras aleja","M\u0101rsta\u013Cu","M\u0101rupes","Mer\u0137e\u013Ca","Me\u017Ekalna","Me\u017Emalas","Me\u017Eotnes","M\u0113meles","M\u0113rsraga","M\u016Bku","M\u016Bkupurva","M\u016Bkusalas","M\u016Brnieku","Naud\u012Btes","N\u0101ras","Ne\u013C\u0137u","N\u0113\u0123u","N\u012Bgrandes","N\u012Bkr\u0101ces","Oj\u0101ra V\u0101cie\u0161a","Ol\u012Bvu","Orma\u0146u","Pamp\u0101\u013Cu","Paula Leji\u0146a","P\u0101rmiju","P\u0101rslas","P\u0113rkones","P\u0113rnavas","P\u0113rses","P\u0113terbazn\u012Bcas","Pilso\u0146u","P\u012Bpe\u0146u","Plan\u012Bcas","Plau\u017Eu","Pleskod\u0101les","Pl\u0113nes","Pl\u0113suma","Pl\u016Bmju","P\u013Cavas","P\u013Cavi\u0146u","P\u013Cavnieku","Po\u013Cu","Pr\u0101gas","Prei\u013Cu","Pr\u016B\u0161u","Purms\u0101tu","Pu\u0161kina","P\u016Bpolu","Rai\u0146a","Ra\u0146\u0137a","Rau\u0161u","R\u0101ce\u0146u","Renc\u0113nu","R\u0113zeknes","R\u0113znas","Riharda V\u0101gnera","Rik\u0161ot\u0101ju","R\u012Bdzenes","R\u012Btausmas","R\u012Btupes","Robe\u017Eu","Rube\u0146kalna","Rudb\u0101r\u017Eu","Ru\u0161onu","R\u016Bdolfa","R\u016Bjienas","R\u016Bsi\u0146a","Sap\u0146u","Sarkan\u0101","S\u0101rtes","S\u0101ti\u0146u","S\u0113jas","S\u0113renes","Sieks\u0101tes","S\u012Bpeles","S\u012Bpolu","Ska\u0146u","Sk\u0101r\u0146u","Skr\u012Bnes","Sl\u0101vu","Sm\u0101rdes","Smil\u0161u","Spa\u013Cu","Spar\u0123e\u013Cu","Sp\u0101res","Sp\u0101rnu","Spe\u0137a","Sp\u012Bdolas","Sp\u012B\u0137eru","Sta\u013C\u0123enes","St\u0101v\u0101","St\u0113rstu","Stopi\u0146u","Stren\u010Du","Str\u016Bgu","Sunta\u017Eu","S\u016Bnu","Sv\u0113tes","\u0160amp\u0113tera","\u0160aur\u0101","\u0160autuves","\u0160\u0137irotavas","\u0160\u0137\u016B\u0146u","Tadai\u0137u","Taisn\u0101","Te\u0101tra","Temp\u013Ca","Ter\u0113zes","T\u0113jas","T\u0113rbatas","T\u0113ri\u0146u","T\u0113rvetes","Tipogr\u0101fijas","Tirgo\u0146u","T\u012Bn\u016B\u017Eu","T\u012Braines","T\u012Bre\u013Ca","T\u012Bruma","Tor\u0146a","Tor\u0146akalna","Trok\u0161\u0146u","Turge\u0146eva","Ug\u0101les","Upesgr\u012Bvas","\u016Abeles","\u016Adensvada","Vai\u0146odes","Valde\u0137u","Valtai\u0137u","Va\u013C\u0146u","Varak\u013C\u0101nu","Var\u0161avas","V\u0101rnu","Vecpils\u0113tas","Vel\u0113nu","V\u0113ja","Vien\u012Bbas","Vies\u012Btes","Vi\u013C\u0101nu","Vir\u0101nes","Virsai\u0161u","Visval\u017Ea","Vi\u0161\u0137u","Za\u0137u","Za\u013Cenieku","Z\u0101\u013Cu","Zelti\u0146u","Ze\u013C\u013Cu","Zemai\u0161u","Zemit\u0101na","Zem\u012Btes","Zute\u0146u","Zvaig\u017E\u0146u","Zv\u0101rdes","Zv\u0101rtavas","\u017Da\u0146a Lipkes","\u017D\u012Bguru","\u017Dub\u012B\u0161u"];var D=["{{location.street_name}} {{location.street_suffix}}"];var R=["iela","bulv\u0101ris","gatve","g\u0101te","laukums","dambis"];var ea={building_number:z,city_name:K,city_pattern:L,country:S,postcode:B,secondary_address:M,state:P,street_address:V,street_name:A,street_pattern:D,street_suffix:R},x=ea;var h=["\u0101","\u0101b","\u0101bb\u0101s","\u0101bduco","\u0101b\u0113o","\u0101bsc\u012Bdo","\u0101bscond\u012Btus","\u0101bs\u0113ns","\u0101bsorb\u0113o","\u0101bsqu\u0113","\u0101bst\u0113rgo","\u0101bsum","\u0101bund\u0101ns","\u0101butor","\u0101cc\u0113do","\u0101cc\u0113ndo","\u0101cc\u0113ptus","\u0101ccommodo","\u0101\u010D\u010D\u016B\u0161\u0101m\u016B\u0161","\u0101\u010D\u010D\u016B\u0161\u0101\u0146t\u012B\u016Bm","\u0101ccus\u0101tor","\u0101c\u0113r","\u0101c\u0113rb\u012Bt\u0101s","\u0101c\u0113rvus","\u0101c\u012Bdus","\u0101c\u012B\u0113s","\u0101cqu\u012Bro","\u0101cs\u012B","\u0101d","\u0101d\u0101mo","\u0101d\u0101ug\u0113o","\u0101ddo","\u0101dduco","\u0101d\u0113mpt\u012Bo","\u0101d\u0113o","\u0101d\u0113pt\u012Bo","\u0101df\u0113ctus","\u0101df\u0113ro","\u0101df\u012Bc\u012Bo","\u0101dfl\u012Bcto","\u0101dh\u0101\u0113ro","\u0101dhuc","\u0101d\u012Bc\u012Bo","\u0101d\u012Bmpl\u0113o","\u0101d\u012Bnv\u0113nt\u012Bt\u012B\u0101s","\u0101d\u012Bp\u012B\u0161\u010D\u012B","\u0101d\u012Bp\u012Bscor","\u0101d\u012Buvo","\u0101dm\u012Bn\u012Bstr\u0101t\u012Bo","\u0101dm\u012Br\u0101t\u012Bo","\u0101dm\u012Btto","\u0101dmon\u0113o","\u0101dmov\u0113o","\u0101dnuo","\u0101dopto","\u0101ds\u012Bdu\u0113","\u0101dstr\u012Bngo","\u0101dsu\u0113sco","\u0101dsum","\u0101dul\u0101t\u012Bo","\u0101dul\u0113sc\u0113ns","\u0101duro","\u0101dv\u0113n\u012Bo","\u0101dv\u0113rsus","\u0101dvoco","\u0101\u0113d\u012Bf\u012Bc\u012Bum","\u0101\u0113g\u0113r","\u0101\u0113gr\u0113","\u0101\u0113grot\u0101t\u012Bo","\u0101\u0113grus","\u0101\u0113n\u0113us","\u0101\u0113qu\u012Bt\u0101s","\u0101\u0113quus","\u0101\u0113r","\u0101\u0113st\u0101s","\u0101\u0113st\u012Bvus","\u0101\u0113stus","\u0101\u0113t\u0101s","\u0101\u0113t\u0113rnus","\u0101g\u0113r","\u0101gg\u0113ro","\u0101ggr\u0113d\u012Bor","\u0101gn\u012Bt\u012Bo","\u0101gnosco","\u0101go","\u0101\u012Bt","\u0101\u012Bunt","\u0101\u013C\u012B\u0101\u0161","\u0101l\u012B\u0113nus","\u0101l\u012B\u012B","\u0101l\u012Boqu\u012B","\u0101l\u012Bqu\u0101","\u0101\u013C\u012Bq\u016B\u0101m","\u0101\u013C\u012Bq\u016B\u012Bd","\u0101l\u012Bus","\u0101ll\u0101tus","\u0101lo","\u0101lt\u0113r","\u0101ltus","\u0101lv\u0113us","\u0101m\u0101r\u012Btudo","\u0101mb\u012Btus","\u0101mbulo","\u0101m\u0113t","\u0101m\u012Bc\u012Bt\u012B\u0101","\u0101m\u012Bculum","\u0101m\u012Bss\u012Bo","\u0101m\u012Bt\u0101","\u0101m\u012Btto","\u0101mo","\u0101mor","\u0101mov\u0113o","\u0101mpl\u0113xus","\u0101mpl\u012Btudo","\u0101mplus","\u0101nc\u012Bll\u0101","\u0101ng\u0113lus","\u0101ngulus","\u0101ngustus","\u0101n\u012Bm\u0101dv\u0113rto","\u0101n\u012Bm\u012B","\u0101\u0146\u012Bm\u012B","\u0101n\u012Bmus","\u0101nnus","\u0101ns\u0113r","\u0101nt\u0113","\u0101nt\u0113\u0101","\u0101nt\u0113pono","\u0101nt\u012Bquus","\u0101p\u0113r\u012B\u0101m","\u0101p\u0113r\u012Bo","\u0101p\u0113rt\u0113","\u0101postolus","\u0101pp\u0101r\u0101tus","\u0101pp\u0113llo","\u0101ppono","\u0101ppos\u012Btus","\u0101pprobo","\u0101pto","\u0101ptus","\u0101pud","\u0101qu\u0101","\u0101r\u0101","\u0101r\u0101n\u0113\u0101","\u0101rb\u012Btro","\u0101rbor","\u0101rbustum","\u0101rc\u0101","\u0101rc\u0113o","\u0101rc\u0113sso","\u0101r\u010Dh\u012Bt\u0113\u010Dto","\u0101rcus","\u0101rg\u0113ntum","\u0101rgum\u0113ntum","\u0101rguo","\u0101rm\u0101","\u0101rm\u0101r\u012Bum","\u0101ro","\u0101rs","\u0101rt\u012Bculus","\u0101rt\u012Bf\u012Bc\u012Bos\u0113","\u0101rto","\u0101rx","\u0101sc\u012Bsco","\u0101sc\u012Bt","\u0101sp\u0113r","\u0101\u0161p\u0113r\u012Bor\u0113\u0161","\u0101\u0161p\u0113r\u0146\u0101t\u016Br","\u0101sp\u012Bc\u012Bo","\u0101sporto","\u0101ss\u0113nt\u0101tor","\u0101\u0161\u0161\u016Bm\u0113\u0146d\u0101","\u0101strum","\u0101t","\u0101t\u0101vus","\u0101t\u0113r","\u0101tq\u016B\u0113","\u0101tqu\u012B","\u0101troc\u012Bt\u0101s","\u0101trox","\u0101tt\u0113ro","\u0101ttollo","\u0101ttonb\u012Btus","\u0101uctor","\u0101uctus","\u0101ud\u0101c\u012B\u0101","\u0101ud\u0101x","\u0101ud\u0113nt\u012B\u0101","\u0101ud\u0113o","\u0101ud\u012Bo","\u0101ud\u012Btor","\u0101uf\u0113ro","\u0101ur\u0113us","\u0101urum","\u0101ut","\u0101\u016Bt","\u0101ut\u0113m","\u0101\u016Bt\u0113m","\u0101utus","\u0101ux\u012Bl\u012Bum","\u0101v\u0101r\u012Bt\u012B\u0101","\u0101v\u0101rus","\u0101v\u0113ho","\u0101v\u0113rto","b\u0101\u012Bulus","b\u0101lbus","b\u0101rb\u0101","b\u0101rdus","b\u0101s\u012Bum","b\u0113\u0101t\u0101\u0113","b\u0113\u0101tus","b\u0113ll\u012Bcus","b\u0113llum","b\u0113n\u0113","b\u0113n\u0113f\u012Bc\u012Bum","b\u0113n\u0113vol\u0113nt\u012B\u0101","b\u0113n\u012Bgn\u0113","b\u0113st\u012B\u0101","b\u012Bbo","b\u012Bs","bl\u0101nd\u012Bor","b\u013C\u0101\u0146d\u012Bt\u012B\u012B\u0161","bo\u0146us","bo\u0161","br\u0113v\u012Bs","c\u0101do","c\u0101\u0113cus","c\u0101\u0113l\u0113st\u012Bs","c\u0101\u0113lum","c\u0101l\u0101m\u012Bt\u0101s","c\u0101lc\u0101r","c\u0101lco","c\u0101lculus","c\u0101ll\u012Bd\u0113","c\u0101mp\u0101n\u0101","c\u0101nd\u012Bdus","c\u0101n\u012Bs","c\u0101non\u012Bcus","c\u0101nto","c\u0101p\u012Bllus","c\u0101p\u012Bo","c\u0101p\u012Btulus","c\u0101pto","c\u0101put","c\u0101rbo","c\u0101rc\u0113r","c\u0101r\u0113o","c\u0101r\u012B\u0113s","c\u0101r\u012Bosus","c\u0101r\u012Bt\u0101s","c\u0101rm\u0113n","c\u0101rpo","c\u0101rus","c\u0101sso","c\u0101st\u0113","c\u0101sus","c\u0101t\u0113n\u0101","c\u0101t\u0113rv\u0101","c\u0101ttus","c\u0101ud\u0101","c\u0101us\u0101","c\u0101ut\u0113","c\u0101v\u0113o","c\u0101vus","c\u0113do","c\u0113l\u0113br\u0113r","c\u0113l\u0113r","c\u0113lo","c\u0113n\u0101","c\u0113n\u0101culum","c\u0113no","c\u0113nsur\u0101","c\u0113ntum","c\u0113rno","c\u0113rnuus","c\u0113rt\u0113","c\u0113rtus","c\u0113rvus","c\u0113t\u0113r\u0101","ch\u0101r\u012Bsm\u0101","ch\u012Brogr\u0101phum","c\u012Bbo","c\u012Bbus","c\u012Bcut\u0101","c\u012Bl\u012Bc\u012Bum","c\u012Bm\u0113nt\u0101r\u012Bus","c\u012Bm\u012Bn\u0101t\u012Bo","c\u012Bn\u012Bs","c\u012Brcumv\u0113n\u012Bo","c\u012Bto","c\u012Bv\u012Bs","c\u012Bv\u012Bt\u0101s","cl\u0101m","cl\u0101mo","cl\u0101ro","cl\u0101rus","cl\u0101ud\u0113o","cl\u0101ustrum","cl\u0113m\u0113nt\u012B\u0101","cl\u012Bb\u0101nus","co\u0101dun\u0101t\u012Bo","co\u0101\u0113gr\u0113sco","co\u0113p\u012B","co\u0113rc\u0113o","cog\u012Bto","cogn\u0101tus","cognom\u0113n","\u010Do\u0123o","coh\u0101\u0113ro","coh\u012Bb\u0113o","\u010Dohors","coll\u012Bgo","co\u013C\u013Cum","co\u013Co","co\u013Cor","com\u0101","comb\u012Bbo","comburo","com\u0113do","com\u0113s","com\u0113t\u0113s","com\u012Bs","com\u012Bt\u0101tus","comm\u0113moro","comm\u012Bnor","\u010Dommod\u012B","\u010Dommodo","commun\u012Bs","comp\u0101ro","comp\u0113llo","compl\u0113ctus","compo\u0146o","compr\u0113h\u0113ndo","comptu\u0161","con\u0101tus","conc\u0113do","conc\u012Bdo","con\u010D\u016Blko","cond\u012Bco","co\u0146duco","conf\u0113ro","conf\u012Bdo","co\u0146forto","confu\u0123o","congr\u0113g\u0101t\u012Bo","con\u012Bc\u012Bo","con\u012B\u0113cto","con\u012Btor","con\u012Bur\u0101t\u012Bo","co\u0146or","conqu\u0113ror","consc\u0113ndo","\u010Do\u0146\u0161\u0113\u010Dt\u0113t\u016Br","\u010Do\u0146\u0161\u0113q\u016B\u0101t\u016Br","\u010Do\u0146\u0161\u0113q\u016B\u016B\u0146t\u016Br","cons\u0113rvo","cons\u012Bd\u0113ro","consp\u0113rgo","const\u0101ns","consu\u0101sor","cont\u0101b\u0113sco","cont\u0113go","cont\u012Bgo","contr\u0101","conturbo","conv\u0113ntus","co\u0146voco","cop\u012B\u0101","cop\u012Bos\u0113","cor\u0146u","coron\u0101","\u010Dorpor\u012B\u0161","corpu\u0161","corr\u0113pt\u012Bus","corr\u012Bgo","corroboro","corrumpo","\u010Dorr\u016Bpt\u012B","coruscus","cot\u012Bd\u012B\u0113","cr\u0101pul\u0101","cr\u0101s","cr\u0101st\u012Bnus","cr\u0113\u0101tor","cr\u0113b\u0113r","cr\u0113bro","cr\u0113do","cr\u0113o","cr\u0113pt\u012Bo","cr\u0113pusculum","cr\u0113sco","cr\u0113t\u0101","cr\u012Bbro","cr\u012Bn\u012Bs","cruc\u012B\u0101m\u0113ntum","crud\u0113l\u012Bs","cru\u0113ntus","cr\u016Br","crustulum","cr\u016Bx","cub\u012Bcul\u0101r\u012Bs","cub\u012Btum","\u010D\u016Bbo","cu\u012B","cu\u012Bus","culp\u0101","\u010D\u016B\u013Cp\u0101","cu\u013Cpo","cult\u0113llus","cultur\u0101","c\u016Bm","\u010D\u016Bm","\u010D\u016Bmq\u016B\u0113","cun\u0101bul\u0101","cun\u0101\u0113","cunct\u0101t\u012Bo","cup\u012Bd\u012Bt\u0101s","\u010D\u016Bp\u012Bd\u012Bt\u0101t\u0113","cup\u012Bo","cupp\u0113d\u012B\u0101","cupr\u0113ssus","c\u016Br","cur\u0101","cur\u0101t\u012Bo","cur\u012B\u0101","cur\u012Bos\u012Bt\u0101s","cur\u012Bs","c\u016Bro","curr\u012Bculum","curr\u016Bs","curs\u012Bm","cur\u0161o","cur\u0161us","c\u016Brto","curt\u016Bs","c\u016Brvo","custod\u012B\u0101","d\u0101mn\u0101t\u012Bo","d\u0101mno","d\u0101p\u012Bf\u0113r","d\u0113b\u0113o","d\u0113b\u012Bl\u012Bto","d\u0113b\u012Bt\u012B\u0161","d\u0113c\u0113ns","d\u0113c\u0113rno","d\u0113c\u0113t","d\u0113c\u012Bmus","d\u0113c\u012Bp\u012Bo","d\u0113cor","d\u0113cr\u0113tum","d\u0113cumbo","d\u0113d\u0113cor","d\u0113d\u012Bco","d\u0113duco","d\u0113f\u0101\u0113co","d\u0113f\u0113ndo","d\u0113f\u0113ro","d\u0113f\u0113ssus","d\u0113f\u0113t\u012Bscor","d\u0113f\u012Bc\u012Bo","d\u0113fl\u0113o","d\u0113fluo","d\u0113fungo","d\u0113g\u0113n\u0113ro","d\u0113g\u0113ro","d\u0113gusto","d\u0113\u012Bnd\u0113","d\u0113l\u0113ct\u0101t\u012Bo","d\u0113\u013C\u0113\u010Dt\u016B\u0161","d\u0113l\u0113go","d\u0113\u013C\u0113\u0146\u012Bt\u012B","d\u0113l\u0113o","d\u0113l\u012Bb\u0113ro","d\u0113l\u012Bc\u0101t\u0113","d\u0113l\u012Bnquo","d\u0113ludo","d\u0113m\u0113ns","d\u0113m\u0113rgo","d\u0113m\u012Btto","d\u0113mo","d\u0113monstro","d\u0113moror","d\u0113mulc\u0113o","d\u0113mum","d\u0113n\u0113go","d\u0113n\u012Bqu\u0113","d\u0113ns","d\u0113nunc\u012Bo","d\u0113nuo","d\u0113orsum","d\u0113p\u0113r\u0113o","d\u0113pono","d\u0113populo","d\u0113porto","d\u0113pr\u0101\u0113dor","d\u0113pr\u0113c\u0101tor","d\u0113pr\u012Bmo","d\u0113promo","d\u0113pulso","d\u0113puto","d\u0113r\u0113l\u012Bnquo","d\u0113r\u012Bd\u0113o","d\u0113r\u012Bp\u012Bo","d\u0113\u0161\u0113r\u016B\u0146t","d\u0113s\u012Bd\u0113ro","d\u0113s\u012Bno","d\u0113s\u012Bp\u012Bo","d\u0113solo","d\u0113sp\u0101r\u0101tus","d\u0113sp\u0113cto","d\u012B\u010Dt\u0101","d\u012B\u0123\u0146\u012B\u0161\u0161\u012Bmo\u0161","d\u012B\u0161t\u012B\u0146\u010Dt\u012Bo","do\u013Cor","do\u013Cor\u0113","do\u013Cor\u0113m","do\u013Cor\u0113mq\u016B\u0113","do\u013Cor\u0113\u0161","do\u013Cor\u012Bb\u016B\u0161","do\u013Cor\u016Bm","d\u016B\u010D\u012Bm\u016B\u0161","\u0113\u0101","\u0113\u0101q\u016B\u0113","\u0113\u0101r\u016Bm","\u0113\u012B\u016B\u0161","\u0113\u013C\u012B\u0123\u0113\u0146d\u012B","\u0113\u0146\u012Bm","\u0113o\u0161","\u0113rror","\u0113\u0161\u0161\u0113","\u0113\u0161t","\u0113t","\u0113\u016Bm","\u0113v\u0113\u0146\u012B\u0113t","\u0113x","\u0113x\u010D\u0113pt\u016Br\u012B","\u0113x\u0113r\u010D\u012Bt\u0101t\u012Bo\u0146\u0113m","\u0113xp\u0113d\u012Bt\u0101","\u0113xp\u013C\u012B\u010D\u0101bo","f\u0101\u010D\u0113r\u0113","f\u0101\u010D\u012B\u013C\u012B\u0161","f\u016B\u0123\u0101","f\u016B\u0123\u012B\u0101t","f\u016B\u0123\u012Bt","h\u0101r\u016Bm","h\u012B\u010D","\u012Bd","\u012B\u013C\u013Co","\u012B\u013C\u013C\u016Bm","\u012Bmp\u0113d\u012Bt","\u012B\u0146","\u012B\u0146\u010D\u012Bd\u016B\u0146t","\u012Bnf\u012Bt","\u012Bnfl\u0101mm\u0101t\u012Bo","\u012B\u0146v\u0113\u0146tor\u0113","\u012Bp\u0161\u0101","\u012Bp\u0161\u0101m","\u012Bp\u0161\u016Bm","\u012B\u0161t\u0113","\u012Bt\u0101q\u016B\u0113","\u012B\u016Br\u0113","\u012B\u016B\u0161to","\u013C\u0101bor\u0113","\u013C\u0101bor\u012Bo\u0161\u0101m","\u013C\u0101bor\u016Bm","\u013C\u0101\u016Bd\u0101\u0146t\u012B\u016Bm","\u013C\u012Bb\u0113ro","m\u0101\u0123\u0146\u0101m","m\u0101\u0123\u0146\u012B","m\u0101\u012Bor\u0113\u0161","m\u0101x\u012Bm\u0113","m\u012B\u0146\u012Bm\u0101","m\u012B\u0146\u016B\u0161","mod\u012B","mo\u013C\u0113\u0161t\u012B\u0101\u0113","mo\u013C\u0113\u0161t\u012B\u0101\u0161","mo\u013C\u013C\u012Bt\u012B\u0101","\u0146\u0101m","\u0146\u0101t\u016B\u0161","\u0146\u0113\u010D\u0113\u0161\u0161\u012Bt\u0101t\u012Bb\u016B\u0161","\u0146\u0113mo","\u0146\u0113q\u016B\u0113","\u0146\u0113\u0161\u010D\u012B\u016B\u0146t","\u0146\u012Bh\u012B\u013C","\u0146\u012B\u0161\u012B","\u0146ob\u012B\u0161","\u0146o\u0146","\u0146o\u0161tr\u016Bm","\u0146\u016B\u013C\u013C\u0101","\u0146\u016Bmq\u016B\u0101m","o\u010D\u010D\u0101\u0113\u010D\u0101t\u012B","oc\u0113r","od\u012Bo","od\u012Bt","off\u012B\u010D\u012B\u0101","off\u012B\u010D\u012B\u012B\u0161","om\u0146\u012B\u0161","opt\u012Bo","p\u0101\u0113ns","p\u0101r\u012B\u0101t\u016Br","p\u0101t\u012Bor","p\u0101tr\u012B\u0101","p\u0101troc\u012Bnor","p\u0101truus","p\u0101uc\u012B","p\u0101ul\u0101t\u012Bm","p\u0101up\u0113r","p\u0101x","p\u0113cc\u0101tus","p\u0113cco","p\u0113cto","p\u0113ctus","p\u0113cus","p\u0113\u012Bor","p\u0113l","p\u0113rf\u0113r\u0113\u0146d\u012B\u0161","p\u0113r\u0161p\u012B\u010D\u012B\u0101t\u012B\u0161","p\u013C\u0101\u010D\u0113\u0101t","porro","po\u0161\u0161\u012Bm\u016B\u0161","pr\u0101\u0113\u0161\u0113\u0146t\u012B\u016Bm","prov\u012Bd\u0113\u0146t","q\u016B\u0101\u0113","q\u016B\u0101\u0113r\u0101t","q\u016B\u0101m","q\u016B\u0101\u0161","q\u016B\u0101\u0161\u012B","q\u016B\u012B","q\u016B\u012B\u0101","q\u016B\u012Bb\u016B\u0161d\u0101m","q\u016B\u012Bd\u0113m","q\u016B\u012B\u0161","q\u016B\u012B\u0161q\u016B\u0101m","q\u016Bo","q\u016Bod","q\u016Bo\u0161","r\u0101t\u012Bo\u0146\u0113","r\u0113\u010D\u016B\u0161\u0101\u0146d\u0101\u0113","r\u0113\u012B\u010D\u012B\u0113\u0146d\u012B\u0161","r\u0113m","r\u0113p\u0113\u013C\u013C\u0101t","r\u0113p\u0113\u013C\u013C\u0113\u0146d\u016B\u0161","r\u0113pr\u0113h\u0113\u0146d\u0113r\u012Bt","r\u0113p\u016Bd\u012B\u0101\u0146d\u0101\u0113","r\u0113r\u016Bm","\u0161\u0101\u0113p\u0113","\u0161\u0101p\u012B\u0113\u0146t\u0113","\u0161\u0113d","\u0161\u0113q\u016B\u012B","\u0161\u012Bm\u012B\u013C\u012Bq\u016B\u0113","\u0161\u012B\u0146t","\u0161\u012Bt","soc\u012Bus","sod\u0101l\u012Bt\u0101s","so\u013C","sol\u0113o","sol\u012Bo","sol\u012Btudo","sol\u012Bum","soll\u0113rs","soll\u012Bc\u012Bto","so\u013Cum","solu\u0161","\u0161o\u013C\u016Bt\u0101","so\u013Cut\u012Bo","so\u013Cvo","somn\u012Bculosus","som\u0146us","so\u0146\u012Btus","so\u0146o","soph\u012Bsm\u0101t\u0101","\u0161opor","sord\u0113o","sort\u012Btus","sp\u0101rgo","sp\u0113c\u012Bosus","sp\u0113ct\u0101culum","sp\u0113culum","sp\u0113rno","sp\u0113ro","sp\u0113s","sp\u012Bculum","sp\u012Br\u012Btus","spol\u012B\u0101t\u012Bo","spont\u0113","st\u0101b\u012Bl\u012Bs","st\u0101t\u012Bm","st\u0101tu\u0101","st\u0113ll\u0101","st\u012Bll\u012Bc\u012Bd\u012Bum","st\u012Bp\u0113s","st\u012Bps","\u0161to","str\u0113nuus","stru\u0113s","stud\u012Bo","stu\u013Ctus","su\u0101d\u0113o","su\u0101sor\u012B\u0101","s\u016Bb","sub\u012Bto","sub\u012Bungo","subl\u012Bm\u0113","subn\u0113cto","subs\u0113co","subst\u0101nt\u012B\u0101","subv\u0113n\u012Bo","succ\u0113do","succurro","suff\u012Bc\u012Bo","suffo\u010Do","suffr\u0101g\u012Bum","sugg\u0113ro","su\u012B","su\u013C\u016Bm","s\u016Bm","summ\u0101","summ\u012Bss\u0113","summop\u0113r\u0113","s\u016Bmo","sumptus","\u0161\u016B\u0146t","sup\u0113ll\u0113x","sup\u0113r","supp\u0113ll\u0113x","suppl\u0101nto","suppono","supr\u0101","sur\u010D\u016Blus","sur\u0123o","sur\u0161um","susc\u012Bp\u012Bo","\u0161\u016B\u0161\u010D\u012Bp\u012Bt","susp\u0113ndo","sust\u012Bn\u0113o","su\u016Bs","syn\u0101gog\u0101","t\u0101b\u0113ll\u0101","t\u0101b\u0113rnus","t\u0101b\u0113sco","t\u0101bgo","t\u0101bul\u0101","t\u0101c\u0113o","t\u0101ctus","t\u0101\u0113d\u012Bum","t\u0101l\u012Bo","t\u0101l\u012Bs","t\u0101lus","t\u0101m","t\u0101md\u012Bu","t\u0101m\u0113n","t\u0101m\u0113ts\u012B","t\u0101m\u012Bs\u012Bum","t\u0101mqu\u0101m","t\u0101nd\u0113m","t\u0101nt\u012Bllus","t\u0101ntum","t\u0101rdus","t\u0113go","t\u0113m\u0113r\u012Bt\u0101s","t\u0113mp\u0113r\u0101nt\u012B\u0101","t\u0113mplum","t\u0113mpor\u0101","t\u0113mpor\u0113","t\u0113mpor\u012Bb\u016B\u0161","t\u0113mpt\u0101t\u012Bo","t\u0113mpus","t\u0113n\u0101x","t\u0113ndo","t\u0113n\u0113o","t\u0113n\u0113r","t\u0113\u0146\u0113t\u016Br","t\u0113nu\u012Bs","t\u0113nus","t\u0113p\u0113sco","t\u0113p\u012Bdus","t\u0113r","t\u0113r\u0113bro","t\u0113r\u0113s","t\u0113rg\u0101","t\u0113rg\u0113o","t\u0113rg\u012Bv\u0113rs\u0101t\u012Bo","t\u0113rgo","t\u0113rgum","t\u0113rm\u0113s","t\u0113rm\u012Bn\u0101t\u012Bo","t\u0113ro","t\u0113rr\u0101","t\u0113rr\u0113o","t\u0113rr\u012Bto","t\u0113rror","t\u0113rsus","t\u0113rt\u012Bus","t\u0113st\u012Bmon\u012Bum","t\u0113xo","t\u0113xt\u012Bl\u012Bs","t\u0113xtor","t\u0113xtus","th\u0101l\u0101ss\u012Bnus","th\u0113\u0101trum","th\u0113c\u0101","th\u0113m\u0101","th\u0113ologus","th\u0113rm\u0101\u0113","th\u0113s\u0101urus","th\u0113s\u012Bs","thor\u0101x","thymbr\u0101","thym\u016Bm","t\u012Bb\u012B","t\u012Bm\u012Bdus","t\u012Bmor","t\u012Btulus","tol\u0113ro","to\u013C\u013Co","tond\u0113o","to\u0146\u0161or","torqu\u0113o","torr\u0113ns","tot","tot\u0101m","tot\u012Bd\u0113m","tot\u012B\u0113s","tot\u016Bs","tr\u0101cto","tr\u0101do","tr\u0101ho","tr\u0101ns","tr\u0113d\u0113c\u012Bm","tr\u0113mo","tr\u0113p\u012Bd\u0113","tr\u0113s","tr\u012Bbuo","tr\u012Bc\u0113s\u012Bmus","tr\u012Bdu\u0101n\u0101","tr\u012Bpud\u012Bo","tr\u012Bst\u012Bs","tr\u012Bumphus","truc\u012Bdo","trucul\u0113nt\u0113r","tub\u012Bn\u0113us","tu\u012B","t\u016Bm","tumu\u013Ctus","t\u016Bn\u010D","turb\u0101","t\u016Brbo","turp\u012Bs","tut\u0101m\u0113n","tut\u012Bs","tyr\u0101nnus","ub\u0113rr\u012Bm\u0113","ub\u012B","ulc\u012Bscor","\u016B\u013C\u013C\u0101m","ull\u016Bs","ult\u0113r\u012Bus","ult\u012Bo","ultr\u0101","umbr\u0101","um\u0113rus","umqu\u0101m","un\u0101","und\u0113","\u016B\u0146d\u0113","und\u012Bqu\u0113","un\u012Bv\u0113rs\u0113","un\u016Bs","urb\u0101nus","urb\u0161","ur\u0113do","us\u012Bt\u0101s","usqu\u0113","ust\u012Blo","ustulo","u\u0161\u016Bs","\u016Bt","ut\u0113r","ut\u0113rqu\u0113","ut\u012Bl\u012Bs","ut\u012Bqu\u0113","\u016Btor","utpot\u0113","utr\u012Bmqu\u0113","utroqu\u0113","utr\u016Bm","\u016Bxor","v\u0101co","v\u0101cuus","v\u0101do","v\u0101\u0113","v\u0101ld\u0113","v\u0101l\u0113ns","v\u0101l\u0113o","v\u0101l\u0113tudo","v\u0101l\u012Bdus","v\u0101llum","v\u0101pulus","v\u0101r\u012B\u0113t\u0101s","v\u0101r\u012Bus","v\u0113h\u0113m\u0113ns","v\u0113l","v\u0113\u013C","v\u0113\u013C\u012Bt","v\u0113loc\u012Bt\u0113r","v\u0113lum","v\u0113lut","v\u0113n\u012B\u0101","v\u0113\u0146\u012B\u0101m","v\u0113n\u012Bo","v\u0113nt\u012Bto","v\u0113ntosus","v\u0113ntus","v\u0113nust\u0101s","v\u0113r","v\u0113rb\u0113r\u0101","v\u0113rbum","v\u0113r\u0113","v\u0113r\u0113cund\u012B\u0101","v\u0113r\u0113or","v\u0113rgo","v\u0113r\u012Bt\u0101s","v\u0113r\u012Bt\u0101t\u012B\u0161","v\u0113ro","v\u0113rsus","v\u0113rto","v\u0113rumt\u0101m\u0113n","v\u0113rus","v\u0113sco","v\u0113s\u012Bc\u0101","v\u0113sp\u0113r","v\u0113sp\u012Bllo","v\u0113st\u0113r","v\u0113st\u012Bg\u012Bum","v\u0113strum","v\u0113tus","v\u012B\u0101","v\u012Bc\u012Bnus","v\u012Bc\u012Bss\u012Btudo","v\u012Bctor\u012B\u0101","v\u012Bctus","v\u012Bd\u0113l\u012Bc\u0113t","v\u012Bd\u0113o","v\u012Bduo","v\u012Bg\u012Blo","v\u012Bgor","v\u012Bl\u012Bcus","v\u012Bl\u012Bs","v\u012Bl\u012Bt\u0101s","v\u012Bll\u0101","v\u012Bnco","v\u012Bnculum","v\u012Bnd\u012Bco","v\u012Bn\u012Btor","v\u012Bnum","v\u012Br","v\u012Brg\u0101","v\u012Brgo","v\u012Br\u012Bd\u012Bs","v\u012Br\u012Bl\u012Bt\u0113r","v\u012Brtus","v\u012Bs","v\u012Bscus","v\u012Bt\u0101","v\u012Bt\u0101\u0113","v\u012Bt\u012Bosus","v\u012Bt\u012Bum","v\u012Bto","v\u012Bvo","v\u012Bx","vob\u012Bs","voc\u012Bf\u0113ror","vo\u010Do","vol\u0101t\u012Bcus","vo\u013Co","volub\u012Bl\u012Bs","volunt\u0101r\u012Bus","vol\u016Bp","vo\u013C\u016Bpt\u0101\u0161","vo\u013C\u016Bpt\u0101t\u0113","vo\u013C\u016Bpt\u0101t\u0113m","vo\u013C\u016Bpt\u0101t\u0113\u0161","vo\u013C\u016Bpt\u0101t\u012Bb\u016B\u0161","vo\u013C\u016Bpt\u0101t\u016Bm","volut\u0101brum","volv\u0101","vom\u0113r","vom\u012Bc\u0101","vom\u012Bto","vor\u0101go","vor\u0101x","voro","vos","vot\u016Bm","vov\u0113o","vox","vul\u0101r\u012Bt\u0113r","vulg\u0101r\u012Bs","vulg\u012Bv\u0101gus","vulgo","vulgus","vuln\u0113ro","vu\u013C\u0146us","vulp\u0113s","vult\u012Bculus","x\u012Bph\u012B\u0101s"];var ia={word:h},Z=ia;var ta={title:"Latvian",code:"lv",language:"lv",endonym:"latvie\u0161u valoda",dir:"ltr",script:"Latn"},J=ta;var T={generic:["Ain\u0101rs","Akvel\u012Bna","Albert\u012Bne","Alfr\u0113ds","Alo\u012Bzs","Alv\u012Bne","Am\u0101lija","Andris","And\u017Eejs","And\u017Es","Anrijs","An\u0161lavs","Ark\u0101dijs","Arm\u012Bns","Art\u016Brs","Arv\u012Bds","Astr\u012Bda","Aur\u0113lija","A\u012Bda","A\u013C\u0123irds","A\u013C\u0123is","Ba\u0146uta","Be\u0101te","Bo\u013Ceslavs","Bro\u0146islavs","B\u0101rbala","Dagm\u0101ra","Dainuv\u012Bte","Di\u0101na","Dzirkst\u012Bte","Dz\u012Ble","D\u0101gs","D\u0101rta","D\u0101vids","D\u0101vis","Edgars","Eduards","Edv\u012Bns","Ed\u012Bte","Ed\u017Eus","Eg\u012Bls","Ein\u0101rs","Ei\u017Eens","Elfr\u012Bda","Elm\u0101rs","Elm\u012Bra","Elv\u012Bra","El\u012Bna","El\u012Bza","Em\u012Blija","Em\u012Bls","Erm\u012Bns","Ernest\u012Bne","Erv\u012Bns","Evel\u012Bna","Fr\u012Bda","Gabriela","Gudr\u012Bte","Gun\u0101rs","Hel\u0113na","Herm\u012Bne","Ilgm\u0101rs","Ilm\u0101rs","Indri\u0137is","Ingm\u0101rs","Ingr\u012Bda","Ing\u016Bna","In\u0101ra","In\u0101rs","Ir\u0113na","Jan\u012Bna","Jasm\u012Bna","Jautr\u012Bte","Jevge\u0146ijs","Jud\u012Bte","Jur\u0123is","Just\u012Bne","J\u0101nis","J\u0101zeps","J\u0113kabs","J\u016Blija","J\u016Blijs","J\u016Bsma","Kar\u012Bna","Katr\u012Bna","Kl\u0101ra","Kl\u0101vs","Konr\u0101ds","Konstant\u012Bns","Kristi\u0101na","Kristi\u0101ns","Krist\u012Bne","Kri\u0161j\u0101nis","Kri\u0161s","K\u0101rlis","Lav\u012Bze","Leont\u012Bne","Leon\u012Bda","Leon\u012Bds","Lili\u0101na","Li\u0101na","Lu\u012Bze","L\u0101sma","L\u012Bba","L\u012Bga","L\u012Bksma","L\u012Bna","L\u012Bva","L\u012Bvija","L\u012Bze","L\u016Bcija","Malv\u012Bne","Mar\u0123ers","Mat\u012Bss","Mi\u0137elis","Modr\u012Bte","Monv\u012Bds","Mud\u012Bte","M\u0101ra","M\u0101rcis","M\u0101ris","M\u0101rti\u0146\u0161","M\u0101r\u012Bte","M\u0113tra","Nikl\u0101vs","Oj\u0101rs","Ol\u012Bvija","Ot\u012Blija","O\u013Cegs","O\u013C\u0123erts","Patr\u012Bcija","Paul\u012Bne","P\u0101rsla","P\u0101vils","P\u0113teris","Regn\u0101rs","Reg\u012Bna","Ren\u0101rs","Ren\u0101te","Ri\u010Dards","Rom\u0101ns","Rud\u012Bte","R\u016Bdis","R\u016Bdolfs","R\u016Bsi\u0146\u0161","R\u016Bta","Sab\u012Bne","Sarm\u012Bte","Saulcer\u012Bte","Skaidr\u012Bte","Sp\u012Bdola","Tam\u0101ra","Ter\u0113ze","Tr\u012Bne","T\u0101lis","T\u0101livaldis","T\u0101lr\u012Bts","T\u012Bna","Und\u012Bne","U\u0123is","Valdem\u0101rs","Valent\u012Bna","Valent\u012Bns","Val\u0113rija","Val\u0113rijs","Vijol\u012Bte","Vilhelm\u012Bne","Vilm\u0101rs","Vit\u0101lijs","Vizbul\u012Bte","Voldem\u0101rs","V\u0113sma","Zelt\u012Bte","Zied\u012Bte","Zigfr\u012Bda","Zigfr\u012Bds","Zigm\u0101rs","Zigr\u012Bda","Z\u012Ble","\u0100dams","\u0100dolfs","\u0100rija","\u0100rijs","\u0100ris","\u0112rika","\u0112riks","\u0112valds","\u0122ederts","\u0122ertr\u016Bde","\u0122irts","\u012Arisa","\u017Danete","\u017Danis","\u017Danna","\u017Denija","\u017Dub\u012Bte"],female:["Akvel\u012Bna","Albert\u012Bne","Alv\u012Bne","Am\u0101lija","Astr\u012Bda","Aur\u0113lija","A\u012Bda","Ba\u0146uta","Be\u0101te","B\u0101rbala","Dagm\u0101ra","Dainuv\u012Bte","Di\u0101na","Dzirkst\u012Bte","Dz\u012Ble","D\u0101rta","Ed\u012Bte","Elfr\u012Bda","Elm\u012Bra","Elv\u012Bra","El\u012Bna","El\u012Bza","Em\u012Blija","Ernest\u012Bne","Evel\u012Bna","Fr\u012Bda","Gabriela","Gudr\u012Bte","Hel\u0113na","Herm\u012Bne","Ingr\u012Bda","Ing\u016Bna","In\u0101ra","Ir\u0113na","Jan\u012Bna","Jasm\u012Bna","Jautr\u012Bte","Jud\u012Bte","Just\u012Bne","J\u016Blija","J\u016Bsma","Kar\u012Bna","Katr\u012Bna","Kl\u0101ra","Kristi\u0101na","Krist\u012Bne","Lav\u012Bze","Leont\u012Bne","Leon\u012Bda","Lili\u0101na","Li\u0101na","Lu\u012Bze","L\u0101sma","L\u012Bba","L\u012Bga","L\u012Bksma","L\u012Bna","L\u012Bva","L\u012Bvija","L\u012Bze","L\u016Bcija","Malv\u012Bne","Modr\u012Bte","Mud\u012Bte","M\u0101ra","M\u0101r\u012Bte","M\u0113tra","Ol\u012Bvija","Ot\u012Blija","Patr\u012Bcija","Paul\u012Bne","P\u0101rsla","Reg\u012Bna","Ren\u0101te","Rud\u012Bte","R\u016Bta","Sab\u012Bne","Sarm\u012Bte","Saulcer\u012Bte","Skaidr\u012Bte","Sp\u012Bdola","Tam\u0101ra","Ter\u0113ze","Tr\u012Bne","T\u012Bna","Und\u012Bne","Valent\u012Bna","Val\u0113rija","Vijol\u012Bte","Vilhelm\u012Bne","Vizbul\u012Bte","V\u0113sma","Zelt\u012Bte","Zied\u012Bte","Zigfr\u012Bda","Zigr\u012Bda","Z\u012Ble","\u0100rija","\u0112rika","\u0122ertr\u016Bde","\u012Arisa","\u017Danete","\u017Danna","\u017Denija","\u017Dub\u012Bte"],male:["Ain\u0101rs","Alfr\u0113ds","Alo\u012Bzs","Andris","And\u017Eejs","And\u017Es","Anrijs","An\u0161lavs","Ark\u0101dijs","Arm\u012Bns","Art\u016Brs","Arv\u012Bds","A\u013C\u0123irds","A\u013C\u0123is","Bo\u013Ceslavs","Bro\u0146islavs","D\u0101gs","D\u0101vids","D\u0101vis","Edgars","Eduards","Edv\u012Bns","Ed\u017Eus","Eg\u012Bls","Ein\u0101rs","Ei\u017Eens","Elm\u0101rs","Em\u012Bls","Erm\u012Bns","Erv\u012Bns","Gun\u0101rs","Ilgm\u0101rs","Ilm\u0101rs","Indri\u0137is","Ingm\u0101rs","In\u0101rs","Jevge\u0146ijs","Jur\u0123is","J\u0101nis","J\u0101zeps","J\u0113kabs","J\u016Blijs","Kl\u0101vs","Konr\u0101ds","Konstant\u012Bns","Kristi\u0101ns","Kri\u0161j\u0101nis","Kri\u0161s","K\u0101rlis","Leon\u012Bds","Mar\u0123ers","Mat\u012Bss","Mi\u0137elis","Monv\u012Bds","M\u0101rcis","M\u0101ris","M\u0101rti\u0146\u0161","Nikl\u0101vs","Oj\u0101rs","O\u013Cegs","O\u013C\u0123erts","P\u0101vils","P\u0113teris","Regn\u0101rs","Ren\u0101rs","Ri\u010Dards","Rom\u0101ns","R\u016Bdis","R\u016Bdolfs","R\u016Bsi\u0146\u0161","T\u0101lis","T\u0101livaldis","T\u0101lr\u012Bts","U\u0123is","Valdem\u0101rs","Valent\u012Bns","Val\u0113rijs","Vilm\u0101rs","Vit\u0101lijs","Voldem\u0101rs","Zigfr\u012Bds","Zigm\u0101rs","\u0100dams","\u0100dolfs","\u0100rijs","\u0100ris","\u0112riks","\u0112valds","\u0122ederts","\u0122irts","\u017Danis"]};var G=["risin\u0101jumu","programmu","dro\u0161\u012Bbas","izp\u0113tes","m\u0101rketinga","vad\u012Bbas","izveides","integr\u0101ciju","funkcionalit\u0101tes","taktikas","identit\u0101tes","tirgus","grupas","noda\u013Cas","pielietojumu","optimiz\u0101cijas","oper\u0101ciju","infrastrukt\u016Bras","intraneta","sakaru","kvalit\u0101tes","mobilit\u0101tes","kontu","datu","kreat\u012Bvais","konfigur\u0101cijas","gr\u0101matojumu","sadarb\u012Bbas","lietojam\u012Bbas","m\u0113r\u012Bjumu"];var E=["Galvenais","Vec\u0101kais","Tie\u0161o","Korporat\u012Bv\u0101s","Produktu","Re\u0123ion\u0101l\u0101s","Rajona","Klientu","Invest\u012Bciju","Starptautisk\u0101s","Iek\u0161\u0113j\u0101s","Personu"];var _=["uzraugs","vad\u012Bt\u0101js","p\u0101rst\u0101vis","oficieris","mened\u017Eers","in\u017Eenieris","speci\u0101lists","direktors","koordinators","administrators","arhitekts","anal\u012Bti\u0137is","dizainers","pl\u0101not\u0101js","p\u0101rraugs","tehni\u0137is","izstr\u0101d\u0101t\u0101js","producents","konsultants","asistents","a\u0123ents","strat\u0113\u0123is"];var q={generic:["Aigare","Aigars","Alksne","Alksnis","Andersone","Andersons","Ar\u0101ja","Ar\u0101js","Aspere","Aspers","Auzi\u0146a","Auzi\u0146\u0161","Baj\u0101re","Baj\u0101rs","Balode","Balodis","Barone","Barons","Bergmane","Bergmanis","Be\u013Cavske","Be\u013Cavskis","Birzi\u0146a","Birzi\u0146\u0161","Birzniece","Birznieks","Biseniece","Bisenieks","Blaua","Blaus","Blekte","Bondare","Bondars","Bre\u0146\u0137e","Bre\u0146\u0137is","Briede","Briedis","Brunkevi\u010Da","Brunkevi\u010Ds","Budreiko","Buile","Builis","Bu\u0161a","Bu\u0161s","B\u0113rzi\u0146a","B\u0113rzi\u0146\u0161","B\u0113rz\u012Bte","B\u0113rz\u012Bts","B\u012Bri\u0146a","B\u012Bri\u0146\u0161","Cauna","Caune","Celma","Celmi\u0146a","Celmi\u0146\u0161","Celms","C\u0101l\u012Bte","C\u0101l\u012Btis","C\u012Brule","C\u012Brulis","Danielsone","Danielsons","De\u0123e","De\u0123is","Dombrovska","Dombrovskis","Dreimane","Dreimanis","Dzene","Dzenis","Dzirkale","Dzirkalis","D\u012Bri\u0137e","D\u012Bri\u0137is","Egl\u012Bte","Egl\u012Btis","Endzi\u0146a","Endzi\u0146\u0161","Fogele","Fogelis","Freimane","Freimanis","Gaile","Gailis","Gail\u012Bte","Gail\u012Btis","Gasj\u016Bne","Gasj\u016Bns","Gibala","Graudi\u0146a","Graudi\u0146\u0161","Gribuste","Gribusts","Groduma","Grodums","Gr\u012Bnberga","Gr\u012Bnbergs","Gr\u016Bba","Gr\u016Bbe","Ivanova","Ivanovs","Jankovska","Jankovskis","Jansone","Jansons","Jukuma","Jukums","Jumi\u0137e","Jumi\u0137is","J\u0113kabsone","J\u0113kabsons","Kalna","Kalni\u0146a","Kalni\u0146\u0161","Kalns","Kaln\u0101ja","Kaln\u0101js","Kal\u0113ja","Kal\u0113js","Karlsone","Karlsons","Kauli\u0146a","Kauli\u0146\u0161","Koha","Kohs","Koka","Koks","Krance","Krancis","Krasti\u0146a","Krasti\u0146\u0161","Kraule","Kraulis","Krauze","Krieva","Krievi\u0146a","Krievi\u0146\u0161","Krievs","Kronberga","Kronbergs","Kr\u0113sli\u0146a","Kr\u0113sli\u0146\u0161","Kr\u016Bmi\u0146a","Kr\u016Bmi\u0146\u0161","Kulmane","Kulmanis","Kurzemniece","Kurzemnieks","K\u0101rkli\u0146a","K\u0101rkli\u0146\u0161","K\u013Cavi\u0146a","K\u013Cavi\u0146\u0161","Laivi\u0146a","Laivi\u0146\u0161","Landmane","Landmanis","Lapi\u0146a","Lapi\u0146\u0161","Lapsi\u0146a","Lapsi\u0146\u0161","Lasmane","Lasmanis","Latkovska","Latkovskis","Lauberga","Laubergs","Legzdi\u0146a","Legzdi\u0146\u0161","Leji\u0146a","Leji\u0146\u0161","Lejniece","Lejnieks","Lielmane","Lielmanis","Liepa","Liepi\u0146a","Liepi\u0146\u0161","Lodi\u0146a","Lodi\u0146\u0161","Logina","Logins","Lo\u010Da","Lo\u010Ds","L\u0101ce","L\u0101cis","L\u012Bce","L\u012Bcis","L\u012Bdaka","L\u012Bdaks","L\u016Bse","L\u016Bsis","Matisone","Matisons","Mazj\u0101ne","Mazj\u0101nis","Medne","Mednis","Meiere","Meiers","Mek\u0161a","Mek\u0161s","Meldere","Melderis","Melngaile","Melngailis","Me\u0161k\u016Bna","Me\u0161k\u016Bne","Me\u0161k\u016Bns","Mihailova","Mihailovs","Mi\u0137elsone","Mi\u0137elsons","Morozova","Morozovs","Muceniece","Mucenieks","Mui\u017Eniece","Mui\u017Enieks","Nami\u0137e","Nami\u0137is","Niedra","Ose","Osis","Ostrovska","Ostrovskis","Ozere","Ozers","Ozola","Ozoli\u0146a","Ozoli\u0146\u0161","Ozols","Pakalniete","Pakalnietis","Paltere","Palters","Pau\u013Cuka","Pau\u013Cuks","Pavlovska","Pavlovskis","Pence","Pencis","Petrovska","Petrovskis","Podniece","Podnieks","Podzi\u0146a","Podzi\u0146\u0161","Pole","Polis","Porgante","Porgants","Pretkalni\u0146a","Pretkalni\u0146\u0161","Prid\u0101ne","Prid\u0101ns","Pried\u012Bte","Pried\u012Btis","Putni\u0146a","Putni\u0146\u0161","Pu\u0137\u012Bte","Pu\u0137\u012Btis","P\u0113rkona","P\u0113rkons","P\u0113tersone","P\u0113tersons","Ratniece","Ratnieks","Rieksti\u0146a","Rieksti\u0146\u0161","Romanovska","Romanovskis","Rozenbaha","Rozenbahs","Roz\u012Bte","Roz\u012Btis","Ro\u017Ekalne","Ro\u017Ekalns","Rubene","Rubenis","Rudz\u012Bte","Rudz\u012Btis","Runce","Runcis","Salmi\u0146a","Salmi\u0146\u0161","Sauliete","Saulietis","Ser\u017Eante","Ser\u017Eants","Sietniece","Sietnieks","Sili\u0146a","Sili\u0146\u0161","Skudra","Smu\u0123e","Smu\u0123is","Sondore","Sondors","Sos\u0101re","Sos\u0101rs","Spro\u0123e","Spro\u0123is","Spr\u016Bde","Strauti\u0146a","Strauti\u0146\u0161","Strautmane","Strautmanis","Strazdi\u0146a","Strazdi\u0146\u0161","Sukute","Sukuts","S\u0113j\u0113ja","S\u0113j\u0113js","S\u012Ble","S\u012Blis","Teic\u0101ne","Teic\u0101ns","Tilti\u0146a","Tilti\u0146\u0161","Tu\u010Da","Tu\u010Ds","T\u0101lberga","T\u0101lbergs","T\u012Brele","T\u012Brelis","Vaivade","Vaivads","Valtere","Valters","Vanaga","Vanags","Vasile","Vasils","Vecumniece","Vecumnieks","Veinberga","Veinbergs","Veisa","Veiss","Vilka","Vilks","Vilsone","Vilsons","Vintere","Vinters","Vi\u013Cuma","Vi\u013Cums","Volle","Vollis","V\u0101ciete","V\u0101cietis","V\u012Bti\u0146a","V\u012Bti\u0146\u0161","V\u012Btola","V\u012Btoli\u0146a","V\u012Btoli\u0146\u0161","V\u012Btols","V\u012Btuma","V\u012Btums","Zari\u0146a","Zari\u0146\u0161","Zeidmane","Zeidmanis","Zelti\u0146a","Zelti\u0146\u0161","Ziemele","Ziemelis","Zunda","Zvaigzne","Zvejniece","Zvejnieks","Zviedre","Zviedrs","Zvinele","Zvinelis","Z\u0101l\u012Bte","Z\u0101l\u012Btis","Z\u012Bbere","Z\u012Bbers","\u0100bele","\u0100boli\u0146a","\u0100boli\u0146\u0161","\u0100bolti\u0146a","\u0100bolti\u0146\u0161","\u010Cudara","\u010Cudars","\u0136eizare","\u0136eizars","\u0136\u0113ni\u0146a","\u0136\u0113ni\u0146\u0161","\u0160ileiko","\u0160irova","\u0160irovs","\u0160mite","\u0160mits","\u0160teina","\u0160teins","\u0160\u012Brante","\u0160\u012Brants","\u016Adre","\u016Adris"],female:["Aigare","Alksne","Andersone","Ar\u0101ja","Aspere","Auzi\u0146a","Baj\u0101re","Balode","Barone","Bergmane","Be\u013Cavske","Birzi\u0146a","Birzniece","Biseniece","Blaua","Blekte","Bondare","Bre\u0146\u0137e","Briede","Brunkevi\u010Da","Budreiko","Buile","Bu\u0161a","B\u0113rzi\u0146a","B\u0113rz\u012Bte","B\u012Bri\u0146a","Cauna","Celma","Celmi\u0146a","C\u0101l\u012Bte","C\u012Brule","Danielsone","De\u0123e","Dombrovska","Dreimane","Dzene","Dzirkale","D\u012Bri\u0137e","Egl\u012Bte","Endzi\u0146a","Fogele","Freimane","Gaile","Gail\u012Bte","Gasj\u016Bne","Gibala","Graudi\u0146a","Gribuste","Groduma","Gr\u012Bnberga","Gr\u016Bba","Ivanova","Jankovska","Jansone","Jukuma","Jumi\u0137e","J\u0113kabsone","Kalna","Kalni\u0146a","Kaln\u0101ja","Kal\u0113ja","Karlsone","Kauli\u0146a","Koha","Koka","Krance","Krasti\u0146a","Kraule","Krauze","Krieva","Krievi\u0146a","Kronberga","Kr\u0113sli\u0146a","Kr\u016Bmi\u0146a","Kulmane","Kurzemniece","K\u0101rkli\u0146a","K\u013Cavi\u0146a","Laivi\u0146a","Landmane","Lapi\u0146a","Lapsi\u0146a","Lasmane","Latkovska","Lauberga","Legzdi\u0146a","Leji\u0146a","Lejniece","Lielmane","Liepa","Liepi\u0146a","Lodi\u0146a","Logina","Lo\u010Da","L\u0101ce","L\u012Bce","L\u012Bdaka","L\u016Bse","Matisone","Mazj\u0101ne","Medne","Meiere","Mek\u0161a","Meldere","Melngaile","Me\u0161k\u016Bna","Me\u0161k\u016Bne","Mihailova","Mi\u0137elsone","Morozova","Muceniece","Mui\u017Eniece","Nami\u0137e","Niedra","Ose","Ostrovska","Ozere","Ozola","Ozoli\u0146a","Pakalniete","Paltere","Pau\u013Cuka","Pavlovska","Pence","Petrovska","Podniece","Podzi\u0146a","Pole","Porgante","Pretkalni\u0146a","Prid\u0101ne","Pried\u012Bte","Putni\u0146a","Pu\u0137\u012Bte","P\u0113rkona","P\u0113tersone","Ratniece","Rieksti\u0146a","Romanovska","Rozenbaha","Roz\u012Bte","Ro\u017Ekalne","Rubene","Rudz\u012Bte","Runce","Salmi\u0146a","Sauliete","Ser\u017Eante","Sietniece","Sili\u0146a","Skudra","Smu\u0123e","Sondore","Sos\u0101re","Spro\u0123e","Spr\u016Bde","Strauti\u0146a","Strautmane","Strazdi\u0146a","Sukute","S\u0113j\u0113ja","S\u012Ble","Teic\u0101ne","Tilti\u0146a","Tu\u010Da","T\u0101lberga","T\u012Brele","Vaivade","Valtere","Vanaga","Vasile","Vecumniece","Veinberga","Veisa","Vilka","Vilsone","Vintere","Vi\u013Cuma","Volle","V\u0101ciete","V\u012Bti\u0146a","V\u012Btola","V\u012Btoli\u0146a","V\u012Btuma","Zari\u0146a","Zeidmane","Zelti\u0146a","Ziemele","Zunda","Zvaigzne","Zvejniece","Zviedre","Zvinele","Z\u0101l\u012Bte","Z\u012Bbere","\u0100bele","\u0100boli\u0146a","\u0100bolti\u0146a","\u010Cudara","\u0136eizare","\u0136\u0113ni\u0146a","\u0160ileiko","\u0160irova","\u0160mite","\u0160teina","\u0160\u012Brante","\u016Adre"],male:["Aigars","Alksnis","Andersons","Ar\u0101js","Aspers","Auzi\u0146\u0161","Baj\u0101rs","Balodis","Barons","Bergmanis","Be\u013Cavskis","Birzi\u0146\u0161","Birznieks","Bisenieks","Blaus","Blekte","Bondars","Bre\u0146\u0137is","Briedis","Brunkevi\u010Ds","Budreiko","Builis","Bu\u0161s","B\u0113rzi\u0146\u0161","B\u0113rz\u012Bts","B\u012Bri\u0146\u0161","Caune","Celmi\u0146\u0161","Celms","C\u0101l\u012Btis","C\u012Brulis","Danielsons","De\u0123is","Dombrovskis","Dreimanis","Dzenis","Dzirkalis","D\u012Bri\u0137is","Egl\u012Btis","Endzi\u0146\u0161","Fogelis","Freimanis","Gailis","Gail\u012Btis","Gasj\u016Bns","Gibala","Graudi\u0146\u0161","Gribusts","Grodums","Gr\u012Bnbergs","Gr\u016Bbe","Ivanovs","Jankovskis","Jansons","Jukums","Jumi\u0137is","J\u0113kabsons","Kalni\u0146\u0161","Kalns","Kaln\u0101js","Kal\u0113js","Karlsons","Kauli\u0146\u0161","Kohs","Koks","Krancis","Krasti\u0146\u0161","Kraulis","Krauze","Krievi\u0146\u0161","Krievs","Kronbergs","Kr\u0113sli\u0146\u0161","Kr\u016Bmi\u0146\u0161","Kulmanis","Kurzemnieks","K\u0101rkli\u0146\u0161","K\u013Cavi\u0146\u0161","Laivi\u0146\u0161","Landmanis","Lapi\u0146\u0161","Lapsi\u0146\u0161","Lasmanis","Latkovskis","Laubergs","Legzdi\u0146\u0161","Leji\u0146\u0161","Lejnieks","Lielmanis","Liepa","Liepi\u0146\u0161","Lodi\u0146\u0161","Logins","Lo\u010Ds","L\u0101cis","L\u012Bcis","L\u012Bdaks","L\u016Bsis","Matisons","Mazj\u0101nis","Mednis","Meiers","Mek\u0161s","Melderis","Melngailis","Me\u0161k\u016Bns","Mihailovs","Mi\u0137elsons","Morozovs","Mucenieks","Mui\u017Enieks","Nami\u0137is","Niedra","Osis","Ostrovskis","Ozers","Ozoli\u0146\u0161","Ozols","Pakalnietis","Palters","Pau\u013Cuks","Pavlovskis","Pencis","Petrovskis","Podnieks","Podzi\u0146\u0161","Polis","Porgants","Pretkalni\u0146\u0161","Prid\u0101ns","Pried\u012Btis","Putni\u0146\u0161","Pu\u0137\u012Btis","P\u0113rkons","P\u0113tersons","Ratnieks","Rieksti\u0146\u0161","Romanovskis","Rozenbahs","Roz\u012Btis","Ro\u017Ekalns","Rubenis","Rudz\u012Btis","Runcis","Salmi\u0146\u0161","Saulietis","Ser\u017Eants","Sietnieks","Sili\u0146\u0161","Skudra","Smu\u0123is","Sondors","Sos\u0101rs","Spro\u0123is","Spr\u016Bde","Strauti\u0146\u0161","Strautmanis","Strazdi\u0146\u0161","Sukuts","S\u0113j\u0113js","S\u012Blis","Teic\u0101ns","Tilti\u0146\u0161","Tu\u010Ds","T\u0101lbergs","T\u012Brelis","Vaivads","Valters","Vanags","Vasils","Vecumnieks","Veinbergs","Veiss","Vilks","Vilsons","Vinters","Vi\u013Cums","Vollis","V\u0101cietis","V\u012Bti\u0146\u0161","V\u012Btoli\u0146\u0161","V\u012Btols","V\u012Btums","Zari\u0146\u0161","Zeidmanis","Zelti\u0146\u0161","Ziemelis","Zunda","Zvaigzne","Zvejnieks","Zviedrs","Zvinelis","Z\u0101l\u012Btis","Z\u012Bbers","\u0100bele","\u0100boli\u0146\u0161","\u0100bolti\u0146\u0161","\u010Cudars","\u0136eizars","\u0136\u0113ni\u0146\u0161","\u0160ileiko","\u0160irovs","\u0160mits","\u0160teins","\u0160\u012Brants","\u016Adris"]};var I={female:[{value:"{{person.last_name.female}}",weight:8},{value:"{{person.last_name.female}}-{{person.last_name.female}}",weight:2}],male:[{value:"{{person.last_name.male}}",weight:8},{value:"{{person.last_name.male}}-{{person.last_name.male}}",weight:2}]};var y=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}} {{person.suffix}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:7}];var O={generic:["Biedrs","Dr.","Prof."],female:["Biedrs","Dr.","Prof."],male:["Biedrs","Dr.","Prof."]};var C=["k-dze","kundze"];var oa={first_name:T,job_area:G,job_descriptor:E,job_type:_,last_name:q,last_name_pattern:I,name:y,prefix:O,suffix:C},N=oa;var F=["(371)6#######","+371 6#######","6#######"];var w=["+3716#######"];var U=["6# ### ###"];var na={human:F,international:w,national:U},H=na;var ua={format:H},Q=ua;var la={cell_phone:i,color:o,commerce:l,company:p,date:b,internet:f,location:x,lorem:Z,metadata:J,person:N,phone_number:Q},W= exports.a =la;var rs=new (0, _chunkZKNYQOPPcjs.n)({locale:[W,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = W; exports.b = rs;
