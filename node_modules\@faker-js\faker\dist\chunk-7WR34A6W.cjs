"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunk63UJVCQ4cjs = require('./chunk-63UJVCQ4.cjs');var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var i=["biz","ca","com","info","name","net","org","qc.ca"];var n=["gmail.com","hotmail.com","yahoo.ca"];var P={domain_suffix:i,free_email:n},a=P;var f=["{{location.city_prefix}} {{person.firstName}}{{location.city_suffix}}","{{location.city_prefix}} {{person.firstName}}","{{person.firstName}}{{location.city_suffix}}","{{person.last_name.generic}}{{location.city_suffix}}"];var m=[{alpha2:"CA",alpha3:"CAN",numeric:"124"}];var p=["A#? #?#","B#? #?#","C#? #?#","E#? #?#","G#? #?#","H#? #?#","J#? #?#","K#? #?#","L#? #?#","M#? #?#","N#? #?#","P#? #?#","R#? #?#","S#? #?#","T#? #?#","V#? #?#","X#? #?#","Y#? #?#"];var u=["Alberta","Colombie-Britannique","Manitoba","Nouveau-Brunswick","Terre-Neuve-et-Labrador","Nouvelle-\xC9cosse","Territoires du Nord-Ouest","Nunavut","Ontario","\xCEle-du-Prince-\xC9douard","Qu\xE9bec","Saskatchewan","Yukon"];var l=["AB","BC","MB","NB","NL","NS","NU","NT","ON","PE","QC","SK","YK"];var c=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}"];var A={city_pattern:f,country_code:m,postcode:p,state:u,state_abbr:l,street_pattern:c},s=A;var B={title:"French (Canada)",code:"fr_CA",country:"CA",language:"fr",endonym:"Fran\xE7ais (Canada)",dir:"ltr",script:"Latn"},d=B;var x={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var g={last_name_pattern:x},N=g;var _=["### ###-####","1 ### ###-####","### ###-####, poste ###"];var y=["+1##########"];var b=["(###) ###-####"];var L={human:_,international:y,national:b},h=L;var k={format:h},C=k;var v={internet:a,location:s,metadata:d,person:N,phone_number:C},D= exports.a =v;var yt=new (0, _chunkZKNYQOPPcjs.n)({locale:[D,_chunk63UJVCQ4cjs.a,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = D; exports.b = yt;
