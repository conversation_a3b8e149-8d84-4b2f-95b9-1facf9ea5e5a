"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var i=["<PERSON><PERSON>\xEDsio Azevedo","Ariano Suassuna","<PERSON>\xE3es","<PERSON><PERSON><PERSON>","<PERSON>","Carolina Maria de Jesus","Castro Alves","Cec\xEDlia Meireles","Clarice Lispector","Concei\xE7\xE3o Evaristo","<PERSON> Coralina","Cruz e Sousa","Gon\xE7alves <PERSON>","<PERSON>\xF3<PERSON>","Jorge Am<PERSON>","Jo<PERSON>\xE9 de Alencar","Jo\xE3o <PERSON>\xE3es Rosa","<PERSON> Verissi<PERSON>","Lygia Bojunga","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","M\xE1rio de Andrade","<PERSON>","<PERSON>lavo Bilac","<PERSON>","Rubem Fonseca","Ziraldo","\xC1lvares de Azevedo"];var r=["Audiolivro","Capa dura","Capa mole","Ebook"];var t=["Aventura","Biografia","Cl\xE1ssico","Com\xE9dia","Detetive","Drama","Fantasia","Faroeste","Fic\xE7\xE3o Cient\xEDfica","Fic\xE7\xE3o Hist\xF3rica","Filosofia","Literatura Infantil","Mem\xF3rias","Mist\xE9rio","Mitologia","Neg\xF3cios","Poesia","Psicologia","Quadrinhos","Religi\xE3o","Romance","Romance Gr\xE1fico","Suspense","Terror"];var n=["Companhia das Letras","Editora Abril","Editora Aleph","Editora Antof\xE1gica","Editora Conrad","Editora Darkside","Editora FTD","Editora Gente","Editora HarperCollins Brasil","Editora Intr\xEDnseca","Editora L&PM","Editora Martin Claret","Editora Melhoramentos","Editora Moderna","Editora Panda Books","Editora Pipoca & Nanquim","Editora Planeta de Livros Brasil","Editora Rocco","Editora Saraiva","Editora Sextante","Editora Viseu","Editora Voo","Globo Livros"];var s=["Ed Mort","O Tempo e o Vento","Os Subterr\xE2neos da Liberdade","S\xE9rie Vaga-Lume","S\xEDtio do Picapau Amarelo","Trilogia do Descobrimento"];var l=["A Estrela sobe","A coleira do c\xE3o","A escrava Isaura","A hora da estrela","A moreninha","A m\xE3o e a luva","A paix\xE3o segundo G.H.","A rosa do povo","A vida como ela \xE9","Ang\xFAstia","As meninas","Ba\xFA de ossos","Broqu\xE9is","Br\xE1s, bexiga e barra funda","Cana\xE3","Cartas chilenas","Casa grande e senzala","Cascalho","Claro enigma","Contos gauchescos","Corpo de baile","Cr\xF4nica da casa assassinada","Dom Casmurro","Dona Flor e seus dois maridos","Espumas flutuantes","Estrela da manh\xE3","Eu","Farda, fard\xE3o, camisola de dormir","Fogo morto","Fundador","Gabriela, cravo e canela","Gram\xE1tica expositiva do ch\xE3o","Grande sert\xE3o: veredas","Iai\xE1 Garcia","Inoc\xEAncia","Inven\xE7\xE3o de Orfeu","Iracema","Jubiab\xE1","Lavoura arcaica","La\xE7os de fam\xEDlia","Libertinagem","Luc\xEDola","Macuna\xEDma","Malagueta, Perus e Bacana\xE7o","Mar morto","Mar\xEDlia de Dirceu","Memorial de Aires","Mem\xF3rias do c\xE1rcere","Mem\xF3rias p\xF3stumas de Br\xE1s Cubas","Mem\xF3rias sentimentais de Jo\xE3o Miramar","Mem\xF3rias sgto de mil\xEDcias","Minha forma\xE7\xE3o","Morte e vida severina","Noite na taverna","O ateneu","O coronel e o lobisomem","O corti\xE7o","O dem\xF4nio familiar","O encontro marcado","O feij\xE3o e o sonho","O guarani","O mez da grippe","O pagador de promessas","O quinze","O tempo e o vento","O uraguai","O vampiro de Curitiba","Obra po\xE9tica","Os cavalinhos de platiplanto","Os ratos","Os sert\xF5es","Pap\xE9is avulsos","Paulic\xE9ia desvairada","Pedra Bonita","Poema sujo","Poesias","Primeiras est\xF3rias","Primeiros Cantos","Quarup","Quincas Borba","Ra\xEDzes do Brasil","Ritmo dissoluto","Romance da Pedra do Reino","Romanceiro da inconfid\xEAncia","Sagarana","Senhora","Serm\xF5es","S\xE3o Bernardo","Tenda dos milagres","Terras do sem fim","Triste fim de Policarpo Quaresma","Uma aprendizagem","Veronika decide morrer","Vestido de noiva","Vidas secas","Viva o povo brasileiro","Zero","\xD3pera dos mortos"];var ia={author:i,format:r,genre:t,publisher:n,series:s,title:l},u=ia;var m=["amarelo","ametista","azul","azul celeste","azul marinho","azul petr\xF3leo","a\xE7afr\xE3o","bord\xF4","bronze","caramelo","castanho","cenoura","cinza","cobre","coral","dourado","escarlate","esmeralda","ferrugem","fuligem","f\xFAchsia","gren\xE1","jade","laranja","lil\xE1s","lim\xE3o","madeira","magenta","marrom","ouro","pele","prata","preto","p\xFArpura","rosa","roxo","salm\xE3o","turquesa","verde","verde lima","verde-azulado","vermelho","violeta","\xE2mbar","\xEDndigo"];var ra={human:m},d=ra;var c=["Automotivo","Beb\xEA","Beleza","Brinquedos","Casa","Computadores","Crian\xE7as","Eletr\xF4nicos","Esportes","Ferramentas","Filmes","Industrial","Jardim","Jogos","J\xF3ias","Livros","Mercearia","M\xFAsica","Roupas","Sapatos","Sa\xFAde","Turismo"];var p={adjective:["Ergon\xF4mico","Fant\xE1stico","Feito \xE0 m\xE3o","Gen\xE9rico","Gostoso","Impressionante","Incr\xEDvel","Inteligente","Licenciado","Lindo","Lustroso","Pequeno","Pr\xE1tico","Refinado","R\xFAstico","Sem marca"],material:["Algod\xE3o","A\xE7o","Borracha","Concreto","Congelado","Fresco","Granito","Macio","Madeira","Metal","Pl\xE1stico"],product:["Atum","Bacon","Bicicleta","Bola","Cadeira","Cal\xE7as","Camiseta","Carro","Chap\xE9u","Computador","Frango","Luvas","Mesa","Mouse","Peixe","Pizza","Queijo","Sabonete","Salada","Salgadinhos","Salsicha","Sapatos","Teclado","Toalhas"]};var ta={department:c,product_name:p},f=ta;var b=["Com\xE9rcio","EIRELI","LTDA","S.A.","e Associados"];var g=["{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} e {{person.last_name.generic}}","{{person.last_name.generic}}-{{person.last_name.generic}}"];var na={legal_entity_type:b,name_pattern:g},M=na;var C={wide:["Abril","Agosto","Dezembro","Fevereiro","Janeiro","Julho","Junho","Maio","Mar\xE7o","Novembro","Outubro","Setembro"],abbr:["Abr","Ago","Dez","Fev","Jan","Jul","Jun","Mai","Mar","Nov","Out","Set"]};var v={wide:["Domingo","Quarta","Quinta","Segunda","Sexta","S\xE1bado","Ter\xE7a"],abbr:["Dom","Qua","Qui","Seg","Sex","S\xE1b","Ter"]};var sa={month:C,weekday:v},A=sa;var S=["biz","br","com","info","name","net","org"];var h=["bol.com.br","gmail.com","hotmail.com","live.com","yahoo.com"];var la={domain_suffix:S,free_email:h},x=la;var L=["#####","####","###"];var P=["{{person.firstName}}{{location.city_suffix}}","{{person.last_name.generic}}{{location.city_suffix}}"];var E=null;var B=[" do Descoberto"," de Nossa Senhora"," do Norte"," do Sul"];var D=["Afeganist\xE3o","Alb\xE2nia","Alg\xE9ria","Samoa","Andorra","Angola","Anguila","Antigua and Barbada","Argentina","Arm\xEAnia","Aruba","Austr\xE1lia","\xC1ustria","Azerbaij\xE3o","Bahamas","Bar\xE9m","Bangladesh","Barbados","B\xE9lgica","Belize","Benin","Bermuda","But\xE3o","Bol\xEDvia","B\xF4snia","Botsuana","Ilha Bouvet","Brasil","Arquip\xE9lago de Chagos","Ilhas Virgens","Brunei","Bulg\xE1ria","Burkina Faso","Burundi","Camboja","Camar\xF5es","Canad\xE1","Cabo Verde","Ilhas Caiman","Rep\xFAblica da \xC1frica Central","Chade","Chile","China","Ilha do Natal","Ilhas Cocos","Col\xF4mbia","Comores","Congo","Ilhas Cook","Costa Rica","Costa do Marfim","Cro\xE1cia","Cuba","Chipre","Rep\xFAblica Tcheca","Dinamarca","Jibuti","Dominica","Rep\xFAblica Dominicana","Equador","Egito","El Salvador","Guin\xE9 Equatorial","Eritreia","Est\xF4nia","Eti\xF3pia","Ilhas Faroe","Malvinas","Fiji","Finl\xE2ndia","Fran\xE7a","Guin\xE9 Francesa","Polin\xE9sia Francesa","Gab\xE3o","G\xE2mbia","Georgia","Alemanha","Gana","Gibraltar","Gr\xE9cia","Groel\xE2ndia","Granada","Guadalupe","Guatemala","Guernesey","Guin\xE9","Guin\xE9-Bissau","Guiana","Haiti","Ilhas Heard e McDonald","Vaticano","Honduras","Hong Kong","Hungria","Isl\xE2ndia","\xCDndia","Indon\xE9sia","Ir\xE3","Iraque","Irlanda","Ilha de Man","Israel","It\xE1lia","Jamaica","Jap\xE3o","Jersey","Jord\xE2nia","Cazaquist\xE3o","Qu\xEAnia","Quiribati","Coreia do Norte","Coreia do Sul","Kuwait","Quirguist\xE3o","Laos","Latvia","L\xEDbano","Lesoto","Lib\xE9ria","L\xEDbia","Liechtenstein","Litu\xE2nia","Luxemburgo","Macao","Maced\xF4nia","Madagascar","Malawi","Mal\xE1sia","Maldives","Mali","Malta","Ilhas Marshall","Martinica","Maurit\xE2nia","Maur\xEDcia","Maiote","M\xE9xico","Micron\xE9sia","Mold\xE1via","M\xF4naco","Mong\xF3lia","Montenegro","Montserrat","Marrocos","Mo\xE7ambique","Myanmar","Namibia","Nauru","Nepal","Antilhas Holandesas","Pa\xEDses Baixos","Nova Caledonia","Nova Zel\xE2ndia","Nicar\xE1gua","Nig\xE9ria","Niue","Ilha Norfolk","Marianas Setentrionais","Noruega","Om\xE3","Paquist\xE3o","Palau","Territ\xF3rio da Palestina","Panam\xE1","Papua-Nova Guin\xE9","Paraguai","Peru","Filipinas","Pol\xF4nia","Portugal","Porto Rico","Qatar","Rom\xEAnia","R\xFAssia","Ruanda","S\xE3o Bartolomeu","Santa Helena","Santa L\xFAcia","S\xE3o Martinho","S\xE3o Pedro e Miquel\xE3o","S\xE3o Vicente e Granadinas","San Marino","Sao Tom\xE9 e Pr\xEDncipe","Ar\xE1bia Saudita","Senegal","S\xE9rvia","Seicheles","Serra Leoa","Singapura","Eslov\xE1quia","Eslov\xEAnia","Ilhas Salom\xE3o","Som\xE1lia","\xC1frica do Sul","Ilhas Ge\xF3rgia do Sul e Sandwich do Sul","Espanha","Sri Lanka","Sud\xE3o","Suriname","Ilhas Svalbard & Jan Mayen","Suazil\xE2ndia","Su\xE9cia","Su\xED\xE7a","S\xEDria","Taiwan","Tajiquist\xE3o","Tanz\xE2nia","Tail\xE2ndia","Timor-Leste","Togo","Toquelau","Tonga","Trinidad e Tobago","Tun\xEDsia","Turquia","Turcomenist\xE3o","Turcas e Caicos","Tuvalu","Uganda","Ucr\xE2nia","Emirados \xC1rabes Unidos","Reino Unido","Estados Unidos da Am\xE9rica","Estados Unidos das Ilhas Virgens","Uruguai","Uzbequist\xE3o","Vanuatu","Venezuela","Vietn\xE3","Wallis e Futuna","I\xEAmen","Z\xE2mbia","Zimb\xE1bue"];var I=["#####-###"];var R=["Apto. ###","Sobrado ##","Casa #","Lote ##","Quadra ##"];var F=["Acre","Alagoas","Amap\xE1","Amazonas","Bahia","Cear\xE1","Distrito Federal","Esp\xEDrito Santo","Goi\xE1s","Maranh\xE3o","Mato Grosso","Mato Grosso do Sul","Minas Gerais","Par\xE1","Para\xEDba","Paran\xE1","Pernambuco","Piau\xED","Rio de Janeiro","Rio Grande do Norte","Rio Grande do Sul","Rond\xF4nia","Roraima","Santa Catarina","S\xE3o Paulo","Sergipe","Tocantins"];var G=["AC","AL","AP","AM","BA","CE","DF","ES","GO","MA","MT","MS","MG","PA","PB","PR","PE","PI","RJ","RN","RS","RO","RR","SC","SP","SE","TO"];var q=["{{location.street_prefix}} {{person.firstName}}","{{location.street_prefix}} {{person.lastName}}"];var y=["Rua","Avenida","Travessa","Alameda","Marginal","Rodovia"];var ua={building_number:L,city_pattern:P,city_prefix:E,city_suffix:B,country:D,postcode:I,secondary_address:R,state:F,state_abbr:G,street_pattern:q,street_prefix:y},N=ua;var z=["alias","consequatur","aut","perferendis","sit","voluptatem","accusantium","doloremque","aperiam","eaque","ipsa","quae","ab","illo","inventore","veritatis","et","quasi","architecto","beatae","vitae","dicta","sunt","explicabo","aspernatur","odit","fugit","sed","quia","consequuntur","magni","dolores","eos","qui","ratione","sequi","nesciunt","neque","dolorem","ipsum","dolor","amet","consectetur","adipisci","velit","non","numquam","eius","modi","tempora","incidunt","ut","labore","dolore","magnam","aliquam","quaerat","enim","ad","minima","veniam","quis","nostrum","exercitationem","ullam","corporis","nemo","ipsam","voluptas","suscipit","laboriosam","nisi","aliquid","ex","ea","commodi","autem","vel","eum","iure","reprehenderit","in","voluptate","esse","quam","nihil","molestiae","iusto","odio","dignissimos","ducimus","blanditiis","praesentium","laudantium","totam","rem","voluptatum","deleniti","atque","corrupti","quos","quas","molestias","excepturi","sint","occaecati","cupiditate","provident","perspiciatis","unde","omnis","iste","natus","error","similique","culpa","officia","deserunt","mollitia","animi","id","est","laborum","dolorum","fuga","harum","quidem","rerum","facilis","expedita","distinctio","nam","libero","tempore","cum","soluta","nobis","eligendi","optio","cumque","impedit","quo","porro","quisquam","minus","quod","maxime","placeat","facere","possimus","assumenda","repellendus","temporibus","quibusdam","illum","fugiat","nulla","pariatur","at","vero","accusamus","officiis","debitis","necessitatibus","saepe","eveniet","voluptates","repudiandae","recusandae","itaque","earum","hic","tenetur","a","sapiente","delectus","reiciendis","voluptatibus","maiores","doloribus","asperiores","repellat"];var ma={word:z},J=ma;var da={title:"Portuguese (Brazil)",code:"pt_BR",country:"BR",language:"pt",endonym:"Portugu\xEAs (Brasil)",dir:"ltr",script:"Latn"},T=da;var _={generic:["Alessandra","Alessandro","Alexandre","Alice","Aline","Al\xEDcia","Ana Clara","Ana J\xFAlia","Ana Laura","Ana Luiza","Anthony","Antonella","Ant\xF4nio","Arthur","Beatriz","Benjamin","Ben\xEDcio","Bernardo","Breno","Bruna","Bryan","Caio","Calebe","Carla","Carlos","Cau\xE3","Cec\xEDlia","Clara","C\xE9lia","C\xE9sar","Dalila","Daniel","Danilo","Davi","Davi Lucca","Deneval","Eduarda","Eduardo","Elisa","Elo\xE1","El\xEDsio","Emanuel","Emanuelly","Enzo","Enzo Gabriel","Esther","Fabiano","Fabr\xEDcia","Fabr\xEDcio","Feliciano","Felipe","Fel\xEDcia","Frederico","F\xE1bio","F\xE9lix","Gabriel","Gael","Giovanna","Guilherme","Gustavo","G\xFAbio","Heitor","Helena","Helo\xEDsa","Henrique","Hugo","H\xE9lio","Isaac","Isabel","Isabela","Isabella","Isabelly","Isadora","Isis","Jana\xEDna","Joana","Joaquim","Jo\xE3o","Jo\xE3o Lucas","Jo\xE3o Miguel","Jo\xE3o Pedro","J\xFAlia","J\xFAlio","J\xFAlio C\xE9sar","Karla","Kl\xE9ber","Ladislau","Lara","Larissa","Laura","Lav\xEDnia","Leonardo","Liz","Lorena","Lorenzo","Lorraine","Lucas","Lucca","Luiza","L\xEDvia","Mait\xEA","Manuela","Marcela","Marcelo","Marcos","Margarida","Maria","Maria Alice","Maria Cec\xEDlia","Maria Clara","Maria Eduarda","Maria Helena","Maria J\xFAlia","Maria Luiza","Mariana","Marina","Marli","Matheus","Meire","Melissa","Miguel","Morgana","Murilo","M\xE1rcia","M\xE9rcia","Nataniel","Nat\xE1lia","Nicolas","Noah","Norberto","N\xFAbia","Of\xE9lia","Pablo","Paula","Paulo","Pedro","Pedro Henrique","Pietro","Rafael","Rafaela","Raul","Rebeca","Ricardo","Roberta","Roberto","Salvador","Samuel","Sara","Sarah","Silas","Sirineu","Sophia","Su\xE9len","S\xEDlvia","Talita","Tertuliano","Th\xE9o","Valentina","Vicente","Vitor","Vit\xF3ria","V\xEDctor","Warley","Washington","Yago","Yango","Yasmin","Yuri","\xCDgor"],female:["Alessandra","Alice","Aline","Al\xEDcia","Ana Clara","Ana J\xFAlia","Ana Laura","Ana Luiza","Antonella","Beatriz","Bruna","Carla","Cec\xEDlia","Clara","C\xE9lia","Dalila","Eduarda","Elisa","Elo\xE1","Emanuelly","Esther","Fabr\xEDcia","Fel\xEDcia","Giovanna","Helena","Helo\xEDsa","Isabel","Isabela","Isabella","Isabelly","Isadora","Isis","Jana\xEDna","Joana","J\xFAlia","Karla","Lara","Larissa","Laura","Lav\xEDnia","Liz","Lorena","Lorraine","Luiza","L\xEDvia","Mait\xEA","Manuela","Marcela","Margarida","Maria","Maria Alice","Maria Cec\xEDlia","Maria Clara","Maria Eduarda","Maria Helena","Maria J\xFAlia","Maria Luiza","Mariana","Marina","Marli","Meire","Melissa","Morgana","M\xE1rcia","M\xE9rcia","Nat\xE1lia","N\xFAbia","Of\xE9lia","Paula","Rafaela","Rebeca","Roberta","Sara","Sarah","Sophia","Su\xE9len","S\xEDlvia","Talita","Valentina","Vit\xF3ria","Yasmin"],male:["Alessandro","Alexandre","Anthony","Ant\xF4nio","Arthur","Benjamin","Ben\xEDcio","Bernardo","Breno","Bryan","Caio","Calebe","Carlos","Cau\xE3","C\xE9sar","Daniel","Danilo","Davi","Davi Lucca","Deneval","Eduardo","El\xEDsio","Emanuel","Enzo","Enzo Gabriel","Fabiano","Fabr\xEDcio","Feliciano","Felipe","Frederico","F\xE1bio","F\xE9lix","Gabriel","Gael","Guilherme","Gustavo","G\xFAbio","Heitor","Henrique","Hugo","H\xE9lio","Isaac","Joaquim","Jo\xE3o","Jo\xE3o Lucas","Jo\xE3o Miguel","Jo\xE3o Pedro","J\xFAlio","J\xFAlio C\xE9sar","Kl\xE9ber","Ladislau","Leonardo","Lorenzo","Lucas","Lucca","Marcelo","Marcos","Matheus","Miguel","Murilo","Nataniel","Nicolas","Noah","Norberto","Pablo","Paulo","Pedro","Pedro Henrique","Pietro","Rafael","Raul","Ricardo","Roberto","Salvador","Samuel","Silas","Sirineu","Tertuliano","Th\xE9o","Vicente","Vitor","V\xEDctor","Warley","Washington","Yago","Yango","Yuri","\xCDgor"]};var O=["Solu\xE7\xF5es","Programa","Marca","Seguran\xE7a","Pesquisar","Marketing","Diretivas","Implementation","Implementa\xE7\xE3o","Funcionalidade","Resposta","Paradigma","T\xE1ticas","Identidade","Mercados","Grupo","Divis\xE3o","Aplica\xE7\xF5es","Otimiza\xE7\xE3o","Opera\xE7\xF5es","Infraestrutura","Intranet","Comunica\xE7\xF5es","Web","Branding","Qualidade","Assurance","Mobilidade","Contas","Dados","Criativo","Configuration","Presta\xE7\xE3o de contas","Intera\xE7\xF5es","Fatores","Usabilidade","M\xE9tricas"];var V=["L\xEDder","Senior","Direto","Corporativo","Din\xE2mico","Futuro","Produto","Nacional","Regional","Distrito","Central","Global","Cliente","Investidor","International","Legado","Avan\xE7ar","Interno","Humano","Chefe","Principal"];var H=["Supervisor","Associado","Executivo","Atentende","Policial","Gerente","Engenheiro","Especialista","Diretor","Coordenador","Administrador","Arquiteto","Analista","Designer","Planejador","Orquestrador","T\xE9cnico","Desenvolvedor","Produtor","Consultor","Assistente","Facilitador","Agente","Representante","Estrategista"];var j={generic:["Albuquerque","Barros","Batista","Braga","Carvalho","Costa","Franco","Macedo","Martins","Melo","Moraes","Moreira","Nogueira","Oliveira","Pereira","Reis","Santos","Saraiva","Silva","Souza","Xavier"]};var k={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var Q=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}} {{person.suffix}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:8}];var w={generic:["Dr.","Dra.","Sr.","Sra.","Srta."],female:["Dra.","Sra.","Srta."],male:["Dr.","Sr."]};var U=["Feminino","Masculino"];var Y=["Jr.","Neto","Filho"];var K=["Aqu\xE1rio","Peixes","\xC1ries","Touro","G\xEAmeos","C\xE2ncer","Le\xE3o","Virgem","Libra","Escorpi\xE3o","Sagit\xE1rio","Capric\xF3rnio"];var ca={first_name:_,job_area:O,job_descriptor:V,job_type:H,last_name:j,last_name_pattern:k,name:Q,prefix:w,sex:U,suffix:Y,western_zodiac_sign:K},W=ca;var Z=["(##) ####-####","+55 (##) ####-####","(##) #####-####"];var X=["+55##########","+55###########"];var $=["(##) ####-####","(##) #####-####"];var pa={human:Z,international:X,national:$},aa=pa;var fa={format:aa},oa=fa;var ba={book:u,color:d,commerce:f,company:M,date:A,internet:x,location:N,lorem:J,metadata:T,person:W,phone_number:oa},ea= exports.a =ba;var Ae=new (0, _chunkZKNYQOPPcjs.n)({locale:[ea,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = ea; exports.b = Ae;
