
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/common/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/common/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">15.44% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>21/136</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">3.7% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>2/54</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">1.88% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/53</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.63% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>18/132</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="correlation.service.ts"><a href="correlation.service.ts.html">correlation.service.ts</a></td>
	<td data-value="9.83" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.83" class="pct low">9.83%</td>
	<td data-value="61" class="abs low">6/61</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="60" class="abs low">5/60</td>
	</tr>

<tr>
	<td class="file low" data-value="enhanced-logger.service.ts"><a href="enhanced-logger.service.ts.html">enhanced-logger.service.ts</a></td>
	<td data-value="20" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 20%"></div><div class="cover-empty" style="width: 80%"></div></div>
	</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="75" class="abs low">15/75</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="30" class="abs low">2/30</td>
	<td data-value="3.03" class="pct low">3.03%</td>
	<td data-value="33" class="abs low">1/33</td>
	<td data-value="18.05" class="pct low">18.05%</td>
	<td data-value="72" class="abs low">13/72</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T10:46:30.857Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    