import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { testDatabaseConfig } from './setup';

// Global test database connection
let testDataSource: DataSource;
let testModule: TestingModule;

// Setup before all integration tests
beforeAll(async () => {
  // Create test database connection
  testDataSource = new DataSource({
    ...testDatabaseConfig,
    entities: ['src/**/*.entity.ts'],
    synchronize: true,
    dropSchema: true,
  });
  
  await testDataSource.initialize();
  
  // Create test module with database
  testModule = await Test.createTestingModule({
    imports: [
      TypeOrmModule.forRoot({
        ...testDatabaseConfig,
        autoLoadEntities: true,
      }),
    ],
  }).compile();
});

// Cleanup after all integration tests
afterAll(async () => {
  if (testDataSource && testDataSource.isInitialized) {
    await testDataSource.destroy();
  }
  
  if (testModule) {
    await testModule.close();
  }
});

// Clean database before each test
beforeEach(async () => {
  if (testDataSource && testDataSource.isInitialized) {
    // Get all table names
    const entities = testDataSource.entityMetadatas;
    
    // Disable foreign key checks
    await testDataSource.query('SET FOREIGN_KEY_CHECKS = 0');
    
    // Truncate all tables
    for (const entity of entities) {
      const repository = testDataSource.getRepository(entity.name);
      await repository.clear();
    }
    
    // Re-enable foreign key checks
    await testDataSource.query('SET FOREIGN_KEY_CHECKS = 1');
  }
});

// Integration test utilities
export class IntegrationTestUtils {
  static getDataSource(): DataSource {
    return testDataSource;
  }
  
  static getTestModule(): TestingModule {
    return testModule;
  }
  
  static async createTestTenant(data: Partial<any> = {}) {
    const tenantRepository = testDataSource.getRepository('Tenant');
    const tenant = tenantRepository.create({
      id: 'test-tenant-' + Date.now(),
      name: 'Test Company',
      domain: 'test.com',
      isActive: true,
      settings: {},
      ...data,
    });
    
    return await tenantRepository.save(tenant);
  }
  
  static async createTestUser(tenantId: string, data: Partial<any> = {}) {
    const userRepository = testDataSource.getRepository('User');
    const user = userRepository.create({
      id: 'test-user-' + Date.now(),
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      isActive: true,
      tenantId,
      ...data,
    });
    
    return await userRepository.save(user);
  }
  
  static async createTestRole(tenantId: string, data: Partial<any> = {}) {
    const roleRepository = testDataSource.getRepository('Role');
    const role = roleRepository.create({
      id: 'test-role-' + Date.now(),
      name: 'Test Role',
      description: 'Test role description',
      tenantId,
      permissions: [],
      ...data,
    });
    
    return await roleRepository.save(role);
  }
  
  static async cleanupTestData() {
    const entities = testDataSource.entityMetadatas;
    
    for (const entity of entities) {
      const repository = testDataSource.getRepository(entity.name);
      await repository.clear();
    }
  }
  
  static async executeRawQuery(query: string, parameters?: any[]) {
    return await testDataSource.query(query, parameters);
  }
  
  static async seedTestData() {
    // Create test tenant
    const tenant = await this.createTestTenant({
      id: 'seed-tenant',
      name: 'Seed Company',
      domain: 'seed.com',
    });
    
    // Create test roles
    const adminRole = await this.createTestRole(tenant.id, {
      id: 'admin-role',
      name: 'Admin',
      permissions: ['*'],
    });
    
    const userRole = await this.createTestRole(tenant.id, {
      id: 'user-role',
      name: 'User',
      permissions: ['read:profile', 'update:profile'],
    });
    
    // Create test users
    const adminUser = await this.createTestUser(tenant.id, {
      id: 'admin-user',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      roles: [adminRole],
    });
    
    const regularUser = await this.createTestUser(tenant.id, {
      id: 'regular-user',
      email: '<EMAIL>',
      firstName: 'Regular',
      lastName: 'User',
      roles: [userRole],
    });
    
    return {
      tenant,
      roles: { admin: adminRole, user: userRole },
      users: { admin: adminUser, regular: regularUser },
    };
  }
}

// Database transaction utilities for testing
export class TransactionTestUtils {
  static async runInTransaction<T>(
    callback: (queryRunner: any) => Promise<T>
  ): Promise<T> {
    const queryRunner = testDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      const result = await callback(queryRunner);
      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  
  static async rollbackTransaction(callback: () => Promise<void>): Promise<void> {
    const queryRunner = testDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      await callback();
    } finally {
      await queryRunner.rollbackTransaction();
      await queryRunner.release();
    }
  }
}

// Mock external services for integration tests
export class MockExternalServices {
  static mockRedisService() {
    return {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      flushall: jest.fn(),
    };
  }
  
  static mockEmailService() {
    return {
      sendEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
      sendWelcomeEmail: jest.fn().mockResolvedValue(true),
      sendPasswordResetEmail: jest.fn().mockResolvedValue(true),
      sendVerificationEmail: jest.fn().mockResolvedValue(true),
    };
  }
  
  static mockFileStorageService() {
    return {
      uploadFile: jest.fn().mockResolvedValue({ url: 'https://test.com/file.jpg' }),
      deleteFile: jest.fn().mockResolvedValue(true),
      getFileUrl: jest.fn().mockReturnValue('https://test.com/file.jpg'),
    };
  }
  
  static mockAuditService() {
    return {
      logEvent: jest.fn().mockResolvedValue(true),
      logUserAction: jest.fn().mockResolvedValue(true),
      logSystemEvent: jest.fn().mockResolvedValue(true),
    };
  }
}

// HTTP test utilities
export class HttpTestUtils {
  static createMockRequest(overrides: Partial<any> = {}) {
    return {
      method: 'GET',
      url: '/test',
      headers: {
        'content-type': 'application/json',
        'user-agent': 'test-agent',
      },
      body: {},
      query: {},
      params: {},
      user: null,
      tenant: null,
      correlationId: 'test-correlation-id',
      ...overrides,
    };
  }
  
  static createMockResponse() {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      header: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
    };
    
    return res;
  }
  
  static createMockNext() {
    return jest.fn();
  }
}

// Performance testing utilities
export class PerformanceTestUtils {
  static async measureExecutionTime<T>(
    operation: () => Promise<T>
  ): Promise<{ result: T; executionTime: number }> {
    const startTime = Date.now();
    const result = await operation();
    const executionTime = Date.now() - startTime;
    
    return { result, executionTime };
  }
  
  static async runConcurrentOperations<T>(
    operation: () => Promise<T>,
    concurrency: number
  ): Promise<T[]> {
    const promises = Array(concurrency).fill(null).map(() => operation());
    return await Promise.all(promises);
  }
  
  static async runLoadTest<T>(
    operation: () => Promise<T>,
    options: {
      duration: number; // in milliseconds
      maxConcurrency: number;
      rampUpTime?: number;
    }
  ): Promise<{ results: T[]; totalOperations: number; averageTime: number }> {
    const { duration, maxConcurrency, rampUpTime = 0 } = options;
    const results: T[] = [];
    const startTime = Date.now();
    const endTime = startTime + duration;
    let currentConcurrency = 1;
    
    const rampUpInterval = rampUpTime > 0 ? rampUpTime / maxConcurrency : 0;
    
    while (Date.now() < endTime) {
      const operationPromises: Promise<T>[] = [];
      
      for (let i = 0; i < currentConcurrency; i++) {
        operationPromises.push(operation());
      }
      
      const batchResults = await Promise.all(operationPromises);
      results.push(...batchResults);
      
      // Ramp up concurrency
      if (currentConcurrency < maxConcurrency && rampUpInterval > 0) {
        await new Promise(resolve => setTimeout(resolve, rampUpInterval));
        currentConcurrency++;
      }
    }
    
    const totalTime = Date.now() - startTime;
    const averageTime = totalTime / results.length;
    
    return {
      results,
      totalOperations: results.length,
      averageTime,
    };
  }
}
