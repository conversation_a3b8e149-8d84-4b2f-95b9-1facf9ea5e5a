import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from '../auth.service';
import { UserService } from '../../user/user.service';
import { TenantService } from '../../tenant/tenant.service';
import { IntegrationTestUtils, TransactionTestUtils } from '../../../../test/integration-setup';
import { testDatabaseConfig } from '../../../../test/setup';
import { User } from '../../user/entities/user.entity';
import { Tenant } from '../../tenant/entities/tenant.entity';
import { Role } from '../../rbac/entities/role.entity';
import { Permission } from '../../rbac/entities/permission.entity';
import { ErrorCode } from '../../../common/utils/error-handler.util';
import * as bcrypt from 'bcrypt';

describe('AuthService (Integration)', () => {
  let authService: AuthService;
  let userService: UserService;
  let tenantService: TenantService;
  let module: TestingModule;
  let testTenant: Tenant;
  let testUser: User;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          ...testDatabaseConfig,
          entities: [User, Tenant, Role, Permission],
          autoLoadEntities: true,
        }),
        TypeOrmModule.forFeature([User, Tenant, Role, Permission]),
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      providers: [
        AuthService,
        UserService,
        TenantService,
        // Mock other dependencies
        {
          provide: 'REDIS_CLIENT',
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
          },
        },
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
    tenantService = module.get<TenantService>(TenantService);
  });

  afterAll(async () => {
    await module.close();
  });

  beforeEach(async () => {
    // Create test tenant
    testTenant = await IntegrationTestUtils.createTestTenant({
      name: 'Integration Test Company',
      domain: 'integration-test.com',
    });

    // Create test user
    const hashedPassword = await bcrypt.hash('TestPassword123!', 10);
    testUser = await IntegrationTestUtils.createTestUser(testTenant.id, {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Integration',
      lastName: 'Test',
    });
  });

  afterEach(async () => {
    await IntegrationTestUtils.cleanupTestData();
  });

  describe('register', () => {
    it('should successfully register a new user', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'NewPassword123!',
        firstName: 'New',
        lastName: 'User',
        tenantId: testTenant.id,
      };

      const result = await authService.register(registerDto);

      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      expect(result.user.email).toBe(registerDto.email);
      expect(result.user.firstName).toBe(registerDto.firstName);
      expect(result.user.lastName).toBe(registerDto.lastName);
      expect(result.user.tenantId).toBe(registerDto.tenantId);
      expect(result.user.password).toBeUndefined(); // Password should not be returned
    });

    it('should throw error for duplicate email', async () => {
      const registerDto = {
        email: testUser.email,
        password: 'TestPassword123!',
        firstName: 'Duplicate',
        lastName: 'User',
        tenantId: testTenant.id,
      };

      await expect(authService.register(registerDto)).rejects.toThrow();
    });

    it('should throw error for invalid tenant', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'User',
        tenantId: 'invalid-tenant-id',
      };

      await expect(authService.register(registerDto)).rejects.toThrow();
    });

    it('should hash password correctly', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'PlainTextPassword123!',
        firstName: 'Password',
        lastName: 'Test',
        tenantId: testTenant.id,
      };

      await authService.register(registerDto);

      // Verify password is hashed in database
      const savedUser = await userService.findByEmail(registerDto.email);
      expect(savedUser.password).not.toBe(registerDto.password);
      expect(await bcrypt.compare(registerDto.password, savedUser.password)).toBe(true);
    });
  });

  describe('login', () => {
    it('should successfully login with valid credentials', async () => {
      const loginDto = {
        email: testUser.email,
        password: 'TestPassword123!',
      };

      const result = await authService.login(loginDto);

      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      expect(result.user.id).toBe(testUser.id);
      expect(result.user.email).toBe(testUser.email);
      expect(result.accessToken).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
    });

    it('should throw error for invalid email', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
      };

      await expect(authService.login(loginDto)).rejects.toThrow();
    });

    it('should throw error for invalid password', async () => {
      const loginDto = {
        email: testUser.email,
        password: 'WrongPassword123!',
      };

      await expect(authService.login(loginDto)).rejects.toThrow();
    });

    it('should throw error for inactive user', async () => {
      // Deactivate user
      await userService.update(testUser.id, { isActive: false });

      const loginDto = {
        email: testUser.email,
        password: 'TestPassword123!',
      };

      await expect(authService.login(loginDto)).rejects.toThrow();
    });

    it('should throw error for inactive tenant', async () => {
      // Deactivate tenant
      await tenantService.update(testTenant.id, { isActive: false });

      const loginDto = {
        email: testUser.email,
        password: 'TestPassword123!',
      };

      await expect(authService.login(loginDto)).rejects.toThrow();
    });
  });

  describe('refreshToken', () => {
    let validRefreshToken: string;

    beforeEach(async () => {
      const loginResult = await authService.login({
        email: testUser.email,
        password: 'TestPassword123!',
      });
      validRefreshToken = loginResult.refreshToken;
    });

    it('should successfully refresh tokens with valid refresh token', async () => {
      const result = await authService.refreshToken(validRefreshToken);

      expect(result).toHaveProperty('accessToken');
      expect(result).toHaveProperty('refreshToken');
      expect(result.accessToken).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
      expect(result.refreshToken).not.toBe(validRefreshToken); // Should be a new token
    });

    it('should throw error for invalid refresh token', async () => {
      const invalidToken = 'invalid.refresh.token';

      await expect(authService.refreshToken(invalidToken)).rejects.toThrow();
    });

    it('should throw error for expired refresh token', async () => {
      // This would require mocking JWT expiration or using a very short expiry
      // For now, we'll test with a malformed token
      const expiredToken = 'expired.token.here';

      await expect(authService.refreshToken(expiredToken)).rejects.toThrow();
    });
  });

  describe('logout', () => {
    let validAccessToken: string;
    let validRefreshToken: string;

    beforeEach(async () => {
      const loginResult = await authService.login({
        email: testUser.email,
        password: 'TestPassword123!',
      });
      validAccessToken = loginResult.accessToken;
      validRefreshToken = loginResult.refreshToken;
    });

    it('should successfully logout user', async () => {
      await expect(authService.logout(testUser.id, validRefreshToken)).resolves.not.toThrow();
    });

    it('should invalidate refresh token after logout', async () => {
      await authService.logout(testUser.id, validRefreshToken);

      // Attempting to use the refresh token should fail
      await expect(authService.refreshToken(validRefreshToken)).rejects.toThrow();
    });
  });

  describe('validateUser', () => {
    it('should return user for valid JWT payload', async () => {
      const payload = {
        sub: testUser.id,
        email: testUser.email,
        tenantId: testTenant.id,
      };

      const result = await authService.validateUser(payload);

      expect(result).toBeDefined();
      expect(result.id).toBe(testUser.id);
      expect(result.email).toBe(testUser.email);
      expect(result.tenantId).toBe(testTenant.id);
    });

    it('should return null for invalid user ID', async () => {
      const payload = {
        sub: 'invalid-user-id',
        email: '<EMAIL>',
        tenantId: testTenant.id,
      };

      const result = await authService.validateUser(payload);

      expect(result).toBeNull();
    });

    it('should return null for inactive user', async () => {
      // Deactivate user
      await userService.update(testUser.id, { isActive: false });

      const payload = {
        sub: testUser.id,
        email: testUser.email,
        tenantId: testTenant.id,
      };

      const result = await authService.validateUser(payload);

      expect(result).toBeNull();
    });
  });

  describe('changePassword', () => {
    it('should successfully change password', async () => {
      const changePasswordDto = {
        currentPassword: 'TestPassword123!',
        newPassword: 'NewPassword123!',
      };

      await expect(
        authService.changePassword(testUser.id, changePasswordDto)
      ).resolves.not.toThrow();

      // Verify new password works
      const loginResult = await authService.login({
        email: testUser.email,
        password: 'NewPassword123!',
      });

      expect(loginResult).toHaveProperty('accessToken');
    });

    it('should throw error for incorrect current password', async () => {
      const changePasswordDto = {
        currentPassword: 'WrongPassword123!',
        newPassword: 'NewPassword123!',
      };

      await expect(
        authService.changePassword(testUser.id, changePasswordDto)
      ).rejects.toThrow();
    });
  });

  describe('transaction handling', () => {
    it('should rollback registration on error', async () => {
      await TransactionTestUtils.rollbackTransaction(async () => {
        const registerDto = {
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'Rollback',
          lastName: 'Test',
          tenantId: testTenant.id,
        };

        await authService.register(registerDto);

        // Verify user was created within transaction
        const user = await userService.findByEmail(registerDto.email);
        expect(user).toBeDefined();
      });

      // Verify user was rolled back after transaction
      await expect(
        userService.findByEmail('<EMAIL>')
      ).rejects.toThrow();
    });
  });

  describe('concurrent operations', () => {
    it('should handle concurrent login attempts', async () => {
      const loginDto = {
        email: testUser.email,
        password: 'TestPassword123!',
      };

      const concurrentLogins = Array(5).fill(null).map(() => 
        authService.login(loginDto)
      );

      const results = await Promise.all(concurrentLogins);

      results.forEach(result => {
        expect(result).toHaveProperty('accessToken');
        expect(result).toHaveProperty('refreshToken');
      });

      // All tokens should be different
      const tokens = results.map(r => r.accessToken);
      const uniqueTokens = new Set(tokens);
      expect(uniqueTokens.size).toBe(tokens.length);
    });
  });
});
