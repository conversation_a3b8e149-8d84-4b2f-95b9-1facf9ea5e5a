{"name": "jest-html-reporters", "license": "MIT", "version": "3.1.7", "description": "Jest test results processor for generating a summary in HTML", "main": "index.js", "scripts": {"build": "yarn tsc -p build && node scripts/build.js", "buildCi": "yarn tsc -p build && node scripts/build.js", "start": "node scripts/start.js", "test": "yarn tsc -p build && node scripts/test.js", "testCi": "yarn tsc -p build && node scripts/test.js --reporters=default", "commit": "cz", "pr": "gh pr create"}, "keywords": ["jest", "html", "reporter", "report", "plugin"], "author": "Hazyzh", "repository": {"type": "git", "url": "git+https://github.com/Hazyzh/jest-html-reporters.git"}, "bugs": {"url": "https://github.com/Hazyzh/jest-html-reporters/issues"}, "homepage": "https://github.com/Hazyzh/jest-html-reporters#readme", "devDependencies": {"@babel/core": "^7.16.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/escape-html": "^1.0.2", "@types/jest": "^27.0.1", "@types/jump.js": "^1.0.4", "@types/node": "^18.11.18", "@types/randomcolor": "^0.5.7", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "ansi-to-html": "^0.7.2", "antd": "^5.1.6", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "copy-to-clipboard": "^3.3.1", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "cz-conventional-changelog": "^3.3.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "escape-html": "^1.0.3", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "jump.js": "^1.0.2", "mini-css-extract-plugin": "^2.4.5", "normalize.css": "^8.0.1", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "randomcolor": "^0.6.2", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-refresh": "^0.11.0", "recharts": "^2.3.2", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass": "^1.57.1", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "typescript": "^4.4.2", "url-loader": "^4.1.1", "web-vitals": "^2.1.0", "webpack": "^5.64.4", "webpack-bundle-analyzer": "^3.9.0", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "dependencies": {"fs-extra": "^10.0.0", "open": "^8.0.3"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "babel": {"presets": ["react-app"]}, "files": ["index.js", "helper.js", "readme.md", "dist/index.html", "dist/singleFile.html", "dist/index.js", "helper.d.ts"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}