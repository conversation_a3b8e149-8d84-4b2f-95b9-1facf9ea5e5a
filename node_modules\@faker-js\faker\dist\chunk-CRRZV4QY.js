import{a as e}from"./chunk-WFBH3POG.js";import{a as r}from"./chunk-KERBADJJ.js";import{n as a,o as i}from"./chunk-PC2QB7VM.js";var n=["0600#####","0601#####","0602#####","0603#####","0604#####","0605#####","0606#####","0607#####","0608#####","0609#####","0610#####","0611#####","0612#####","0613#####","0614#####","0615#####","0616#####","0617#####","0618#####","0619#####","0620#####","0621#####","0622#####","0623#####","0624#####","0625#####","0626#####","0627#####","0628#####","0629#####","0660#####","0661#####","0662#####","0663#####","0664#####","0665#####","0666#####","0667#####","0668#####","0669#####","0670#####","0671#####","0672#####","0673#####","0674#####","0675#####","0676#####","0677#####","0678#####","0679#####","0680#####","0681#####","0682#####","0683#####","0684#####","0685#####","0686#####","0687#####","0688#####","0689#####","0690#####","0691#####","0692#####","0693#####","0694#####","0695#####","0696#####","0697#####","0698#####","0699#####","0790#####","0791#####","0792#####","0793#####","0794#####","0795#####","0796#####","0797#####","0798#####","0799#####","0780#####","0781#####","0782#####","0783#####","0784#####","0785#####","0786#####","0787#####","0788#####","0789#####"];var _={formats:n},o=_;var l={wide:["Aprilie","August","Decembrie","Februarie","Ianuarie","Iulie","Iunie","Mai","Martie","Noiembrie","Octombrie","Septembrie"],abbr:["Apr","Aug","Dec","Feb","Ian","Iul","Iun","Mai","Mar","Noi","Oct","Sep"]};var t={wide:["Duminic\u0103","Joi","Luni","Mar\u021Bi","Miercuri","S\xE2mb\u0103t\u0103","Vineri"],abbr:["Du","Jo","Lu","Ma","Mi","S\xE2","Vi"]};var y={month:l,weekday:t},u=y;var c=["com","md","net","org","ru"];var s=["gmail.com","gmail.ru","hotmail.com","mail.ru","rambler.ru","yahoo.com","yandex.ru"];var j={domain_suffix:c,free_email:s},d=j;var m=["Bloc ##","Bloc ##/##"];var v=["Anenii Noi","Basarabeasca","Bender","Briceni","B\u0103l\u021Bi","Cahul","Cead\xEEr-Lunga","Chi\u0219in\u0103u","Cimi\u0219lia","Cocieri","Codru","Comrat","Criuleni","C\u0103l\u0103ra\u0219i","C\u0103u\u0219eni","Dnestrovsc","Dondu\u0219eni","Drochia","Dub\u0103sari","Dumbrava","Edine\u021B","Flore\u0219ti","F\u0103le\u0219ti","Glodeni","Grigoriopol","H\xEEnce\u0219ti","Ialoveni","Iargara","Leova","Nisporeni","Ocni\u021Ba","Orhei","Otaci","Rezina","R\xEEbni\u021Ba","Sadaclia","Sadaclia Mic\u0103","Sadaclia Nou\u0103","Saharna","Saharna Nou\u0103","Saharna Veche","Salcia","Seli\u0219te","Sipoteni","Sipoteni Noui","Slobozia Mare","Soroca","Str\u0103\u0219eni","St\u0103uceni","Susleni","Suvorovca","S\xEEngerei","Talmaza","Taraclia","Taraclia de Salcie","Telenesti","Tigheci","Tiraspol","Tocuz","Tocuzeni","Tomai","Trebis\u0103u\u021Bi","Tvardi\u021Ba","T\u0103t\u0103r\u0103\u0219eni","Ungheni","Vadul lui Isac","Vadul lui Voda","Vaduleni","Valea Coloni\u021Bei","Valea Perjei","Varni\u021Ba","Varvareuca","Vatra","Verde\u0219ti","Veveri\u021Ba","Volintiri","Vulc\u0103ne\u0219ti","Zagarancea","Zaicana","Zaim","Zamciogi","Zguri\u021Ba","Zg\u0103rde\u0219ti","Zirnesti","Zolonceni","Z\xE2rne\u0219ti","Z\xEErne\u0219ti","Z\u0103briceni","Z\u0103icani","Z\u0103luceni","Z\u0103ticeni","\u0218ofr\xEEncani","\u0218tefan Vod\u0103"];var p=["{{location.city_prefix}} {{person.firstName}}","{{location.city_name}}"];var C=["Raionul","Municipiu"];var g=["Anenii Noi","Basarabeasca","Bender","Briceni","Cahul","Calarasi","Cantemir","Causeni","Chisinau","Cimislia","Criuleni","Donduseni","Drochia","Dubasari","Edinet","Falesti","Floresti","Glodeni","Hincesti","Ialoveni","Leova","Nisporeni","Ocnita","Orhei","Rezina","Riscani","Singerei","Soldanesti","Soroca","Stefan-Voda","Straseni","Taraclia","Telenesti","Ungheni"];var A=["MD-####"];var f=["Ap. ##","Ap. ###"];var M=null;var S=null;var h={normal:"{{location.street}}, {{location.buildingNumber}}",full:"{{location.street}}, {{location.buildingNumber}}, {{location.secondaryAddress}}"};var V=["1 Mai","27 August","28 Iunie","31 August","8 Martie","9 Mai","Adam","Alba Iulia","Aldea-Teodorovici Doina Si Ion","Alecsandri","Alecu Russo","Alexandru Cel Bun","Alexandru Donici","Alexandru Lapusneanu","Alexandru Plamadeala","Alexei Mateevici","Arborilor","Arghezi","Aron Pumnul","Asachi","Aurel David","Balcescu","Banulescu-Bodoni","Barbu Lautaru","Basarabia","Basarabiei","Bernardazzi","Bisericii","Bogdan Voda","Boris Glavan","Brincus","Bucovinei","Bucuriei","Bulgara","Burebista","Cahul","Cantemir","Cantemir Dimitrie","Capriana","Caraciobanu","Caragiale","Ceaikovski Piotr","Ceapaev","Cehov","Cetatea Alba","Chirov","Chisinau","Chisinaului","Cibotari Maria","Cimpului","Ciobanu","Ciocirlia","Ciorba","Ciprian Porumbescu","Ciresilor","Cismelelor","Codrilor","Colinei","Columna","Comarov","Comarova","Constantin Negruzzi","Constantin Stamati","Constantin Stere","Constructorilor","Corlatenilor","Cosbuc","Cosbuc George","Cosmescu","Cosmonautilor","Costin","Crasescu","Creanga","Crihan","Crizantemelor","Cupcea","Cutuzov","Cuza Voda","Dacia","Dacilor","Damian","Decebal","Delete","Dimo","Doga","Doina","Donici","Dosoftei","Dragan","Dragomirna","Dragos Voda","Dumbrava","Eminescu","Feroviarilor","Fintinilor","Floreni","Florilor","Franco","Frunze","Gagarin","Garii","Gheorghe Asachi","Ghica-Voda","Ghioceilor","Glavan","Gogol","Gradinarilor","Gradinilor","Gratiesti","Gribov","Grigore Ureche","Haiducilor","Halippa","Halippa Pantelimon","Hasdeu","Hasdeu Bogdan Petriceicu","Hijdeu","Hincesti","Horelor","Hotin","Hotinului","Iachir","Ialoveni","Iasului","Iazului","Igor Vieru","Independentei","Industriala","Ioan Voda","Ion Creanga","Ion Neculce","Ion Soltis","Iorga","Ismail","Isnovat","Izvoarelor","Kiev","Kogalniceanu","Lacului","Lapusneanu","Lautarilor","Lazo","Lenin","Lermontov","Libertatii","Livezilor","Liviu Deleanu","Lomonosov","Luceafarul","Luceafarului","Lupu","Macarenco","Maiacovschi Vladimir","Maria Cibotaru","Maria Dragan","Marinescu","Martisor","Mateevici","Matei Basarab","Matrosov","Mendeleev","Meniuc","Mesterul Manole","Miciurin","Micle","Mihai Eminescu","Mihai Sadoveanu","Mihail Kogalniceanu","Mihail Sadoveanu","Milescu Spataru Nicolae","Miorita","Mioritei","Mira","Mircea Cel Batrin","Miron Costin","Mitropolit Petru Movila","Mitropolitul Varlaam","Moldova","Moldovita","Molodiojnaia","Moruzi","Movila","Muncii","Muresanu","Musicescu Gavriil","Neaga","Necrasov","Neculce","Negruzzi","Nicolae Iorga","Novaia","Nucarilor","Nuferilor","Orhei","Ostrovschi","Ovidiu","Pacii","Padurilor","Parcului","Pavlov","Pescarilor","Petru Movila","Petru Rares","Petru Zadnipru","Pirogov","Plaiului","Plamadeala","Plopilor","Plugarilor","Pobedi","Podgorenilor","Polevaia","Popov","Porumbescu","Prieteniei","Primaverii","Pruncul","Puskin","Rares","Razesilor","Rediu Mare","Renasterii","Romana","Russo","Russo Alecu","Sadovaia","Sadoveanu","Saharov","Salcimilor","Satul","Sciusev","Scolii","Serghei Lazo","Sevcenco","Sfatul Tarii","Sfinta Maria","Sfinta Treime","Sfintul Andrei","Sfintul Gheorghe","Sirbu","Smochina","Solidaritatii","Solnecinaia","Solohov","Soltis","Soltis Ion","Sperantei","Sportiva","Stamati","Stefan Cel Mare","Stefan Neaga","Stefan Voda","Stejarilor","Stere","Studentilor","Suceava","Suveranitatii","Suvorov","Tamara Ciobanu","Tatarbunar","Tcacenco","Teilor","Teodorovici","Testemiteanu","Tighina","Tighinei","Timisoara","Tineretului","Tolstoi","Toma Ciorba","Traian","Trandafirilor","Tricolorului","Tudor Vladimirescu","Turghenev","Ungureanu","Unirii","Ureche","Uzinelor","Valeriu Cupcea","Varlaam","Vasile Alecsandri","Vasile Lupu","Veronica Micle","Victoriei","Vieru","Viilor","Visinilor","Viteazul Mihai","Vlad Tepes","Vladimirescu","Voda","Voluntarilor","Vorosilov","Zadnipru","Zamfir Arbore","Zmeurei","Zorilor"];var D=["{{location.street_prefix}} {{location.street_name_part}}"];var I=["Aleea","Bulevardul","Str-la","Str"];var H={building_number:m,city_name:v,city_pattern:p,city_prefix:C,county:g,postcode:A,secondary_address:f,state:M,state_abbr:S,street_address:h,street_name_part:V,street_pattern:D,street_prefix:I},b=H;var U={title:"Romanian (Moldova)",code:"ro_MD",country:"MD",language:"ro",endonym:"Rom\xE2n\u0103 (Moldova)",dir:"ltr",script:"Latn"},L=U;var P={generic:["Aculina","Ada","Adela","Adelaida","Adelina","Adrian","Adriana","Afanasi","Afanasie","Agafia","Agnesa","Ahmad","Ala","Albert","Albina","Alea","Alena","Alesea","Alevtina","Alexander","Alexandr","Alexandra","Alexandrina","Alexandru","Alexei","Ali","Alic","Alina","Aliona","Alisa","Alla","Amir","Ana","Ana-Maria","Anastasia","Anatol","Anatoli","Anatolie","Anatolii","Andrei","Andrian","Andriana","Angela","Angelica","Anghelina","Anjela","Anjelica","Anna","Anton","Antonina","Arcadi","Arcadie","Arcadii","Argentina","Ariadna","Arina","Artiom","Artur","Augustin","Aurel","Aurelia","Aurelian","Aureliu","Aurica","Axenia","Boris","Calin","Calina","Carina","Carolina","Catalin","Catalina","Cezar","Cezara","Chira","Chiril","Chirill","Ciprian","Claudia","Clavdia","Constanta","Constantia","Constantin","Constatin","Corina","Cornel","Cornelia","Corneliu","Crina","Cristian","Cristiana","Cristin","Cristina","Cristofor","Dana","Daniel","Daniela","Daniil","Danil","Danu","Daria","David","Denis","Diana","Didina","Dimitri","Dimitrii","Dina","Dinu","Dmitri","Dmitrii","Doina","Doinita","Domnica","Dora","Dorel","Dorian","Doriana","Dorin","Dorina","Dragomir","Dragos","Dumitrita","Dumitru","Ecaterina","Edgar","Eduard","Efim","Efimia","Efrosinia","Egor","Elena","Eleonora","Elina","Elisaveta","Elizaveta","Ella","Elmira","Elvira","Ema","Emil","Emilia","Emilian","Eric","Erica","Eudochia","Eugen","Eugenia","Eugeniu","Eva","Evdochia","Evelina","Evgheni","Evghenia","Evghenii","Fedora","Felicia","Feodor","Feodora","Feodosia","Fevronia","Filip","Filipp","Fiodor","Florin","Gabriel","Gabriela","Galina","Gavril","George","Georgeta","Ghenadi","Ghenadie","Ghennadi","Gheorghe","Gheorghi","Gheorghii","Gheorghina","Gherman","Gleb","Greta","Grigore","Grigori","Husein","Iacob","Iacov","Iana","Ianina","Ianna","Ianos","Iaroslav","Iaroslava","Ibrahim","Igor","Igori","Ileana","Ilia","Ilie","Ilinca","Ilona","Ina","Inesa","Inessa","Inga","Inna","Ioan","Ioana","Ion","Ionel","Ionela","Iosif","Ira","Iraida","Irena","Irina","Iryna","Iulia","Iulian","Iuliana","Iuri","Iurie","Iurii","Ivan","Ivana","Ivanna","Jan","Jana","Janeta","Janna","Lada","Larisa","Laura","Laurentia","Laurentiu","Leon","Leonid","Leonora","Lev","Lia","Lidia","Lilia","Liliana","Lina","Liuba","Liubov","Liubovi","Liudmila","Liusea","Livia","Liviu","Lora","Luca","Lucia","Lucian","Lucica","Lucretia","Ludmila","Luiza","Luminita","Lungu","Magdalena","Mahmoud","Maia","Malvina","Marat","Marc","Marcel","Marcela","Margareta","Margarita","Maria","Marian","Mariana","Marianna","Maricica","Marin","Marina","Marius","Marta","Maxim","Melania","Melnic","Mihaela","Mihai","Mihail","Minodora","Mircea","Mirela","Miroslav","Miroslava","Mohamad","Mohamed","Muhammad","Mustafa","Nadejda","Natalia","Natasa","Nelea","Neli","Nellea","Nelli","Neonila","Nicanor","Nicolae","Nicolai","Nicolaie","Nicoleta","Nicon","Nicu","Niculina","Nina","Nineli","Nona","Nonna","Octavian","Oleg","Oleksandr","Olena","Olesea","Olga","Oliga","Olimpiada","Otilia","Oxana","Pantelei","Parascovia","Paulina","Pavel","Pavlina","Pelaghia","Petru","Piotr","Polina","Prascovia","Rada","Radion","Radislav","Radu","Raisa","Reghina","Renat","Renata","Rima","Rimma","Rita","Robert","Rodica","Rodion","Roman","Romeo","Romina","Rosina","Rostislav","Rotaru","Ruslan","Ruslana","Rustam","Ruxanda","Sabina","Said","Sanda","Sandu","Sava","Savva","Seghei","Semion","Serafim","Serafima","Sergei","Sergey","Serghei","Sergiu","Silvia","Silviu","Simion","Snejana","Sofia","Sorina","Spiridon","Stanislav","Stefan","Stela","Stelian","Steliana","Stella","Steluta","Stepan","Stepanida","Sveatoslav","Svetlana","Svetoslav","Svitlana","Taisia","Tamara","Taras","Tatiana","Teodor","Teodora","Tetiana","Timofei","Toma","Traian","Trofim","Tudor","Uliana","Vadim","Valentin","Valentina","Valeri","Valeria","Valerii","Valeriu","Varvara","Vasile","Vasili","Vasilie","Vasilina","Vasilisa","Veaceslav","Veceaslav","Veceslav","Veniamin","Vera","Vergiliu","Verginia","Veronica","Vica","Victor","Victoria","Violeta","Violetta","Violina","Viorel","Viorelia","Viorica","Virgiliu","Virginia","Vitali","Vitalia","Vitalie","Vitalii","Vitalina","Vlad","Vlada","Vladilena","Vladimer","Vladimir","Vladislav","Vladislava","Vladlen","Vladlena","Vsevolod","Xenia","Zahar","Zaharia","Zina","Zinaida","Zinovia","Zoia","Zorina"],female:["Aculina","Ada","Adela","Adelaida","Adelina","Adriana","Afanasi","Agafia","Agnesa","Ala","Albina","Alea","Alena","Alesea","Alevtina","Alexandra","Alexandrina","Alina","Aliona","Alisa","Alla","Ana","Ana-Maria","Anastasia","Andriana","Angela","Angelica","Anghelina","Anjela","Anjelica","Anna","Antonina","Argentina","Ariadna","Arina","Aurelia","Aurica","Axenia","Calina","Carina","Carolina","Catalina","Cezara","Chira","Claudia","Clavdia","Constanta","Constantia","Corina","Cornelia","Crina","Cristiana","Cristina","Dana","Daniela","Daria","Diana","Didina","Dina","Doina","Doinita","Domnica","Dora","Doriana","Dorina","Dumitrita","Ecaterina","Efimia","Efrosinia","Elena","Eleonora","Elina","Elisaveta","Elizaveta","Ella","Elmira","Elvira","Ema","Emilia","Erica","Eudochia","Eugenia","Eva","Evdochia","Evelina","Evghenia","Fedora","Felicia","Feodora","Feodosia","Fevronia","Gabriela","Galina","Georgeta","Gheorghina","Greta","Iana","Ianina","Ianna","Iaroslava","Ileana","Ilia","Ilinca","Ilona","Ina","Inesa","Inessa","Inga","Inna","Ioana","Ionela","Ira","Iraida","Irena","Irina","Iryna","Iulia","Iuliana","Ivana","Ivanna","Jana","Janeta","Janna","Lada","Larisa","Laura","Laurentia","Leonora","Lia","Lidia","Lilia","Liliana","Lina","Liuba","Liubov","Liubovi","Liudmila","Liusea","Livia","Lora","Lucia","Lucica","Lucretia","Ludmila","Luiza","Luminita","Magdalena","Maia","Malvina","Marcela","Margareta","Margarita","Maria","Mariana","Marianna","Maricica","Marina","Marta","Melania","Melnic","Mihaela","Minodora","Mirela","Miroslava","Nadejda","Natalia","Natasa","Nelea","Neli","Nellea","Nelli","Neonila","Nicolai","Nicoleta","Niculina","Nina","Nineli","Nona","Nonna","Olena","Olesea","Olga","Oliga","Olimpiada","Otilia","Oxana","Pantelei","Parascovia","Paulina","Pavlina","Pelaghia","Polina","Prascovia","Rada","Raisa","Reghina","Renata","Rima","Rimma","Rita","Rodica","Romina","Rosina","Rotaru","Ruslana","Ruxanda","Sabina","Sanda","Serafima","Silvia","Snejana","Sofia","Sorina","Stela","Steliana","Stella","Steluta","Stepanida","Svetlana","Svitlana","Taisia","Tamara","Tatiana","Teodora","Tetiana","Uliana","Valentina","Valeria","Valeriu","Varvara","Vasilina","Vasilisa","Veaceslav","Vera","Verginia","Veronica","Vica","Victoria","Violeta","Violetta","Violina","Viorelia","Viorica","Virginia","Vitalia","Vitalina","Vlada","Vladilena","Vladislava","Vladlena","Xenia","Zina","Zinaida","Zinovia","Zoia","Zorina"],male:["Adrian","Afanasi","Afanasie","Ahmad","Albert","Alexander","Alexandr","Alexandru","Alexei","Ali","Alic","Amir","Anatol","Anatoli","Anatolie","Anatolii","Andrei","Andrian","Anjela","Anton","Arcadi","Arcadie","Arcadii","Artiom","Artur","Augustin","Aurel","Aurelian","Aureliu","Boris","Calin","Catalin","Cezar","Chiril","Chirill","Ciprian","Constantin","Constatin","Cornel","Corneliu","Cristian","Cristin","Cristofor","Daniel","Daniil","Danil","Danu","David","Denis","Dimitri","Dimitrii","Dinu","Dmitri","Dmitrii","Dorel","Dorian","Dorin","Dragomir","Dragos","Dumitru","Edgar","Eduard","Efim","Egor","Emil","Emilian","Eric","Eugen","Eugeniu","Evgheni","Evghenii","Feodor","Filip","Filipp","Fiodor","Florin","Gabriel","Gavril","George","Ghenadi","Ghenadie","Ghennadi","Gheorghe","Gheorghi","Gheorghii","Gherman","Gleb","Grigore","Grigori","Husein","Iacob","Iacov","Ianos","Iaroslav","Ibrahim","Igor","Igori","Ilia","Ilie","Ioan","Ion","Ionel","Iosif","Iulian","Iuri","Iurie","Iurii","Ivan","Jan","Laurentiu","Leon","Leonid","Lev","Liubovi","Liviu","Luca","Lucian","Lungu","Mahmoud","Marat","Marc","Marcel","Marian","Marin","Marius","Maxim","Mihai","Mihail","Mircea","Miroslav","Mohamad","Mohamed","Muhammad","Mustafa","Nicanor","Nicolae","Nicolai","Nicolaie","Nicon","Nicu","Octavian","Oleg","Oleksandr","Pantelei","Pavel","Petru","Piotr","Radion","Radislav","Radu","Renat","Robert","Rodion","Roman","Romeo","Rostislav","Ruslan","Rustam","Said","Sandu","Sava","Savva","Seghei","Semion","Serafim","Sergei","Sergey","Serghei","Sergiu","Silvia","Silviu","Simion","Spiridon","Stanislav","Stefan","Stela","Stelian","Stepan","Sveatoslav","Svetlana","Svetoslav","Taras","Teodor","Timofei","Toma","Traian","Trofim","Tudor","Vadim","Valentin","Valeri","Valerii","Valeriu","Vasile","Vasili","Vasilie","Veaceslav","Veceaslav","Veceslav","Veniamin","Vergiliu","Victor","Viorel","Virgiliu","Vitali","Vitalie","Vitalii","Vlad","Vladimer","Vladimir","Vladislav","Vladlen","Vsevolod","Zahar","Zaharia"]};var N={generic:["Ababii","Abu","Adam","Albu","Alexandru","Andrei","Andries","Andronic","Anghel","Antoci","Apostol","Arnaut","Babin","Baciu","Balaban","Balan","Baltag","Bargan","Bejan","Bejenari","Bejenaru","Birca","Bitca","Bivol","Boboc","Bodrug","Bogdan","Boico","Bondarenco","Bordian","Bors","Borta","Bostan","Botan","Botezatu","Botnari","Botnaru","Braga","Brinza","Buga","Bujor","Bulat","Bunescu","Burduja","Burlacu","Buruiana","Busuioc","Butnaru","Capatina","Cara","Caraman","Caraus","Carp","Casian","Catana","Cazac","Cazacu","Ceban","Cebanu","Cebotari","Cecan","Cernei","Chicu","Chihai","Chiriac","Chirilov","Chirita","Cibotari","Cioban","Ciobanu","Ciorba","Ciornii","Ciubotaru","Ciumac","Codreanu","Cojocari","Cojocaru","Cojuhari","Colesnic","Condrea","Constantinov","Costin","Cotorobai","Cotruta","Covalciuc","Covalenco","Covali","Craciun","Creciun","Cretu","Cristea","Croitor","Croitoru","Crudu","Cucos","Cucu","Cujba","Cusnir","Dabija","Damian","Darii","David","Diaconu","Dodon","Donica","Dragan","Duca","Enachi","Eni","Erhan","Esanu","Filip","Florea","Focsa","Frunza","Frunze","Furtuna","Gaina","Gangan","Gavrilita","Gavriliuc","Gheorghita","Gherman","Gilca","Girbu","Gisca","Golban","Goncear","Gonta","Gorea","Graur","Grecu","Grigoras","Grosu","Groza","Gusan","Gutu","Guzun","Hincu","Iatco","Ignat","Iovu","Isac","Istrati","Ivanov","Ivanova","Jardan","Josan","Lazari","Leahu","Lisnic","Luca","Luchian","Lungu","Lupasco","Lupascu","Lupu","Macari","Macovei","Madan","Malai","Mamaliga","Manole","Marcu","Mardari","Marian","Marin","Matei","Mazur","Melnic","Mereuta","Mihailov","Mihalachi","Mindru","Miron","Mirza","Mitu","Mocan","Mocanu","Moisei","Moldovan","Moldovanu","Morari","Moraru","Moroz","Muntean","Munteanu","Musteata","Nastas","Neagu","Negara","Negru","Negruta","Nicolaev","Nistor","Novac","Olari","Olaru","Oleinic","Oprea","Paladi","Palii","Pasat","Pascal","Pascari","Pascaru","Pavlov","Petrov","Pintea","Pinzari","Pinzaru","Placinta","Plamadeala","Platon","Plesca","Popa","Popescu","Popov","Popova","Popovici","Popusoi","Postica","Postolachi","Prepelita","Prisacari","Prisacaru","Prodan","Pruteanu","Puscas","Racu","Radu","Railean","Raileanu","Rata","Revenco","Robu","Roman","Romanciuc","Rosca","Rotari","Rotaru","Rusnac","Russu","Rusu","Sandu","Sava","Savciuc","Savin","Schiopu","Scripnic","Scurtu","Scutaru","Secrieru","Seremet","Serghei","Sevcenco","Sirbu","Sirghi","Sochirca","Socolov","Soltan","Spataru","Spinu","Stavila","Stirbu","Stoian","Stratan","Stratulat","Svet","Talpa","Taran","Tataru","Tatiana","Tcacenco","Tcaci","Terzi","Tofan","Toma","Topal","Triboi","Trifan","Turcan","Turcanu","Ungureanu","Untila","Ursachi","Ursu","Uzun","Vacarciuc","Vartic","Verdes","Vicol","Vieru","Virlan","Vizitiu","Vlas","Vrabie","Zaharia","Zaporojan"]};var R=[{value:"{{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1}];var G={generic:["Dl","Dna","Dra"],female:["Dna","Dra"],male:["Dl","Dra"]};var T=null;var J={first_name:P,last_name:N,name:R,prefix:G,suffix:T},x=J;var E=["0220#####","0221#####","0222#####","0223#####","0224#####","0225#####","0226#####","0227#####","0228#####","0229#####"];var B=["+373220#####","+373221#####","+373222#####","+373223#####","+373224#####","+373225#####","+373226#####","+373227#####","+373228#####","+373229#####"];var z=["022 0## ###","022 1## ###","022 2## ###","022 3## ###","022 4## ###","022 5## ###","022 6## ###","022 7## ###","022 8## ###","022 9## ###"];var k={human:E,international:B,national:z},F=k;var w={format:F},Z=w;var K={cell_phone:o,date:u,internet:d,location:b,metadata:L,person:x,phone_number:Z},O=K;var pi=new a({locale:[O,e,r,i]});export{O as a,pi as b};
