"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunk63UJVCQ4cjs = require('./chunk-63UJVCQ4.cjs');var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var i=["621 ### ###","661 ### ###","671 ### ###","691 ### ###","+352 621 ### ###","+352 661 ### ###","+352 671 ### ###","+352 691 ### ###"];var b={formats:i},n=b;var m=["lu"];var P={domain_suffix:m},a=P;var f=["Diekirch","Differdange","Dudelange","Echternach","Esch-sur-Alzette","Ettelbruck","<PERSON>revenmacher","Luxembourg","<PERSON><PERSON><PERSON>","Rumelange","Vianden","Wiltz"];var p=["{{location.city_name}}"];var l=["####"];var u=["Capellen","Clervaux","Diekirch","Echternach","Esch-sur-Alzette","Grevenmacher","Luxembourg","Mersch","Redange","Remich","Vianden","Wiltz"];var k={city_name:f,city_pattern:p,postcode:l,state:u},c=k;var E={title:"French (Luxembourg)",code:"fr_LU",country:"LU",language:"fr",endonym:"Fran\xE7ais (Luxembourg)",dir:"ltr",script:"Latn"},d=E;var s={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var F={last_name_pattern:s},x=F;var h=["######","########","+352 ######","+352 ########"];var D=["+352######","+352########"];var y=["## ## ##","## ## ## ##"];var v={human:h,international:D,national:y},_=v;var z={format:_},g=z;var C={cell_phone:n,internet:a,location:c,metadata:d,person:x,phone_number:g},L= exports.a =C;var ht=new (0, _chunkZKNYQOPPcjs.n)({locale:[L,_chunk63UJVCQ4cjs.a,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = L; exports.b = ht;
