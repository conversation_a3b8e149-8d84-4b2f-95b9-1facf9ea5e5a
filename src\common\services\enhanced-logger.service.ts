import { Injectable, Inject, LoggerService } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger as WinstonLogger } from 'winston';
import { CorrelationService } from './correlation.service';

/**
 * Log levels enum
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly',
}

/**
 * Log context interface
 */
export interface LogContext {
  correlationId?: string;
  tenantId?: string;
  userId?: string;
  requestId?: string;
  sessionId?: string;
  traceId?: string;
  spanId?: string;
  method?: string;
  url?: string;
  userAgent?: string;
  ip?: string;
  component?: string;
  action?: string;
  resource?: string;
  duration?: number;
  statusCode?: number;
  [key: string]: any;
}

/**
 * Enhanced logger service with correlation ID support and structured logging
 */
@Injectable()
export class EnhancedLoggerService implements LoggerService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER)
    private readonly winstonLogger: WinstonLogger,
    private readonly correlationService: CorrelationService,
  ) {}

  /**
   * Log error message
   */
  error(message: string, error?: Error | any, context?: LogContext): void {
    this.log(LogLevel.ERROR, message, { ...context, error: this.formatError(error) });
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log info message
   */
  log(message: string, context?: LogContext): void;
  log(level: LogLevel, message: string, context?: LogContext): void;
  log(levelOrMessage: LogLevel | string, messageOrContext?: string | LogContext, context?: LogContext): void {
    if (typeof levelOrMessage === 'string') {
      // log(message, context) signature
      this.writeLog(LogLevel.INFO, levelOrMessage, messageOrContext as LogContext);
    } else {
      // log(level, message, context) signature
      this.writeLog(levelOrMessage, messageOrContext as string, context);
    }
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log verbose message
   */
  verbose(message: string, context?: LogContext): void {
    this.log(LogLevel.VERBOSE, message, context);
  }

  /**
   * Log HTTP request/response
   */
  http(message: string, context?: LogContext): void {
    this.log(LogLevel.HTTP, message, context);
  }

  /**
   * Log business event
   */
  business(event: string, data?: any, context?: LogContext): void {
    this.log(LogLevel.INFO, `Business Event: ${event}`, {
      ...context,
      eventType: 'business',
      event,
      data,
    });
  }

  /**
   * Log security event
   */
  security(event: string, data?: any, context?: LogContext): void {
    this.log(LogLevel.WARN, `Security Event: ${event}`, {
      ...context,
      eventType: 'security',
      event,
      data,
    });
  }

  /**
   * Log audit event
   */
  audit(action: string, resource: string, data?: any, context?: LogContext): void {
    this.log(LogLevel.INFO, `Audit: ${action} ${resource}`, {
      ...context,
      eventType: 'audit',
      action,
      resource,
      data,
    });
  }

  /**
   * Log performance metrics
   */
  performance(operation: string, duration: number, context?: LogContext): void {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.DEBUG;
    this.log(level, `Performance: ${operation} took ${duration}ms`, {
      ...context,
      eventType: 'performance',
      operation,
      duration,
    });
  }

  /**
   * Log database operation
   */
  database(operation: string, table?: string, duration?: number, context?: LogContext): void {
    this.log(LogLevel.DEBUG, `Database: ${operation}${table ? ` on ${table}` : ''}`, {
      ...context,
      eventType: 'database',
      operation,
      table,
      duration,
    });
  }

  /**
   * Log external service call
   */
  external(service: string, operation: string, duration?: number, statusCode?: number, context?: LogContext): void {
    const level = statusCode && statusCode >= 400 ? LogLevel.WARN : LogLevel.DEBUG;
    this.log(level, `External: ${service} ${operation}`, {
      ...context,
      eventType: 'external',
      service,
      operation,
      duration,
      statusCode,
    });
  }

  /**
   * Log user action
   */
  userAction(action: string, userId?: string, data?: any, context?: LogContext): void {
    this.log(LogLevel.INFO, `User Action: ${action}`, {
      ...context,
      eventType: 'userAction',
      action,
      userId: userId || context?.userId,
      data,
    });
  }

  /**
   * Log system event
   */
  system(event: string, data?: any, context?: LogContext): void {
    this.log(LogLevel.INFO, `System: ${event}`, {
      ...context,
      eventType: 'system',
      event,
      data,
    });
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: LogContext): EnhancedLoggerService {
    const childLogger = new EnhancedLoggerService(this.winstonLogger, this.correlationService);
    (childLogger as any).defaultContext = additionalContext;
    return childLogger;
  }

  /**
   * Start timing an operation
   */
  startTimer(operation: string): () => void {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      this.performance(operation, duration);
    };
  }

  /**
   * Write log with correlation context
   */
  private writeLog(level: LogLevel, message: string, context?: LogContext): void {
    const correlationContext = this.correlationService.getContext();
    const defaultContext = (this as any).defaultContext || {};
    
    const logContext: LogContext = {
      ...defaultContext,
      ...correlationContext,
      ...context,
      timestamp: new Date().toISOString(),
      level,
    };

    // Remove undefined values
    Object.keys(logContext).forEach(key => {
      if (logContext[key] === undefined) {
        delete logContext[key];
      }
    });

    this.winstonLogger.log(level, message, logContext);
  }

  /**
   * Format error object for logging
   */
  private formatError(error?: Error | any): any {
    if (!error) return undefined;

    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...(error as any).code && { code: (error as any).code },
        ...(error as any).statusCode && { statusCode: (error as any).statusCode },
      };
    }

    if (typeof error === 'object') {
      return {
        ...error,
        toString: error.toString?.(),
      };
    }

    return { value: error };
  }

  /**
   * Flush logs (useful for testing or shutdown)
   */
  flush(): void {
    // Winston doesn't have a direct flush method, but we can end all transports
    this.winstonLogger.end?.();
  }

  /**
   * Set log level
   */
  setLevel(level: LogLevel): void {
    this.winstonLogger.level = level;
  }

  /**
   * Get current log level
   */
  getLevel(): string {
    return this.winstonLogger.level;
  }

  /**
   * Check if level is enabled
   */
  isLevelEnabled(level: LogLevel): boolean {
    return this.winstonLogger.isLevelEnabled(level);
  }

  /**
   * Add metadata to all subsequent logs
   */
  addMetadata(metadata: Record<string, any>): void {
    this.winstonLogger.defaultMeta = {
      ...this.winstonLogger.defaultMeta,
      ...metadata,
    };
  }

  /**
   * Remove metadata
   */
  removeMetadata(keys: string[]): void {
    if (this.winstonLogger.defaultMeta) {
      keys.forEach(key => {
        delete this.winstonLogger.defaultMeta[key];
      });
    }
  }

  /**
   * Create structured log entry
   */
  structured(level: LogLevel, message: string, data: Record<string, any>): void {
    this.log(level, message, {
      ...data,
      structured: true,
    });
  }

  /**
   * Log with custom format
   */
  custom(level: LogLevel, format: string, ...args: any[]): void {
    const message = this.formatMessage(format, args);
    this.log(level, message);
  }

  /**
   * Format message with arguments
   */
  private formatMessage(format: string, args: any[]): string {
    return format.replace(/%[sdj%]/g, (match) => {
      const arg = args.shift();
      switch (match) {
        case '%s': return String(arg);
        case '%d': return String(Number(arg));
        case '%j': return JSON.stringify(arg);
        case '%%': return '%';
        default: return match;
      }
    });
  }
}
