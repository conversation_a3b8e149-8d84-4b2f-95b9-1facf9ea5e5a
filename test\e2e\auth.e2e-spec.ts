import { E2ETestUtils, E2ETestDataFactory, E2EResponseValidators } from '../e2e-setup';

describe('Authentication (E2E)', () => {
  let testData: any;

  beforeAll(async () => {
    testData = await E2ETestUtils.seedTestData();
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = E2ETestDataFactory.createUserData({
        email: '<EMAIL>',
      });

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      E2EResponseValidators.validateAuthResponse(response.body);
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.firstName).toBe(userData.firstName);
      expect(response.body.user.lastName).toBe(userData.lastName);
    });

    it('should return 400 for invalid email format', async () => {
      const userData = E2ETestDataFactory.createUserData({
        email: 'invalid-email',
      });

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
      expect(response.body.message).toContain('email');
    });

    it('should return 400 for weak password', async () => {
      const userData = E2ETestDataFactory.createUserData({
        password: '123',
      });

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
      expect(response.body.message).toContain('password');
    });

    it('should return 409 for duplicate email', async () => {
      const userData = E2ETestDataFactory.createUserData({
        email: '<EMAIL>', // Already exists from seed data
      });

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(409);

      E2EResponseValidators.validateErrorResponse(response.body, 409);
      expect(response.body.message).toContain('already exists');
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteData = {
        email: '<EMAIL>',
        // Missing password, firstName, lastName
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/register')
        .send(incompleteData)
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
    });
  });

  describe('POST /auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/login')
        .send(credentials)
        .expect(200);

      E2EResponseValidators.validateAuthResponse(response.body);
      expect(response.body.user.email).toBe(credentials.email);
    });

    it('should return 401 for invalid email', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'SomePassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/login')
        .send(credentials)
        .expect(401);

      E2EResponseValidators.validateErrorResponse(response.body, 401);
      expect(response.body.message).toContain('Invalid credentials');
    });

    it('should return 401 for invalid password', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'WrongPassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/login')
        .send(credentials)
        .expect(401);

      E2EResponseValidators.validateErrorResponse(response.body, 401);
      expect(response.body.message).toContain('Invalid credentials');
    });

    it('should return 400 for missing credentials', async () => {
      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/login')
        .send({})
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
    });

    it('should include user roles in login response', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/login')
        .send(credentials)
        .expect(200);

      expect(response.body.user).toHaveProperty('roles');
      expect(Array.isArray(response.body.user.roles)).toBe(true);
    });
  });

  describe('POST /auth/refresh', () => {
    let refreshToken: string;

    beforeEach(async () => {
      const loginResponse = await E2ETestUtils.loginUser({
        email: '<EMAIL>',
        password: 'AdminPassword123!',
      });
      refreshToken = loginResponse.refreshToken;
    });

    it('should refresh tokens successfully', async () => {
      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body.refreshToken).not.toBe(refreshToken); // Should be new token
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: 'invalid.refresh.token' })
        .expect(401);

      E2EResponseValidators.validateErrorResponse(response.body, 401);
    });

    it('should return 400 for missing refresh token', async () => {
      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/refresh')
        .send({})
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
    });
  });

  describe('POST /auth/logout', () => {
    let authHeaders: any;
    let refreshToken: string;

    beforeEach(async () => {
      const loginResponse = await E2ETestUtils.loginUser({
        email: '<EMAIL>',
        password: 'UserPassword123!',
      });
      authHeaders = { Authorization: `Bearer ${loginResponse.accessToken}` };
      refreshToken = loginResponse.refreshToken;
    });

    it('should logout successfully', async () => {
      await E2ETestUtils.request()
        .post('/api/v1/auth/logout')
        .set(authHeaders)
        .send({ refreshToken })
        .expect(200);
    });

    it('should invalidate refresh token after logout', async () => {
      // Logout
      await E2ETestUtils.request()
        .post('/api/v1/auth/logout')
        .set(authHeaders)
        .send({ refreshToken })
        .expect(200);

      // Try to use refresh token - should fail
      await E2ETestUtils.request()
        .post('/api/v1/auth/refresh')
        .send({ refreshToken })
        .expect(401);
    });

    it('should return 401 for unauthenticated request', async () => {
      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/logout')
        .send({ refreshToken })
        .expect(401);

      E2EResponseValidators.validateErrorResponse(response.body, 401);
    });
  });

  describe('POST /auth/change-password', () => {
    let authHeaders: any;

    beforeEach(async () => {
      authHeaders = await E2ETestUtils.getAuthHeaders(
        '<EMAIL>',
        'UserPassword123!'
      );
    });

    it('should change password successfully', async () => {
      const changePasswordData = {
        currentPassword: 'UserPassword123!',
        newPassword: 'NewUserPassword123!',
      };

      await E2ETestUtils.request()
        .post('/api/v1/auth/change-password')
        .set(authHeaders)
        .send(changePasswordData)
        .expect(200);

      // Verify new password works
      await E2ETestUtils.loginUser({
        email: '<EMAIL>',
        password: 'NewUserPassword123!',
      });
    });

    it('should return 400 for incorrect current password', async () => {
      const changePasswordData = {
        currentPassword: 'WrongPassword123!',
        newPassword: 'NewUserPassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/change-password')
        .set(authHeaders)
        .send(changePasswordData)
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
    });

    it('should return 400 for weak new password', async () => {
      const changePasswordData = {
        currentPassword: 'UserPassword123!',
        newPassword: '123',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/change-password')
        .set(authHeaders)
        .send(changePasswordData)
        .expect(400);

      E2EResponseValidators.validateErrorResponse(response.body, 400);
      expect(response.body.message).toContain('password');
    });

    it('should return 401 for unauthenticated request', async () => {
      const changePasswordData = {
        currentPassword: 'UserPassword123!',
        newPassword: 'NewUserPassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/change-password')
        .send(changePasswordData)
        .expect(401);

      E2EResponseValidators.validateErrorResponse(response.body, 401);
    });
  });

  describe('GET /auth/profile', () => {
    let authHeaders: any;

    beforeEach(async () => {
      authHeaders = await E2ETestUtils.getAuthHeaders(
        '<EMAIL>',
        'UserPassword123!'
      );
    });

    it('should return user profile successfully', async () => {
      const response = await E2ETestUtils.request()
        .get('/api/v1/auth/profile')
        .set(authHeaders)
        .expect(200);

      E2EResponseValidators.validateUserResponse(response.body);
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should return 401 for unauthenticated request', async () => {
      const response = await E2ETestUtils.request()
        .get('/api/v1/auth/profile')
        .expect(401);

      E2EResponseValidators.validateErrorResponse(response.body, 401);
    });

    it('should include user roles in profile', async () => {
      const response = await E2ETestUtils.request()
        .get('/api/v1/auth/profile')
        .set(authHeaders)
        .expect(200);

      expect(response.body).toHaveProperty('roles');
      expect(Array.isArray(response.body.roles)).toBe(true);
    });
  });

  describe('Performance Tests', () => {
    it('should handle login requests within acceptable time', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
      };

      const { responseTime } = await E2ETestUtils.measureResponseTime(() =>
        E2ETestUtils.request()
          .post('/api/v1/auth/login')
          .send(credentials)
          .expect(200)
      );

      expect(responseTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle concurrent login requests', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
      };

      const concurrentRequests = () =>
        E2ETestUtils.request()
          .post('/api/v1/auth/login')
          .send(credentials)
          .expect(200);

      const responses = await E2ETestUtils.runConcurrentRequests(
        concurrentRequests,
        5
      );

      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        E2EResponseValidators.validateAuthResponse(response.body);
      });
    });
  });

  describe('Security Tests', () => {
    it('should rate limit login attempts', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'WrongPassword123!',
      };

      // Make multiple failed login attempts
      const attempts = Array(10).fill(null).map(() =>
        E2ETestUtils.request()
          .post('/api/v1/auth/login')
          .send(credentials)
      );

      const responses = await Promise.allSettled(attempts);
      
      // Some requests should be rate limited (429)
      const rateLimitedResponses = responses.filter(
        (result: any) => result.value?.status === 429
      );
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should not expose sensitive information in error messages', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'SomePassword123!',
      };

      const response = await E2ETestUtils.request()
        .post('/api/v1/auth/login')
        .send(credentials)
        .expect(401);

      // Should not reveal whether email exists or not
      expect(response.body.message).not.toContain('email not found');
      expect(response.body.message).not.toContain('user does not exist');
    });
  });
});
