import{a as n}from"./chunk-RCCYSHWF.js";import{a}from"./chunk-KERBADJJ.js";import{n as e,o as r}from"./chunk-PC2QB7VM.js";var i=["& Co.","AG","GmbH","Gruppe","Inc.","LLC","und Partner","und S\xF6hne"];var t=["{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} und {{person.last_name.generic}}","{{person.last_name.generic}}-{{person.last_name.generic}}"];var P={legal_entity_type:i,name_pattern:t},o=P;var l=["at","biz","ch","com","de","li","net"];var R={domain_suffix:l},s=R;var u=["Aarau","Adliswil","Allschwil","Arbon","Baar","Baden","Basel","Bellinzona","Bern","Biel/Bienne","Binningen","Brig-Glis","Bulle","Burgdorf","B\xFClach","Carouge","Cham","Chur","Dietikon","D\xFCbendorf","Einsiedeln","Emmen","Frauenfeld","Freiburg","Freienbach","Genf","Glarus Nord","Gossau","Grenchen","Herisau","Horgen","Horw","Illnau-Effretikon","Kloten","Kreuzlingen","Kriens","K\xF6niz","K\xFCsnacht","La Chaux-de-Fonds","Lancy","Langenthal","Lausanne","Liestal","Locarno","Lugano","Luzern","Lyss","Martigny","Meilen","Mendrisio","Meyrin","Monthey","Montreux","Morges","Muri bei Bern","Muttenz","Neuenburg","Nyon","Oftringen","Olten","Onex","Opfikon","Ostermundigen","Pratteln","Pully","Rapperswil-Jona","Regensdorf","Reinach","Renens","Rheinfelden","Richterswil","Riehen","Schaffhausen","Schlieren","Schwyz","Siders","Sitten","Solothurn","St. Gallen","Steffisburg","St\xE4fa","Thalwil","Thun","Th\xF4nex","Uster","Val-de-Ruz","Vernier","Versoix","Vevey","Volketswil","Wallisellen","Wettingen","Wetzikon","Wil","Winterthur","Wohlen","W\xE4denswil","Yverdon-les-Bains","Zug","Z\xFCrich"];var h=["{{location.city_name}}"];var d=[{alpha2:"CH",alpha3:"CHE",numeric:"756"},{alpha2:"DE",alpha3:"DEU",numeric:"276"},{alpha2:"AT",alpha3:"AUT",numeric:"040"}];var m=["1###","2###","3###","4###","5###","6###","7###","8###","9###"];var c=["Aargau","Appenzell Ausserrhoden","Appenzell Innerrhoden","Basel-Land","Basel-Stadt","Bern","Freiburg","Genf","Glarus","Graub\xFCnden","Jura","Luzern","Neuenburg","Nidwalden","Obwalden","St. Gallen","Schaffhausen","Schwyz","Solothurn","Tessin","Thurgau","Uri","Waadt","Wallis","Zug","Z\xFCrich"];var f=["AG","AR","AI","BL","BS","BE","FR","GE","GL","GR","JU","LU","NE","NW","OW","SG","SH","SZ","SO","TI","TG","UR","VD","VS","ZG","ZH"];var p=["Amthausstrasse","Augustinergasse","Bahnhofstrasse","Birkenweg","Bierkellerweg","Columbusstrasse","Dorfstrasse","Elefantenbach","Endingerstrasse","Glockengasse","Hauptstrasse","Hirschengraben","Honiggasse","Industriestrasse","Katzenplatz","Kirchweg","Knoblauchweg","Lindenhofweg","Melonenstrasse","Oberdorfstrasse","\xD6dhus","Ogimatte","R\xE4mistrasse","Rennweg","Rosenweg","Schulhausstrasse","Schulstrasse","Sihlfeldstrasse","Trittligasse","Uraniastrasse","Vorstadt"];var g=["{{location.street_name}}"];var k={city_name:u,city_pattern:h,country_code:d,postcode:m,state:c,state_abbr:f,street_name:p,street_pattern:g},S=k;var x={title:"German (Switzerland)",code:"de_CH",country:"CH",language:"de",endonym:"Deutsch (Schweiz)",dir:"ltr",script:"Latn"},M=x;var H={generic:["Alfons","Alfred","Alice","Alois","Andrea","Andreas","Andr\xE9","Angela","Angelo","Anita","Anna","Anne","Anne-Marie","Annemarie","Antoine","Anton","Antonio","Armin","Arnold","Arthur","Astrid","Barbara","Beat","Beatrice","Beatrix","Bernadette","Bernard","Bernhard","Bettina","Brigitta","Brigitte","Bruno","Carlo","Carmen","Caroline","Catherine","Chantal","Charles","Charlotte","Christa","Christian","Christiane","Christina","Christine","Christoph","Christophe","Claire","Claude","Claudia","Claudine","Claudio","Corinne","Cornelia","Daniel","Daniela","Daniele","Danielle","David","Denis","Denise","Didier","Dieter","Dominik","Dominique","Dora","Doris","Edgar","Edith","Eduard","Edwin","Eliane","Elisabeth","Elsa","Elsbeth","Emil","Enrico","Eric","Erica","Erich","Erika","Ernst","Erwin","Esther","Eugen","Eva","Eveline","Evelyne","Fabienne","Felix","Ferdinand","Florence","Francesco","Francis","Franco","Frank","Franz","Franziska","Fran\xE7ois","Fran\xE7oise","Fredy","Fridolin","Friedrich","Fritz","Fr\xE9d\xE9ric","Gabriel","Gabriela","Gabrielle","Georg","Georges","Gerhard","Gertrud","Gianni","Gilbert","Giorgio","Giovanni","Gisela","Giuseppe","Gottfried","Guido","Guy","G\xE9rald","G\xE9rard","Hanna","Hans","Hans-Peter","Hans-Rudolf","Hans-Ulrich","Hansj\xF6rg","Hanspeter","Hansruedi","Hansueli","Harry","Heidi","Heinrich","Heinz","Helen","Helena","Helene","Helmut","Henri","Herbert","Hermann","Hildegard","Hubert","Hugo","Ingrid","Irene","Iris","Isabelle","Jacqueline","Jacques","Jakob","Jan","Janine","Jean","Jean-Claude","Jean-Daniel","Jean-Fran\xE7ois","Jean-Jacques","Jean-Louis","Jean-Luc","Jean-Marc","Jean-Marie","Jean-Paul","Jean-Pierre","Johann","Johanna","Johannes","John","Jolanda","Josef","Joseph","Josette","Josiane","Judith","Julia","J\xF6rg","J\xFCrg","Karin","Karl","Katharina","Klaus","Konrad","Kurt","Laura","Laurence","Laurent","Leo","Liliane","Liselotte","Louis","Luca","Luigi","Lukas","Lydia","Madeleine","Maja","Manfred","Manuel","Manuela","Marc","Marcel","Marco","Margrit","Margrith","Maria","Marianne","Mario","Marion","Markus","Marlies","Marlis","Marl\xE8ne","Martha","Martin","Martina","Martine","Massimo","Matthias","Maurice","Max","Maya","Michael","Michel","Michele","Micheline","Monica","Monika","Monique","Myriam","Nadia","Nadja","Nathalie","Nelly","Nicolas","Nicole","Niklaus","Norbert","Olivier","Oskar","Otto","Paola","Paolo","Pascal","Patricia","Patrick","Paul","Peter","Petra","Philipp","Philippe","Pia","Pierre","Pierre-Alain","Pierre-Andr\xE9","Pius","Priska","Rainer","Raymond","Regina","Regula","Reinhard","Remo","Renata","Renate","Renato","Rene","Ren\xE9","Reto","Richard","Rudolf","Ruedi","Ruth","Sabine","Samuel","Sandra","Sandro","Serge","Silvia","Silvio","Simon","Simone","Sonia","Sonja","Stefan","Stephan","St\xE9phane","St\xE9phanie","Susanna","Susanne","Suzanne","Sylvia","Sylvie","Theo","Theodor","Therese","Thomas","Toni","Ueli","Ulrich","Urs","Ursula","Verena","Victor","Viktor","Vreni","V\xE9ronique","Walter","Werner","Willi","Willy","Wolfgang","Yolande","Yves","Yvette","Yvonne"],female:["Alice","Andrea","Angela","Anita","Anna","Anne","Anne-Marie","Annemarie","Astrid","Barbara","Beatrice","Beatrix","Bernadette","Bettina","Brigitta","Brigitte","Carmen","Caroline","Catherine","Chantal","Charlotte","Christa","Christiane","Christina","Christine","Claire","Claudia","Claudine","Corinne","Cornelia","Daniela","Danielle","Denise","Dominique","Dora","Doris","Edith","Eliane","Elisabeth","Elsa","Elsbeth","Erica","Erika","Esther","Eva","Eveline","Evelyne","Fabienne","Florence","Franziska","Fran\xE7oise","Gabriela","Gabrielle","Gertrud","Gisela","Hanna","Heidi","Helen","Helena","Helene","Hildegard","Ingrid","Irene","Iris","Isabelle","Jacqueline","Janine","Jean","Johanna","Jolanda","Josette","Josiane","Judith","Julia","Karin","Katharina","Laura","Laurence","Liliane","Liselotte","Lydia","Madeleine","Maja","Manuela","Margrit","Margrith","Maria","Marianne","Marion","Marlies","Marlis","Marl\xE8ne","Martha","Martina","Martine","Maya","Michele","Micheline","Monica","Monika","Monique","Myriam","Nadia","Nadja","Nathalie","Nelly","Nicole","Paola","Patricia","Petra","Pia","Priska","Regina","Regula","Renata","Renate","Ruth","Sabine","Sandra","Silvia","Simone","Sonia","Sonja","St\xE9phanie","Susanna","Susanne","Suzanne","Sylvia","Sylvie","Therese","Toni","Ursula","Verena","Vreni","V\xE9ronique","Yolande","Yvette","Yvonne"],male:["Alfons","Alfred","Alois","Andreas","Andr\xE9","Angelo","Antoine","Anton","Antonio","Armin","Arnold","Arthur","Beat","Bernard","Bernhard","Bruno","Carlo","Charles","Christian","Christoph","Christophe","Claude","Claudio","Daniel","Daniele","David","Denis","Didier","Dieter","Dominik","Dominique","Edgar","Eduard","Edwin","Emil","Enrico","Eric","Erich","Ernst","Erwin","Eugen","Felix","Ferdinand","Francesco","Francis","Franco","Frank","Franz","Fran\xE7ois","Fredy","Fridolin","Friedrich","Fritz","Fr\xE9d\xE9ric","Gabriel","Georg","Georges","Gerhard","Gianni","Gilbert","Giorgio","Giovanni","Giuseppe","Gottfried","Guido","Guy","G\xE9rald","G\xE9rard","Hans","Hans-Peter","Hans-Rudolf","Hans-Ulrich","Hansj\xF6rg","Hanspeter","Hansruedi","Hansueli","Harry","Heinrich","Heinz","Helmut","Henri","Herbert","Hermann","Hubert","Hugo","Jacques","Jakob","Jan","Jean-Claude","Jean-Daniel","Jean-Fran\xE7ois","Jean-Jacques","Jean-Louis","Jean-Luc","Jean-Marc","Jean-Marie","Jean-Paul","Jean-Pierre","Johann","Johannes","John","Josef","Joseph","J\xF6rg","J\xFCrg","Karl","Klaus","Konrad","Kurt","Laurent","Leo","Louis","Luca","Luigi","Lukas","Manfred","Manuel","Marc","Marcel","Marco","Mario","Markus","Martin","Massimo","Matthias","Maurice","Max","Michael","Michel","Nicolas","Niklaus","Norbert","Olivier","Oskar","Otto","Paolo","Pascal","Patrick","Paul","Peter","Philipp","Philippe","Pierre","Pierre-Alain","Pierre-Andr\xE9","Pius","Rainer","Raymond","Reinhard","Remo","Renato","Rene","Ren\xE9","Reto","Richard","Rudolf","Ruedi","Samuel","Sandro","Serge","Silvio","Simon","Stefan","Stephan","St\xE9phane","Theo","Theodor","Thomas","Ueli","Ulrich","Urs","Victor","Viktor","Walter","Werner","Willi","Willy","Wolfgang","Yves"]};var B={generic:["Ackermann","Aebi","Albrecht","Ammann","Amrein","Arnold","Bachmann","Bader","Bauer","Baumann","Baumgartner","Baur","Beck","Benz","Berger","Bernasconi","Betschart","Bianchi","Bieri","Blaser","Blum","Bolliger","Bosshard","Braun","Brun","Brunner","Bucher","Burri","B\xE4r","B\xE4ttig","B\xFChler","B\xFChlmann","Christen","Egger","Egli","Eichenberger","Erni","Ernst","Eugster","Fankhauser","Favre","Fehr","Felber","Felder","Ferrari","Fischer","Fl\xFCckiger","Forster","Frei","Frey","Frick","Friedli","Fuchs","Furrer","Gasser","Geiger","Gerber","Gfeller","Giger","Gloor","Graf","Grob","Gross","Gut","Haas","Hafner","Hartmann","Hasler","Hauser","Hermann","Herzog","Hess","Hirt","Hodel","Hofer","Hoffmann","Hofmann","Hofstetter","Hotz","Huber","Hug","Hunziker","H\xE4fliger","H\xFCrlimann","Imhof","Isler","Iten","Jenni","Jost","J\xE4ggi","Kaiser","Kaufmann","Keller","Kern","Kessler","Knecht","Koch","Kohler","Kuhn","Kunz","K\xE4gi","K\xE4lin","K\xE4ser","K\xFCng","Lang","Lanz","Lehmann","Leu","Leunberger","Lustenberger","Lutz","L\xFCscher","L\xFCthi","Maier","Marti","Martin","Maurer","Mayer","Meier","Meili","Meister","Merz","Mettler","Meyer","Michel","Moser","M\xE4der","M\xFCller","N\xE4f","Ott","Peter","Pfister","Portmann","Probst","Rey","Ritter","Roos","Roth","R\xFCegg","Schaller","Schaub","Scheidegger","Schenk","Scherrer","Schlatter","Schmid","Schmidt","Schneider","Schnyder","Schoch","Schuler","Schumacher","Schwab","Schwarz","Schweizer","Sch\xE4fer","Sch\xE4r","Sch\xE4rer","Sch\xFCrch","Seiler","Senn","Sidler","Siegrist","Sigrist","Sp\xF6rri","Stadelmann","Stalder","Staub","Stauffer","Steffen","Steiger","Steiner","Steinmann","Stettler","Stocker","Stucki","Studer","Stutz","St\xF6ckli","Suter","Sutter","Tanner","Thommen","Tobler","Vogel","Vogt","Wagner","Walder","Walter","Weber","Wegmann","Wehrli","Weibel","Wenger","Wettstein","Widmer","Winkler","Wirth","Wirz","Wolf","Wyss","W\xFCthrich","Zbinden","Zehnder","Ziegler","Zimmermann","Zingg","Zollinger","Z\xFCrcher"]};var b={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var A=[{value:"{{person.firstName}} {{person.lastName}}",weight:1}];var G={generic:["Dr.","Frau","Herr","Prof. Dr."],female:["Dr.","Frau","Prof. Dr."],male:["Dr.","Herr","Prof. Dr."]};var z={first_name:H,last_name:B,last_name_pattern:b,name:A,prefix:G},J=z;var y=["0800 ### ###","0800 ## ## ##","0## ### ## ##","+41 ## ### ## ##","0900 ### ###","076 ### ## ##","+4178 ### ## ##","0041 79 ### ## ##"];var C=["+41800######","+41#########","+41900######","+4176#######","+4178#######","+4179#######"];var F=["0800 ### ###","0## ### ## ##","0900 ### ###","076 ### ## ##","078 ### ## ##","079 ### ## ##"];var W={human:y,international:C,national:F},E=W;var K={format:E},D=K;var v={company:o,internet:s,location:S,metadata:M,person:J,phone_number:D},L=v;var Ve=new e({locale:[L,n,a,r]});export{L as a,Ve as b};
