import {
  HttpException,
  HttpStatus,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { QueryFailedError } from 'typeorm';

/**
 * Custom error types for better error handling
 */
export enum ErrorCode {
  // Authentication & Authorization
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  
  // Validation
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  
  // Business Logic
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  
  // Database
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  FOREIGN_KEY_VIOLATION = 'FOREIGN_KEY_VIOLATION',
  
  // External Services
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // System
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Tenant & Multi-tenancy
  TENANT_NOT_FOUND = 'TENANT_NOT_FOUND',
  TENANT_DISABLED = 'TENANT_DISABLED',
  CROSS_TENANT_ACCESS = 'CROSS_TENANT_ACCESS',
}

/**
 * Custom application error class
 */
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: any;
  public readonly correlationId?: string;

  constructor(
    message: string,
    code: ErrorCode,
    statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR,
    isOperational: boolean = true,
    context?: any,
    correlationId?: string,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;
    this.correlationId = correlationId;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error handling utility class
 */
export class ErrorHandler {
  private static readonly logger = new Logger(ErrorHandler.name);

  /**
   * Convert various error types to HTTP exceptions
   */
  static handleError(error: any, correlationId?: string): HttpException {
    // If it's already an HTTP exception, return as is
    if (error instanceof HttpException) {
      return error;
    }

    // Handle custom app errors
    if (error instanceof AppError) {
      return this.createHttpException(
        error.message,
        error.statusCode,
        error.code,
        error.context,
        correlationId,
      );
    }

    // Handle database errors
    if (error instanceof QueryFailedError) {
      return this.handleDatabaseError(error, correlationId);
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return new BadRequestException({
        message: 'Validation failed',
        code: ErrorCode.VALIDATION_FAILED,
        errors: error.errors || [error.message],
        correlationId,
      });
    }

    // Handle JWT errors
    if (error.name === 'JsonWebTokenError') {
      return new UnauthorizedException({
        message: 'Invalid token',
        code: ErrorCode.TOKEN_INVALID,
        correlationId,
      });
    }

    if (error.name === 'TokenExpiredError') {
      return new UnauthorizedException({
        message: 'Token expired',
        code: ErrorCode.TOKEN_EXPIRED,
        correlationId,
      });
    }

    // Handle timeout errors
    if (error.name === 'TimeoutError' || error.code === 'ETIMEDOUT') {
      return new InternalServerErrorException({
        message: 'Request timeout',
        code: ErrorCode.TIMEOUT_ERROR,
        correlationId,
      });
    }

    // Handle network errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new InternalServerErrorException({
        message: 'Network error',
        code: ErrorCode.NETWORK_ERROR,
        correlationId,
      });
    }

    // Default to internal server error
    this.logger.error('Unhandled error', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      correlationId,
    });

    return new InternalServerErrorException({
      message: process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error.message,
      code: ErrorCode.INTERNAL_ERROR,
      correlationId,
    });
  }

  /**
   * Handle database-specific errors
   */
  private static handleDatabaseError(error: QueryFailedError, correlationId?: string): HttpException {
    const { code, detail, constraint } = error.driverError as any;

    switch (code) {
      case '23505': // Unique violation
        return new ConflictException({
          message: 'Resource already exists',
          code: ErrorCode.DUPLICATE_ENTRY,
          detail: this.sanitizeDatabaseError(detail),
          correlationId,
        });

      case '23503': // Foreign key violation
        return new BadRequestException({
          message: 'Referenced resource does not exist',
          code: ErrorCode.FOREIGN_KEY_VIOLATION,
          detail: this.sanitizeDatabaseError(detail),
          correlationId,
        });

      case '23502': // Not null violation
        return new BadRequestException({
          message: 'Required field is missing',
          code: ErrorCode.MISSING_REQUIRED_FIELD,
          detail: this.sanitizeDatabaseError(detail),
          correlationId,
        });

      case '23514': // Check constraint violation
        return new BadRequestException({
          message: 'Invalid data format or value',
          code: ErrorCode.CONSTRAINT_VIOLATION,
          detail: this.sanitizeDatabaseError(detail),
          correlationId,
        });

      default:
        this.logger.error('Database error', {
          code,
          detail,
          constraint,
          correlationId,
        });

        return new InternalServerErrorException({
          message: 'Database operation failed',
          code: ErrorCode.DATABASE_ERROR,
          correlationId,
        });
    }
  }

  /**
   * Create HTTP exception with consistent format
   */
  private static createHttpException(
    message: string,
    statusCode: number,
    code: ErrorCode,
    context?: any,
    correlationId?: string,
  ): HttpException {
    const response = {
      message,
      code,
      correlationId,
      ...(context && { context }),
    };

    switch (statusCode) {
      case HttpStatus.BAD_REQUEST:
        return new BadRequestException(response);
      case HttpStatus.UNAUTHORIZED:
        return new UnauthorizedException(response);
      case HttpStatus.FORBIDDEN:
        return new ForbiddenException(response);
      case HttpStatus.NOT_FOUND:
        return new NotFoundException(response);
      case HttpStatus.CONFLICT:
        return new ConflictException(response);
      default:
        return new InternalServerErrorException(response);
    }
  }

  /**
   * Sanitize database error messages to avoid exposing sensitive information
   */
  private static sanitizeDatabaseError(detail: string): string {
    if (!detail) return 'Database constraint violation';

    // Remove sensitive information like table names, column names, etc.
    return detail
      .replace(/Key \([^)]+\)/g, 'Key')
      .replace(/relation "[^"]+"/g, 'table')
      .replace(/column "[^"]+"/g, 'field')
      .replace(/constraint "[^"]+"/g, 'constraint');
  }

  /**
   * Create business logic error
   */
  static createBusinessError(
    message: string,
    code: ErrorCode = ErrorCode.BUSINESS_RULE_VIOLATION,
    context?: any,
    correlationId?: string,
  ): AppError {
    return new AppError(
      message,
      code,
      HttpStatus.BAD_REQUEST,
      true,
      context,
      correlationId,
    );
  }

  /**
   * Create not found error
   */
  static createNotFoundError(
    resource: string,
    identifier?: string,
    correlationId?: string,
  ): AppError {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;

    return new AppError(
      message,
      ErrorCode.RESOURCE_NOT_FOUND,
      HttpStatus.NOT_FOUND,
      true,
      { resource, identifier },
      correlationId,
    );
  }

  /**
   * Create unauthorized error
   */
  static createUnauthorizedError(
    message: string = 'Unauthorized access',
    code: ErrorCode = ErrorCode.INSUFFICIENT_PERMISSIONS,
    correlationId?: string,
  ): AppError {
    return new AppError(
      message,
      code,
      HttpStatus.UNAUTHORIZED,
      true,
      undefined,
      correlationId,
    );
  }

  /**
   * Create forbidden error
   */
  static createForbiddenError(
    message: string = 'Access forbidden',
    code: ErrorCode = ErrorCode.INSUFFICIENT_PERMISSIONS,
    correlationId?: string,
  ): AppError {
    return new AppError(
      message,
      code,
      HttpStatus.FORBIDDEN,
      true,
      undefined,
      correlationId,
    );
  }

  /**
   * Create validation error
   */
  static createValidationError(
    message: string,
    errors: string[] = [],
    correlationId?: string,
  ): AppError {
    return new AppError(
      message,
      ErrorCode.VALIDATION_FAILED,
      HttpStatus.BAD_REQUEST,
      true,
      { errors },
      correlationId,
    );
  }

  /**
   * Check if error is operational (expected) or programming error
   */
  static isOperationalError(error: any): boolean {
    if (error instanceof AppError) {
      return error.isOperational;
    }

    if (error instanceof HttpException) {
      return true; // HTTP exceptions are considered operational
    }

    return false;
  }
}
