import 'reflect-metadata';
import { config } from 'dotenv';

// Load environment variables for testing
config({ path: '.env.test' });

// Global test configuration
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  
  // Set default timezone for consistent date testing
  process.env.TZ = 'UTC';
  
  // Increase timeout for async operations
  jest.setTimeout(30000);
});

// Global test cleanup
afterAll(async () => {
  // Clean up any global resources
  await new Promise(resolve => setTimeout(resolve, 100));
});

// Mock console methods in test environment to reduce noise
const originalConsole = global.console;

beforeEach(() => {
  // Restore console for each test
  global.console = originalConsole;
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Mock Date.now for consistent testing
const mockDateNow = jest.fn(() => new Date('2024-01-01T00:00:00.000Z').getTime());
Date.now = mockDateNow;

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidUUID(): R;
      toBeValidEmail(): R;
      toBeValidPhoneNumber(): R;
      toBeValidDate(): R;
      toBeWithinRange(min: number, max: number): R;
    }
  }
}

// Custom Jest matchers
expect.extend({
  toBeValidUUID(received: string) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },
  
  toBeValidEmail(received: string) {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    const pass = emailRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      };
    }
  },
  
  toBeValidPhoneNumber(received: string) {
    const phoneRegex = /^\+[1-9]\d{6,14}$/;
    const cleanPhone = received.replace(/[\s\-\(\)]/g, '');
    const pass = phoneRegex.test(cleanPhone);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid phone number`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid phone number`,
        pass: false,
      };
    }
  },
  
  toBeValidDate(received: string | Date) {
    const date = new Date(received);
    const pass = !isNaN(date.getTime());
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date`,
        pass: false,
      };
    }
  },
  
  toBeWithinRange(received: number, min: number, max: number) {
    const pass = received >= min && received <= max;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${min}-${max}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${min}-${max}`,
        pass: false,
      };
    }
  },
});

// Mock external dependencies
jest.mock('winston', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    log: jest.fn(),
  })),
  format: {
    combine: jest.fn(),
    timestamp: jest.fn(),
    errors: jest.fn(),
    json: jest.fn(),
    printf: jest.fn(),
    colorize: jest.fn(),
    simple: jest.fn(),
  },
  transports: {
    Console: jest.fn(),
    File: jest.fn(),
  },
}));

// Mock Redis for testing
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    flushall: jest.fn(),
    on: jest.fn(),
  })),
}));

// Mock bcrypt for faster tests
jest.mock('bcrypt', () => ({
  hash: jest.fn((password: string) => Promise.resolve(`hashed_${password}`)),
  compare: jest.fn((password: string, hash: string) => 
    Promise.resolve(hash === `hashed_${password}`)
  ),
  genSalt: jest.fn(() => Promise.resolve('salt')),
}));

// Mock JWT for testing
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn((payload: any) => `jwt_token_${JSON.stringify(payload)}`),
  verify: jest.fn((token: string) => {
    if (token.startsWith('jwt_token_')) {
      return JSON.parse(token.replace('jwt_token_', ''));
    }
    throw new Error('Invalid token');
  }),
  decode: jest.fn((token: string) => {
    if (token.startsWith('jwt_token_')) {
      return JSON.parse(token.replace('jwt_token_', ''));
    }
    return null;
  }),
}));

// Test database configuration
export const testDatabaseConfig = {
  type: 'postgres' as const,
  host: process.env.TEST_DB_HOST || 'localhost',
  port: parseInt(process.env.TEST_DB_PORT || '5433'),
  username: process.env.TEST_DB_USERNAME || 'test_user',
  password: process.env.TEST_DB_PASSWORD || 'test_password',
  database: process.env.TEST_DB_NAME || 'peoplenest_test',
  synchronize: true,
  dropSchema: true,
  logging: false,
  entities: ['src/**/*.entity.ts'],
  migrations: ['src/database/migrations/*.ts'],
};

// Test utilities
export class TestUtils {
  static generateMockUser(overrides: Partial<any> = {}) {
    return {
      id: 'user-123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      isActive: true,
      tenantId: 'tenant-123',
      roles: ['user'],
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }
  
  static generateMockTenant(overrides: Partial<any> = {}) {
    return {
      id: 'tenant-123',
      name: 'Test Company',
      domain: 'test.com',
      isActive: true,
      settings: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }
  
  static generateMockJwtPayload(overrides: Partial<any> = {}) {
    return {
      sub: 'user-123',
      email: '<EMAIL>',
      tenantId: 'tenant-123',
      roles: ['user'],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
      ...overrides,
    };
  }
  
  static async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  static mockConsole() {
    return {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
    };
  }
  
  static restoreConsole() {
    global.console = originalConsole;
  }
}
